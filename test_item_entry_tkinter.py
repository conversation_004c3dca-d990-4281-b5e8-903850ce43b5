# -*- coding: utf-8 -*-
"""
اختبار نافذة إدخال الأصناف - إصدار Tkinter
Test Item Entry Window - Tkinter Version
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from windows.item_entry_tkinter import ItemEntryTkinter
    
    def test_item_entry_tkinter():
        """اختبار نافذة إدخال الأصناف"""
        print("🚀 بدء اختبار نافذة إدخال الأصناف...")
        print("📋 المكونات المتاحة:")
        print("   ✅ نموذج إدخال البيانات الأساسية")
        print("   ✅ قسم الأسعار والتكلفة مع حساب الربح")
        print("   ✅ تحميل وعرض الصور")
        print("   ✅ التحقق من صحة البيانات")
        print("   ✅ توليد رموز الأصناف تلقائياً")
        print("   ✅ حفظ واسترجاع البيانات")
        print("   ✅ واجهة عربية متكاملة")
        print("   ✅ دعم التمرير للنماذج الطويلة")
        print("   🆕 ملء الشاشة مع قابلية التحكم")
        print("   🆕 أزرار تصغير وتكبير")
        print("   🆕 اختصارات لوحة المفاتيح")
        print("")
        print("🎯 فتح النافذة بملء الشاشة...")
        print("⌨️  اختصارات التحكم:")
        print("   F11/Escape: تبديل ملء الشاشة")
        print("   Ctrl+S: حفظ سريع")
        print("   Ctrl+Q: إغلاق")
        print("")

        # إنشاء وعرض النافذة
        app = ItemEntryTkinter()
        app.show()

        print("✅ تم إغلاق النافذة بنجاح")
    
    if __name__ == "__main__":
        test_item_entry_tkinter()

except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من وجود الملفات المطلوبة:")
    print("- windows/item_entry_tkinter.py")
    print("- core/inventory_manager.py")
    print("- core/item_code_generator.py")
    print("- core/validation_engine.py")
    print("- models/item_model.py")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
