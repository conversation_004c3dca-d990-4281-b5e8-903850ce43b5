{"app": {"name": "Sit Al-Kol Accounting System", "version": "1.0.0", "description": "Comprehensive accounting system for small and medium enterprises"}, "menu": {"file": "File", "edit": "Edit", "view": "View", "tools": "Tools", "help": "Help", "sales": "Sales", "purchases": "Purchases", "inventory": "Inventory", "accounts": "Accounts", "reports": "Reports", "settings": "Settings"}, "buttons": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "print": "Print", "export": "Export", "import": "Import", "refresh": "Refresh", "close": "Close", "ok": "OK", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "apply": "Apply", "reset": "Reset"}, "labels": {"name": "Name", "description": "Description", "date": "Date", "time": "Time", "amount": "Amount", "quantity": "Quantity", "price": "Price", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "customer": "Customer", "supplier": "Supplier", "product": "Product", "category": "Category", "code": "Code", "barcode": "Barcode", "unit": "Unit", "status": "Status", "notes": "Notes", "address": "Address", "phone": "Phone", "email": "Email", "tax_number": "Tax Number"}, "sales": {"title": "Sales", "new_invoice": "New Invoice", "invoice_number": "Invoice Number", "customer_name": "Customer Name", "invoice_date": "Invoice Date", "due_date": "Due Date", "payment_method": "Payment Method", "cash": "Cash", "credit": "Credit", "bank_transfer": "Bank Transfer", "credit_card": "Credit Card", "check": "Check", "invoice_items": "Invoice Items", "add_item": "Add Item", "remove_item": "Remove Item", "unit_price": "Unit Price", "line_total": "Line Total", "invoice_total": "Invoice Total", "tax_amount": "Tax Amount", "discount_amount": "Discount Amount", "net_amount": "Net Amount", "paid_amount": "<PERSON><PERSON>", "remaining_amount": "Remaining Amount", "payment_status": "Payment Status", "draft": "Draft", "confirmed": "Confirmed", "paid": "Paid", "partially_paid": "Partially Paid", "cancelled": "Cancelled"}, "purchases": {"title": "Purchases", "new_purchase": "New Purchase", "purchase_number": "Purchase Number", "supplier_name": "Supplier Name", "purchase_date": "Purchase Date", "delivery_date": "Delivery Date", "purchase_items": "Purchase Items", "cost_price": "Cost Price", "purchase_total": "Purchase Total"}, "inventory": {"title": "Inventory", "product_name": "Product Name", "product_code": "Product Code", "current_stock": "Current Stock", "min_stock": "Minimum Stock", "max_stock": "Maximum Stock", "reorder_level": "Reorder Level", "cost_price": "Cost Price", "selling_price": "Selling <PERSON>", "profit_margin": "<PERSON><PERSON>", "stock_value": "Stock Value", "low_stock": "Low Stock", "out_of_stock": "Out of Stock", "stock_movement": "Stock Movement", "stock_in": "Stock In", "stock_out": "Stock Out", "stock_adjustment": "Stock Adjustment"}, "accounts": {"title": "Accounts", "chart_of_accounts": "Chart of Accounts", "account_code": "Account Code", "account_name": "Account Name", "account_type": "Account Type", "parent_account": "Parent Account", "account_balance": "Account <PERSON><PERSON>", "debit": "Debit", "credit": "Credit", "journal_entries": "Journal Entries", "entry_number": "Entry Number", "entry_date": "Entry Date", "entry_description": "Entry Description", "debit_amount": "Debit Amount", "credit_amount": "Credit Amount", "trial_balance": "Trial Balance", "general_ledger": "General <PERSON><PERSON>", "account_statement": "Account Statement"}, "reports": {"title": "Reports", "financial_reports": "Financial Reports", "income_statement": "Income Statement", "balance_sheet": "Balance Sheet", "cash_flow": "Cash Flow Statement", "trial_balance": "Trial Balance", "sales_reports": "Sales Reports", "sales_summary": "Sales Summary", "sales_by_customer": "Sales by Customer", "sales_by_product": "Sales by Product", "sales_by_period": "Sales by Period", "purchase_reports": "Purchase Reports", "purchase_summary": "Purchase Summary", "purchase_by_supplier": "Purchase by Supplier", "inventory_reports": "Inventory Reports", "stock_report": "Stock Report", "stock_valuation": "Stock Valuation", "stock_movement": "Stock Movement", "aging_reports": "Aging Reports", "customer_aging": "Customer Aging", "supplier_aging": "Supplier Aging"}, "settings": {"title": "Settings", "general_settings": "General Settings", "company_info": "Company Information", "user_settings": "User Settings", "system_settings": "System Settings", "backup_settings": "Backup Settings", "security_settings": "Security Settings", "print_settings": "Print Settings", "language": "Language", "theme": "Theme", "font_size": "Font Size", "currency": "<PERSON><PERSON><PERSON><PERSON>", "tax_rate": "Tax Rate", "fiscal_year": "Fiscal Year", "auto_backup": "Auto Backup", "backup_interval": "Backup Interval"}, "messages": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "confirm_delete": "Are you sure you want to delete?", "confirm_save": "Do you want to save changes?", "data_saved": "Data saved successfully", "data_deleted": "Data deleted successfully", "invalid_data": "Invalid data", "required_field": "This field is required", "duplicate_entry": "This entry already exists", "operation_completed": "Operation completed successfully", "operation_failed": "Operation failed", "no_data_found": "No data found", "loading": "Loading...", "processing": "Processing...", "please_wait": "Please wait...", "connection_error": "Connection error", "permission_denied": "Permission denied", "session_expired": "Session expired", "login_required": "Login required"}, "validation": {"required": "This field is required", "invalid_email": "Invalid email address", "invalid_phone": "Invalid phone number", "invalid_number": "Invalid number", "invalid_date": "Invalid date", "min_length": "Minimum length is {min} characters", "max_length": "Maximum length is {max} characters", "min_value": "Minimum value is {min}", "max_value": "Maximum value is {max}", "positive_number": "Number must be positive", "unique_value": "This value already exists"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "draft": "Draft", "published": "Published"}}