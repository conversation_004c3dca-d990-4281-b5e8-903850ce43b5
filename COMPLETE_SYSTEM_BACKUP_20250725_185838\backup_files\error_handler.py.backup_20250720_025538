# -*- coding: utf-8 -*-
"""
نظام معالجة الأخطاء المحسن للبرنامج المحاسبي
Enhanced Error Handling System for Accounting Software
"""

import sys
import logging
import traceback
from datetime import datetime
from pathlib import Path
from typing import Optional, Any, Dict
import customtkinter as ctk
from tkinter import messagebox


class ErrorHandler:
    """معالج الأخطاء المحسن مع دعم اللغة العربية"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self.setup_logging()
        self.error_count = 0
        self.max_errors = 50  # الحد الأقصى للأخطاء قبل إيقاف البرنامج
        
    def setup_logging(self):
        """إعداد نظام تسجيل الأخطاء"""
        log_file = self.log_dir / f"app_errors_{datetime.now().strftime('%Y%m%d')}.log"
        
        # إعداد التنسيق
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # معالج الملف
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.ERROR)
        file_handler.setFormatter(formatter)
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        console_handler.setFormatter(formatter)
        
        # إعداد المسجل الرئيسي
        self.logger = logging.getLogger('AccountingApp')
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
    def handle_exception(self, context: str, exception: Exception, 
                        show_user: bool = True, critical: bool = False) -> None:
        """معالجة الاستثناءات مع خيارات متقدمة"""
        self.error_count += 1
        
        # تسجيل الخطأ
        error_info = {
            'context': context,
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'traceback': traceback.format_exc(),
            'error_count': self.error_count
        }
        
        log_message = f"{context}: {error_info['exception_type']} - {error_info['exception_message']}"
        
        if critical:
            self.logger.critical(log_message, exc_info=True)
        else:
            self.logger.error(log_message, exc_info=True)
            
        # عرض رسالة للمستخدم إذا كان مطلوباً
        if show_user:
            self.show_error_dialog(context, exception, critical)
            
        # التحقق من تجاوز الحد الأقصى للأخطاء
        if self.error_count >= self.max_errors:
            self.handle_critical_failure()
            
    def show_error_dialog(self, context: str, exception: Exception, critical: bool = False):
        """عرض نافذة خطأ للمستخدم"""
        try:
            if critical:
                title = "خطأ حرج في البرنامج"
                message = f"حدث خطأ حرج يتطلب إعادة تشغيل البرنامج:\n\n{context}"
                icon = "error"
            else:
                title = "تحذير"
                message = f"حدث خطأ أثناء العملية:\n\n{context}\n\nسيتم المتابعة بأمان."
                icon = "warning"
                
            # استخدام messagebox بدلاً من CTk للأخطاء الحرجة
            if critical:
                messagebox.showerror(title, message)
            else:
                messagebox.showwarning(title, message)
                
        except Exception as e:
            # في حالة فشل عرض النافذة، اطبع في وحدة التحكم
            print(f"فشل في عرض نافذة الخطأ: {e}")
            print(f"الخطأ الأصلي: {context} - {exception}")
            
    def handle_critical_failure(self):
        """معالجة الفشل الحرج للبرنامج"""
        critical_message = f"تم تجاوز الحد الأقصى للأخطاء ({self.max_errors}). سيتم إغلاق البرنامج لحماية البيانات."
        
        self.logger.critical(critical_message)
        messagebox.showerror("فشل حرج", critical_message)
        
        # إغلاق البرنامج بأمان
        sys.exit(1)
        
    def log_info(self, message: str):
        """تسجيل معلومات عامة"""
        self.logger.info(message)
        
    def log_warning(self, message: str):
        """تسجيل تحذير"""
        self.logger.warning(message)
        
    def safe_execute(self, func, *args, context: str = "عملية غير محددة", **kwargs) -> Any:
        """تنفيذ دالة بأمان مع معالجة الأخطاء"""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.handle_exception(f"خطأ في {context}", e, show_user=False)
            return None


class DatabaseErrorHandler(ErrorHandler):
    """معالج أخطاء قاعدة البيانات المتخصص"""
    
    def __init__(self, log_dir: str = "logs"):
        super().__init__(log_dir)
        self.db_error_count = 0
        
    def handle_db_error(self, operation: str, exception: Exception, 
                       table: str = "", critical: bool = False):
        """معالجة أخطاء قاعدة البيانات"""
        self.db_error_count += 1
        context = f"خطأ في قاعدة البيانات - العملية: {operation}"
        if table:
            context += f" - الجدول: {table}"
            
        # تحديد نوع الخطأ
        error_type = type(exception).__name__
        if "IntegrityError" in error_type:
            user_message = "خطأ في سلامة البيانات. تحقق من صحة المدخلات."
        elif "OperationalError" in error_type:
            user_message = "خطأ في العملية. تحقق من اتصال قاعدة البيانات."
        elif "DatabaseError" in error_type:
            user_message = "خطأ في قاعدة البيانات. اتصل بالدعم الفني."
        else:
            user_message = "حدث خطأ غير متوقع في قاعدة البيانات."
            
        self.handle_exception(f"{context}\n{user_message}", exception, 
                            show_user=True, critical=critical)


# إنشاء معالج الأخطاء العام
error_handler = ErrorHandler()
db_error_handler = DatabaseErrorHandler()


def setup_global_exception_handler():
    """إعداد معالج الاستثناءات العام"""
    def excepthook(exc_type, exc_value, exc_traceback):
        """معالجة الأخطاء غير المتوقعة"""
        if issubclass(exc_type, KeyboardInterrupt):
            # السماح بـ Ctrl+C
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
            
        error_handler.handle_exception(
            "خطأ غير متوقع في البرنامج", 
            exc_value, 
            show_user=True, 
            critical=True
        )
        
    sys.excepthook = excepthook


def handle_ui_error(func):
    """ديكوريتر لمعالجة أخطاء واجهة المستخدم"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_handler.handle_exception(
                f"خطأ في واجهة المستخدم - {func.__name__}", 
                e, 
                show_user=True
            )
            return None
    return wrapper


def handle_db_operation(func):
    """ديكوريتر لمعالجة عمليات قاعدة البيانات"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            db_error_handler.handle_db_error(
                func.__name__, 
                e, 
                critical=False
            )
            return None
    return wrapper


# دوال مساعدة للاستخدام السريع
def log_error(message: str, exception: Exception = None):
    """تسجيل خطأ سريع"""
    if exception:
        error_handler.handle_exception(message, exception, show_user=False)
    else:
        error_handler.logger.error(message)


def log_info(message: str):
    """تسجيل معلومات"""
    error_handler.log_info(message)


def log_warning(message: str):
    """تسجيل تحذير"""
    error_handler.log_warning(message)


def show_user_error(message: str):
    """عرض خطأ للمستخدم"""
    messagebox.showerror("خطأ", message)


def show_user_warning(message: str):
    """عرض تحذير للمستخدم"""
    messagebox.showwarning("تحذير", message)


def show_user_info(message: str):
    """عرض معلومات للمستخدم"""
    messagebox.showinfo("معلومات", message)
