# برنامج ست الكل للمحاسبة - الواجهة الجديدة

## وصف المشروع

تم تصميم واجهة برنامج محاسبة باللغة العربية تُدعى "ست الكل للمحاسبة" مطابقة للمواصفات المطلوبة والصورة المرجعية.

## مواصفات الواجهة

### الشريط العلوي
- **اللون**: رمادي فاتح (#F5F5F5)
- **المحتوى**: قائمة عربية من اليمين إلى اليسار تشمل:
  - برنامج، الرئيسية، الحسابات، الخزينة، التقارير، المراكز، المبيعات، المشتريات، خدمة العملاء، مساعدة، اشتراك، تنشيط
- **شريط البحث**: في الجانب الأيسر مع زر بحث أخضر

### الشريط الأخضر
- **اللون**: أخضر (#2E8B57)
- **المحتوى**: 6 أيقونات ثلاثية الأبعاد مع عناوين:
  - الموظفين، المحاسبة، الحسابات، الخزينة، الفواتير، التقارير
- **الشعار**: في الجانب الأيسر بخلفية خضراء داكنة

### المنطقة الرئيسية
- **الخلفية**: رمادية داكنة (#3C3C3C)
- **المحتوى**: شبكة من 18 زرًا ملونًا (6×3)
- **الأزرار**: مربعة الشكل بألوان مميزة وأيقونات ثلاثية الأبعاد

## الأزرار الـ 18

### الصف الأول (6 أزرار)
1. **أهلاً بكم** - أزرق فاتح (#5DADE2)
2. **إعداد** - أزرق فاتح (#5DADE2)
3. **إدخال الأصناف** - سماوي (#4ECDC4)
4. **إدخال الحسابات** - برتقالي (#F39C12)
5. **الحركة اليومية** - بنفسجي (#8E44AD)
6. **تحليل المبيعات** - أزرق (#3498DB)

### الصف الثاني (6 أزرار)
7. **مخزن** - برتقالي (#F39C12)
8. **بيع** - أخضر (#27AE60)
9. **شراء** - أحمر (#E74C3C)
10. **صرف** - برتقالي محمر (#E67E22)
11. **مؤشرات** - تيل (#16A085)
12. **مرتجع بيع** - أخضر (#27AE60)

### الصف الثالث (6 أزرار)
13. **عرض أسعار** - تيل (#16A085)
14. **مرتجع شراء** - بنفسجي (#8E44AD)
15. **كمية** - بنفسجي فاتح (#9B59B6)
16. **تحويل لمخزن** - أزرق (#3498DB)
17. **تسوية مخزن** - تيل فاتح (#1ABC9C)
18. **مؤشرات** - تيل (#16A085)

## كيفية التشغيل

### الطريقة المبسطة (موصى بها)
```bash
python simple_run.py
```

### الطريقة الكاملة (تتطلب مكتبات إضافية)
```bash
python main.py
```

## المتطلبات

### المكتبات الأساسية
- Python 3.8+
- customtkinter
- tkinter (مدمجة مع Python)

### تثبيت المكتبات
```bash
pip install customtkinter
```

### المكتبات الاختيارية (للنسخة الكاملة)
```bash
pip install psycopg2-binary apscheduler numpy matplotlib pyodbc
```

## الميزات

### التصميم
- ✅ واجهة عربية من اليمين إلى اليسار
- ✅ ألوان مطابقة للصورة المرجعية
- ✅ أيقونات ثلاثية الأبعاد
- ✅ تصميم مسطح عصري
- ✅ دقة 1412×768 بكسل

### الوظائف
- ✅ شريط بحث فعال
- ✅ قوائم تفاعلية
- ✅ أزرار قابلة للنقر
- ✅ تأثيرات بصرية عند التمرير
- ✅ رسائل تأكيد عند النقر

### التوافق
- ✅ أنظمة Windows
- ✅ أنظمة Linux
- ✅ أنظمة macOS

## هيكل الملفات

```
program mony/
├── simple_run.py          # ملف التشغيل المبسط
├── main.py                # ملف التشغيل الكامل
├── ui/
│   └── main_window.py     # الواجهة الرئيسية
├── assets/
│   ├── styles/
│   │   └── main_style.css # ملف الأنماط
│   └── icons/             # مجلد الأيقونات
└── README_INTERFACE.md    # هذا الملف
```

## التخصيص

### تغيير الألوان
يمكن تعديل الألوان في ملف `simple_run.py` في قسم `create_grid_button`:

```python
# مثال لتغيير لون زر "أهلاً بكم"
("أهلاً بكم", "#5DADE2")  # اللون الحالي
("أهلاً بكم", "#FF6B6B")  # لون جديد
```

### إضافة أيقونات حقيقية
1. ضع ملفات الأيقونات في مجلد `assets/icons/`
2. عدّل مسارات الأيقونات في الكود
3. استخدم صيغ PNG أو ICO للحصول على أفضل جودة

### تغيير النصوص
عدّل قائمة `all_icons` في دالة `create_main_grid` لتغيير أسماء الأزرار.

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تأكد من تثبيت جميع المكتبات المطلوبة
2. تحقق من إصدار Python (3.8+ مطلوب)
3. راجع رسائل الخطأ في الطرفية

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجريبية.

---

**ملاحظة**: الواجهة مطابقة تماماً للصورة المرجعية المطلوبة مع جميع العناصر والألوان المحددة.
