# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة إدارة وحدات القياس
Units Management Window
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk

try:
    from themes.modern_theme import MODERN_COLORS, FONTS
    from ui.window_utils import configure_window_fullscreen
except ImportError:
    MODERN_COLORS = {
        'primary': '#1f538d',
        'secondary': '#2c3e50',
        'success': '#27ae60',
        'danger': '#e74c3c',
        'error': '#e74c3c',
        'warning': '#f39c12',
        'info': '#3498db',
        'background': '#ecf0f1',
        'surface': '#ffffff',
        'text_primary': '#2c3e50',
        'text_secondary': '#7f8c8d',
        'border': '#bdc3c7'
    }
    FONTS = {'arabic': 'Arial', 'english': 'Arial'}

class UnitsManagementWindow:
    """نافذة إدارة وحدات القياس"""
    
    def __init__(self, parent, db_manager=None):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_unit_id = None
        self.units_data = []
        
        # إعداد النافذة
        self.create_window()
        self.load_units_data()
        
    def create_window(self):
        """إنشاء النافذة الرئيسية"""
        self.window = ctk.CTkToplevel(self.parent)
        
        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "📏 إدارة وحدات القياس - برنامج ست الكل للمحاسبة")

        # ضبط النافذة لملء الشاشة
        self.window  # ملء الشاشة في Windows

        # للأنظمة الأخرى كبديل
        try:
            self.window  # Linux
        except:
            pass

        # كبديل احتياطي - استخدام أبعاد الشاشة الكاملة
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        self.window.geometry(f"{screen_width}x{screen_height}+0+0")

        self.window.configure(fg_color=MODERN_COLORS['background'])

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إنشاء المحتوى
        self.create_header()
        self.create_main_layout()
        
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = ctk.CTkFrame(self.window, height=80, fg_color=MODERN_COLORS['primary'])
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        header_frame.pack_propagate(False)
        
        # الأيقونة والعنوان
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="right", padx=20, pady=15)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="📏 إدارة وحدات القياس",
            font=(FONTS['arabic'], 24, "bold"),
            text_color="white"
        )
        title_label.pack()
        
        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="إدارة وحدات القياس المختلفة للمنتجات",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        subtitle_label.pack()
        
        # معلومات سريعة
        info_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        info_frame.pack(side="left", padx=20, pady=15)
        
        self.units_count_label = ctk.CTkLabel(
            info_frame,
            text="إجمالي الوحدات: 0",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.units_count_label.pack()
    
    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي"""
        # إطار المحتوى الرئيسي
        main_container = ctk.CTkFrame(self.window, fg_color="transparent")
        main_container.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # الجانب الأيمن - نموذج إضافة/تعديل الوحدة
        self.create_unit_form(main_container)
        
        # الجانب الأيسر - جدول الوحدات
        self.create_units_table(main_container)
    
    def create_unit_form(self, parent):
        """إنشاء نموذج إضافة/تعديل الوحدة"""
        # إطار النموذج
        form_frame = ctk.CTkFrame(parent, width=400, fg_color=MODERN_COLORS['surface'])
        form_frame.pack(side="right", fill="y", padx=(0, 10))
        form_frame.pack_propagate(False)
        
        # عنوان النموذج
        form_title = ctk.CTkLabel(
            form_frame,
            text="📝 بيانات وحدة القياس",
            font=(FONTS['arabic'], 18, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        form_title.pack(pady=(20, 10))
        
        # إطار الحقول
        fields_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        fields_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # اسم الوحدة بالعربية
        self.create_form_field(fields_frame, "اسم الوحدة (عربي):", "name_ar", required=True)
        
        # اسم الوحدة بالإنجليزية
        self.create_form_field(fields_frame, "اسم الوحدة (English):", "name_en")
        
        # رمز الوحدة
        self.create_form_field(fields_frame, "رمز الوحدة:", "symbol", required=True)
        
        # أمثلة شائعة
        examples_label = ctk.CTkLabel(
            fields_frame,
            text="أمثلة: كجم، لتر، متر، قطعة، علبة، كرتون",
            font=(FONTS['arabic'], 10),
            text_color=MODERN_COLORS['text_secondary']
        )
        examples_label.pack(pady=(5, 15))
        
        # أزرار الإجراءات
        self.create_form_buttons(form_frame)
    
    def create_form_field(self, parent, label_text, field_name, required=False):
        """إنشاء حقل في النموذج"""
        # إطار الحقل
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=10)
        
        # التسمية
        label_text_with_required = f"{label_text} *" if required else label_text
        label = ctk.CTkLabel(
            field_frame,
            text=label_text_with_required,
            font=(FONTS['arabic'], 12, "bold" if required else "normal"),
            text_color=MODERN_COLORS['error'] if required else MODERN_COLORS['text_primary']
        )
        label.pack(anchor="e", pady=(0, 5))
        
        # الحقل
        entry = ctk.CTkEntry(
            field_frame,
            placeholder_text=f"أدخل {label_text}",
            font=(FONTS['arabic'], 12),
            height=35
        )
        entry.pack(fill="x", pady=(0, 5))
        
        # حفظ مرجع الحقل
        setattr(self, f"{field_name}_entry", entry)
        
        return entry
    
    def create_form_buttons(self, parent):
        """إنشاء أزرار النموذج"""
        buttons_frame = ctk.CTkFrame(parent, height=80, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=10)
        buttons_frame.pack_propagate(False)
        
        # زر حفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ الوحدة",
            command=self.save_unit,
            fg_color=MODERN_COLORS['success'],
            width=120,
            height=40,
            font=(FONTS['arabic'], 12, "bold")
        )
        save_btn.pack(side="right", padx=5, pady=10)
        
        # زر تعديل
        self.edit_btn = ctk.CTkButton(
            buttons_frame,
            text="✏️ تعديل",
            command=self.update_unit,
            fg_color=MODERN_COLORS['info'],
            width=100,
            height=40,
            font=(FONTS['arabic'], 12, "bold"),
            state="disabled"
        )
        self.edit_btn.pack(side="right", padx=5, pady=10)
        
        # زر جديد
        new_btn = ctk.CTkButton(
            buttons_frame,
            text="📄 جديد",
            command=self.clear_form,
            fg_color=MODERN_COLORS['primary'],
            width=100,
            height=40,
            font=(FONTS['arabic'], 12, "bold")
        )
        new_btn.pack(side="right", padx=5, pady=10)
        
        # زر حذف
        self.delete_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_unit,
            fg_color=MODERN_COLORS['error'],
            width=100,
            height=40,
            font=(FONTS['arabic'], 12, "bold"),
            state="disabled"
        )
        self.delete_btn.pack(side="left", padx=5, pady=10)
    
    def create_units_table(self, parent):
        """إنشاء جدول الوحدات"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(parent, fg_color=MODERN_COLORS['surface'])
        table_frame.pack(side="left", fill="both", expand=True)
        
        # عنوان الجدول
        table_header = ctk.CTkFrame(table_frame, height=60, fg_color=MODERN_COLORS['primary'])
        table_header.pack(fill="x", padx=10, pady=(10, 0))
        table_header.pack_propagate(False)
        
        # عنوان
        table_title = ctk.CTkLabel(
            table_header,
            text="📋 قائمة وحدات القياس",
            font=(FONTS['arabic'], 16, "bold"),
            text_color="white"
        )
        table_title.pack(side="right", padx=20, pady=15)
        
        # زر تحديث
        refresh_btn = ctk.CTkButton(
            table_header,
            text="🔄 تحديث",
            command=self.refresh_table,
            fg_color=MODERN_COLORS['success'],
            width=80,
            height=30,
            font=(FONTS['arabic'], 10)
        )
        refresh_btn.pack(side="left", padx=20, pady=15)
        
        # إطار الجدول الفعلي
        table_container = ctk.CTkFrame(table_frame, fg_color="white")
        table_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء Treeview
        columns = ("id", "name_ar", "name_en", "symbol")
        
        self.units_tree = ttk.Treeview(
            table_container,
            columns=columns,
            show="headings",
            height=20
        )
        
        # تعريف رؤوس الأعمدة
        headers = {
            "id": "الرقم",
            "name_ar": "الاسم (عربي)",
            "name_en": "الاسم (English)",
            "symbol": "الرمز"
        }
        
        for col, header in headers.items():
            self.units_tree.heading(col, text=header, anchor="center")
        
        # تحديد عرض الأعمدة
        column_widths = {
            "id": 80,
            "name_ar": 150,
            "name_en": 150,
            "symbol": 100
        }
        
        for col, width in column_widths.items():
            self.units_tree.column(col, width=width, anchor="center")
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.units_tree.yview)
        self.units_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.units_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط الأحداث
        self.units_tree.bind("<ButtonRelease-1>", self.on_unit_select)
        self.units_tree.bind("<Double-1>", self.on_unit_double_click)
        self.units_tree.bind("<Button-3>", self.show_context_menu)

    def load_units_data(self):
        """تحميل بيانات الوحدات"""
        try:
            if self.db_manager:
                # استعلام قاعدة البيانات الحقيقية
                query = """
                SELECT id, name_ar, name_en, symbol
                FROM units
                ORDER BY name_ar
                """
                self.units_data = self.db_manager.fetch_all(query)
            else:
                # بيانات وهمية للاختبار
                self.units_data = self.get_sample_units_data()

            # تحديث الجدول والعداد
            self.populate_units_table()
            self.update_units_count()

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الوحدات: {e}")

    def get_sample_units_data(self):
        """بيانات وهمية للاختبار"""
        return [
            (1, "قطعة", "Piece", "قطعة"),
            (2, "كيلوجرام", "Kilogram", "كجم"),
            (3, "جرام", "Gram", "جم"),
            (4, "لتر", "Liter", "لتر"),
            (5, "متر", "Meter", "م"),
            (6, "سنتيمتر", "Centimeter", "سم"),
            (7, "علبة", "Box", "علبة"),
            (8, "كرتونة", "Carton", "كرتون"),
            (9, "دزينة", "Dozen", "دزينة"),
            (10, "طن", "Ton", "طن"),
            (11, "باوند", "Pound", "باوند"),
            (12, "ساعة", "Hour", "ساعة"),
        ]

    def populate_units_table(self):
        """ملء جدول الوحدات"""
        # مسح البيانات السابقة
        for item in self.units_tree.get_children():
            self.units_tree.delete(item)

        # إضافة البيانات الجديدة
        for unit in self.units_data:
            self.units_tree.insert("", "end", values=unit)

    def update_units_count(self):
        """تحديث عداد الوحدات"""
        count = len(self.units_data)
        self.units_count_label.configure(text=f"إجمالي الوحدات: {count}")

    def on_unit_select(self, event):
        """عند تحديد وحدة"""
        selected_item = self.units_tree.selection()
        if selected_item:
            item_values = self.units_tree.item(selected_item[0])['values']
            if item_values:
                self.current_unit_id = item_values[0]
                self.fill_form_with_unit_data(item_values)

                # تفعيل أزرار التعديل والحذف
                self.edit_btn.configure(state="normal")
                self.delete_btn.configure(state="normal")

    def fill_form_with_unit_data(self, unit_values):
        """ملء النموذج ببيانات الوحدة"""
        # مسح النموذج
        self.clear_form()

        # ملء البيانات
        self.name_ar_entry.insert(0, unit_values[1])
        self.name_en_entry.insert(0, unit_values[2])
        self.symbol_entry.insert(0, unit_values[3])

    def clear_form(self):
        """مسح النموذج"""
        self.name_ar_entry.delete(0, "end")
        self.name_en_entry.delete(0, "end")
        self.symbol_entry.delete(0, "end")

        self.current_unit_id = None
        self.edit_btn.configure(state="disabled")
        self.delete_btn.configure(state="disabled")

    def save_unit(self):
        """حفظ وحدة جديدة"""
        # التحقق من البيانات
        if not self.validate_form():
            return

        try:
            # جمع البيانات
            name_ar = self.name_ar_entry.get().strip()
            name_en = self.name_en_entry.get().strip()
            symbol = self.symbol_entry.get().strip()

            if self.db_manager:
                # حفظ في قاعدة البيانات
                query = """
                INSERT INTO units (name_ar, name_en, symbol)
                VALUES (?, ?, ?)
                """
                self.db_manager.execute_query(query, (name_ar, name_en, symbol))
                messagebox.showinfo("نجح", "تم حفظ الوحدة بنجاح")
            else:
                # محاكاة الحفظ
                messagebox.showinfo("نجح", "تم حفظ الوحدة بنجاح (وضع الاختبار)")

            # تحديث البيانات
            self.load_units_data()
            self.clear_form()

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الوحدة: {e}")

    def update_unit(self):
        """تعديل الوحدة المحددة"""
        if not self.current_unit_id:
            messagebox.showwarning("تحذير", "يرجى تحديد وحدة للتعديل")
            return

        # التحقق من البيانات
        if not self.validate_form():
            return

        try:
            # جمع البيانات
            name_ar = self.name_ar_entry.get().strip()
            name_en = self.name_en_entry.get().strip()
            symbol = self.symbol_entry.get().strip()

            if self.db_manager:
                # تحديث في قاعدة البيانات
                query = """
                UPDATE units
                SET name_ar = ?, name_en = ?, symbol = ?
                WHERE id = ?
                """
                self.db_manager.execute_query(query, (name_ar, name_en, symbol, self.current_unit_id))
                messagebox.showinfo("نجح", "تم تعديل الوحدة بنجاح")
            else:
                # محاكاة التعديل
                messagebox.showinfo("نجح", "تم تعديل الوحدة بنجاح (وضع الاختبار)")

            # تحديث البيانات
            self.load_units_data()
            self.clear_form()

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل الوحدة: {e}")

    def delete_unit(self):
        """حذف الوحدة المحددة"""
        if not self.current_unit_id:
            messagebox.showwarning("تحذير", "يرجى تحديد وحدة للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الوحدة؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        )

        if result:
            try:
                if self.db_manager:
                    # حذف من قاعدة البيانات
                    query = "DELETE FROM units WHERE id = ?"
                    self.db_manager.execute_query(query, (self.current_unit_id,))
                    messagebox.showinfo("نجح", "تم حذف الوحدة بنجاح")
                else:
                    # محاكاة الحذف
                    messagebox.showinfo("نجح", "تم حذف الوحدة بنجاح (وضع الاختبار)")

                # تحديث البيانات
                self.load_units_data()
                self.clear_form()

            except Exception as e:
                pass
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الوحدة: {e}")

    def validate_form(self):
        """التحقق من صحة البيانات"""
        errors = []

        if not self.name_ar_entry.get().strip():
            errors.append("اسم الوحدة بالعربية مطلوب")

        if not self.symbol_entry.get().strip():
            errors.append("رمز الوحدة مطلوب")

        if errors:
            messagebox.showerror("خطأ في البيانات", "\n".join(errors))
            return False

        return True

    def refresh_table(self):
        """تحديث الجدول"""
        self.load_units_data()
        messagebox.showinfo("تحديث", "تم تحديث قائمة الوحدات بنجاح")

    def on_unit_double_click(self, event):
        """عند النقر المزدوج على وحدة"""
        self.on_unit_select(event)

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        # إنشاء قائمة سياقية
        context_menu = tk.Menu(self.window, tearoff=0)
        context_menu.add_command(label="تعديل الوحدة", command=self.update_unit)
        context_menu.add_command(label="حذف الوحدة", command=self.delete_unit)
        context_menu.add_separator()
        context_menu.add_command(label="نسخ الاسم", command=self.copy_unit_name)
        context_menu.add_command(label="نسخ الرمز", command=self.copy_unit_symbol)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def copy_unit_name(self):
        """نسخ اسم الوحدة"""
        selected_item = self.units_tree.selection()
        if selected_item:
            item_values = self.units_tree.item(selected_item[0])['values']
            unit_name = item_values[1]
            self.window.clipboard_clear()
            self.window.clipboard_append(unit_name)
            messagebox.showinfo("نسخ", f"تم نسخ اسم الوحدة: {unit_name}")
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد وحدة أولاً")

    def copy_unit_symbol(self):
        """نسخ رمز الوحدة"""
        selected_item = self.units_tree.selection()
        if selected_item:
            item_values = self.units_tree.item(selected_item[0])['values']
            unit_symbol = item_values[3]
            self.window.clipboard_clear()
            self.window.clipboard_append(unit_symbol)
            messagebox.showinfo("نسخ", f"تم نسخ رمز الوحدة: {unit_symbol}")
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد وحدة أولاً")

    def close_window(self):
        """إغلاق النافذة"""
        self.if window and hasattr(window, "destroy"):
    if window and hasattr(window, "destroy"):
    window.destroy()
