# -*- coding: utf-8 -*-
"""
اختبار إصلاح الشريط الأخضر
Test Green Bar Fix
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_green_bar_fix():
    """اختبار إصلاح الشريط الأخضر"""
    try:
        print("🧪 اختبار إصلاح الشريط الأخضر...")
        
        # استيراد البرنامج
        import large_font_run
        print("✅ تم استيراد البرنامج بنجاح")
        
        # إنشاء مثيل للاختبار
        app = large_font_run.LargeFontMainWindow()
        print("✅ تم إنشاء مثيل البرنامج")
        
        print("🎯 التغييرات المطبقة:")
        print("   ✅ إزالة الارتفاع الثابت للشريط الأخضر")
        print("   ✅ إزالة pack_propagate(False) للسماح بالتوسع التلقائي")
        print("   ✅ تحسين تخطيط الأيقونات")
        print("   ✅ إضافة مساحة بين الشريط والعناصر التالية")
        
        print("\n🎉 الإصلاح مكتمل! الشريط الأخضر لن يؤثر على عرض الأيقونات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = test_green_bar_fix()
    if success:
        print("\n🚀 يمكنك الآن تشغيل البرنامج:")
        print("   python large_font_run.py")
    sys.exit(0 if success else 1)
