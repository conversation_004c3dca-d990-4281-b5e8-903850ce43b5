#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بقواعد البيانات PostgreSQL و MongoDB
"""

import sys
from datetime import datetime

def test_postgresql():
    """اختبار الاتصال بـ PostgreSQL"""
    print("🐘 اختبار PostgreSQL...")
    try:
        import psycopg2
        from sqlalchemy import create_engine, text
        
        # معلومات الاتصال الافتراضية
        # يجب تغيير كلمة المرور حسب ما تم اختياره أثناء التثبيت
        connection_string = "postgresql://postgres:password@localhost:5432/postgres"
        
        print("   📡 محاولة الاتصال...")
        engine = create_engine(connection_string)
        
        with engine.connect() as connection:
            result = connection.execute(text("SELECT version();"))
            version = result.fetchone()[0]
            print(f"   ✅ تم الاتصال بنجاح!")
            print(f"   📋 الإصدار: {version[:50]}...")
            
            # اختبار إنشاء جدول
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS test_table (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """))
            
            # اختبار إدراج بيانات
            connection.execute(text("""
                INSERT INTO test_table (name) VALUES ('اختبار PostgreSQL')
                ON CONFLICT DO NOTHING;
            """))
            
            # اختبار استعلام
            result = connection.execute(text("SELECT COUNT(*) FROM test_table;"))
            count = result.fetchone()[0]
            print(f"   📊 عدد السجلات في جدول الاختبار: {count}")
            
            connection.commit()
            
        return True
        
    except ImportError:
        print("   ❌ مكتبة psycopg2 غير مثبتة")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {str(e)}")
        print("   💡 تأكد من:")
        print("      - تشغيل خدمة PostgreSQL")
        print("      - صحة كلمة المرور")
        print("      - صحة المنفذ (5432)")
        return False

def test_mongodb():
    """اختبار الاتصال بـ MongoDB"""
    print("\n🍃 اختبار MongoDB...")
    try:
        import pymongo
        from pymongo import MongoClient
        
        print("   📡 محاولة الاتصال...")
        
        # الاتصال المحلي الافتراضي
        client = MongoClient('mongodb://localhost:27017/')
        
        # اختبار الاتصال
        client.admin.command('ping')
        print("   ✅ تم الاتصال بنجاح!")
        
        # معلومات الخادم
        server_info = client.server_info()
        print(f"   📋 إصدار MongoDB: {server_info['version']}")
        
        # اختبار قاعدة بيانات
        db = client['test_database']
        collection = db['test_collection']
        
        # اختبار إدراج بيانات
        test_doc = {
            'name': 'اختبار MongoDB',
            'timestamp': datetime.now(),
            'type': 'test'
        }
        
        result = collection.insert_one(test_doc)
        print(f"   📝 تم إدراج مستند بـ ID: {result.inserted_id}")
        
        # اختبار استعلام
        count = collection.count_documents({})
        print(f"   📊 عدد المستندات في المجموعة: {count}")
        
        # تنظيف البيانات التجريبية
        collection.delete_many({'type': 'test'})
        
        client.close()
        return True
        
    except ImportError:
        print("   ❌ مكتبة pymongo غير مثبتة")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {str(e)}")
        print("   💡 تأكد من:")
        print("      - تشغيل خدمة MongoDB")
        print("      - صحة المنفذ (27017)")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🔍 اختبار الاتصال بقواعد البيانات")
    print("=" * 50)
    
    postgresql_ok = test_postgresql()
    mongodb_ok = test_mongodb()
    
    print("\n" + "=" * 50)
    print("📋 ملخص النتائج:")
    print(f"   PostgreSQL: {'✅ يعمل' if postgresql_ok else '❌ لا يعمل'}")
    print(f"   MongoDB: {'✅ يعمل' if mongodb_ok else '❌ لا يعمل'}")
    
    if postgresql_ok and mongodb_ok:
        print("\n🎉 جميع قواعد البيانات تعمل بشكل صحيح!")
    elif postgresql_ok or mongodb_ok:
        print("\n⚠️ بعض قواعد البيانات تعمل، تحقق من التي لا تعمل")
    else:
        print("\n❌ لا توجد قواعد بيانات تعمل، تحقق من التثبيت")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
