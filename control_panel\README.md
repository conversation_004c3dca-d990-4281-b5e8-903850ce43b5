# 🎛️ لوحة التحكم والإعدادات الشاملة

## 📋 نظرة عامة

نظام لوحة التحكم الشامل لبرنامج ست الكل للمحاسبة، مصمم ليوفر واجهة احترافية وسهلة الاستخدام لإدارة جميع إعدادات البرنامج.

## ✨ المميزات الرئيسية

### 🎨 التصميم
- **واجهة عصرية**: تصميم حديث يشبه التطبيقات المتقدمة
- **دعم RTL**: دعم كامل للغة العربية واتجاه النص من اليمين لليسار
- **ثيمات متعددة**: فاتح، داكن، وتلقائي
- **تأثيرات بصرية**: انتقالات سلسة وتأثيرات hover جذابة

### 🔧 الوظائف
- **إدارة شاملة**: 9 أقسام رئيسية للإعدادات
- **حفظ تلقائي**: نظام حفظ متقدم مع إدارة الأخطاء
- **استيراد/تصدير**: إمكانية نسخ واستيراد الإعدادات
- **إعادة تعيين**: استعادة الإعدادات الافتراضية

## 📁 هيكل المشروع

```
control_panel/
├── __init__.py                 # ملف التهيئة الرئيسي
├── control_panel_window.py     # النافذة الرئيسية
├── styles.qss                  # ملف الأنماط CSS
├── README.md                   # هذا الملف
├── icons/                      # مجلد الأيقونات
└── settings_pages/             # صفحات الإعدادات
    ├── __init__.py
    ├── general_settings.py     # الإعدادات العامة
    ├── company_settings.py     # إعدادات الشركة
    ├── accounting_settings.py  # إعدادات المحاسبة
    ├── inventory_settings.py   # إعدادات المخزون
    ├── invoicing_settings.py   # إعدادات الفوترة
    ├── users_settings.py       # إدارة المستخدمين
    ├── security_settings.py    # إعدادات الأمان
    ├── network_settings.py     # الشبكة والتكامل
    └── reports_settings.py     # التقارير والطباعة
```

## 🎛️ أقسام الإعدادات

### 🏠 الإعدادات العامة
- **اللغة**: العربية / الإنجليزية
- **الخط**: نوع وحجم الخط
- **الثيم**: فاتح / داكن / تلقائي
- **اتجاه النص**: RTL / LTR
- **السلوك العام**: الحفظ التلقائي، النافذة الافتراضية

### 🏢 بيانات الشركة
- **المعلومات الأساسية**: الاسم، العنوان، المدينة، الدولة
- **الشعار**: تحميل وإدارة شعار الشركة
- **معلومات الاتصال**: الهاتف، البريد الإلكتروني، الموقع
- **المعلومات القانونية**: الرقم الضريبي، السجل التجاري

### 💼 المحاسبة
- **السنة المالية**: تاريخ البداية والنهاية
- **طريقة القيد**: يدوي / تلقائي
- **العملة الافتراضية**: SAR, USD, EUR, إلخ
- **الحسابات الافتراضية**: المبيعات، المشتريات، الضرائب

### 📦 إدارة المخزون
- **طريقة التقييم**: FIFO, LIFO, المتوسط المرجح
- **التنبيهات**: انخفاض الكمية، انتهاء الصلاحية
- **الإعادة الطلب**: تلقائي / يدوي
- **الباركود**: توليد تلقائي

### 🧾 إعدادات الفوترة
- **قوالب الفواتير**: تصميمات متعددة
- **الترقيم التلقائي**: نمط وبادئة الأرقام
- **الضرائب**: معدل الضريبة الافتراضي
- **الطباعة**: إعدادات الطابعة والنسخ

### 👥 إدارة المستخدمين
- **إنشاء المستخدمين**: إضافة وتعديل المستخدمين
- **الصلاحيات**: تحديد الصلاحيات حسب الشاشة
- **سياسة كلمة المرور**: قوة وتعقيد كلمة المرور
- **سجل الجلسات**: تتبع نشاط المستخدمين

### 🔐 الأمان
- **النسخ الاحتياطي**: جدولة تلقائية
- **التشفير**: تشفير البيانات الحساسة
- **سجل التدقيق**: تسجيل جميع العمليات
- **مهلة الجلسة**: إعدادات انتهاء الجلسة

### 🌐 الشبكة والتكامل
- **قاعدة البيانات**: SQLite, PostgreSQL, SQL Server
- **الاتصال**: إعدادات الخادم والمنفذ
- **المزامنة**: مزامنة تلقائية للبيانات
- **API**: تكامل مع أنظمة خارجية

### 📊 التقارير والطباعة
- **تنسيق التقارير**: PDF, Excel, Word
- **الخطوط**: نوع وحجم خط التقارير
- **الشعار**: إدراج شعار الشركة
- **العلامة المائية**: إضافة علامة مائية

## 🚀 كيفية الاستخدام

### التشغيل المباشر
```python
from control_panel import ControlPanelWindow

# إنشاء نافذة الإعدادات
settings_window = ControlPanelWindow()
settings_window.show()
```

### التكامل مع التطبيق الرئيسي
```python
def open_settings():
    from control_panel.control_panel_window import ControlPanelWindow
    settings_window = ControlPanelWindow(parent_window)
    settings_window.show()
```

## 🔧 إدارة الإعدادات

### تحميل الإعدادات
```python
from core.settings_manager import settings_manager

# الحصول على إعداد محدد
language = settings_manager.get_setting("general", "language", "ar")

# الحصول على قسم كامل
company_info = settings_manager.get_section("company")
```

### حفظ الإعدادات
```python
# تعيين إعداد محدد
settings_manager.set_setting("general", "theme", "dark")

# تعيين قسم كامل
settings_manager.set_section("company", company_data)

# حفظ في الملف
settings_manager.save_settings()
```

### إعادة التعيين
```python
# إعادة تعيين قسم محدد
settings_manager.reset_section("general")

# إعادة تعيين جميع الإعدادات
settings_manager.reset_to_defaults()
```

## 📋 متطلبات النظام

### المكتبات المطلوبة
```
customtkinter >= 5.0.0
Pillow >= 9.0.0
```

### التثبيت
```bash
pip install customtkinter Pillow
```

## 🎨 التخصيص

### إضافة صفحة إعدادات جديدة
1. إنشاء ملف جديد في `settings_pages/`
2. إنشاء كلاس يرث من الإطار الأساسي
3. تنفيذ دوال `load_settings()` و `save_settings()`
4. إضافة الصفحة في `control_panel_window.py`

### تخصيص الألوان والأنماط
- تعديل ملف `styles.qss`
- استخدام متغيرات CSS للألوان
- إضافة تأثيرات وانتقالات جديدة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في تحميل الإعدادات**: تحقق من صحة ملف `settings.json`
2. **مشاكل في الخطوط**: تأكد من تثبيت خط Cairo
3. **أخطاء الاستيراد**: تحقق من مسارات الملفات

### سجل الأخطاء
يتم طباعة الأخطاء في وحدة التحكم مع تفاصيل كاملة للتشخيص.

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع التوثيق الكامل
- تحقق من سجل الأخطاء
- تواصل مع فريق التطوير

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة برنامج ست الكل للمحاسبة.

---

**تم التطوير بواسطة**: فريق برنامج ست الكل للمحاسبة  
**الإصدار**: 1.0.0  
**تاريخ التحديث**: 2025-07-29
