# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة الترحيب الاحترافية
Professional Welcome Window
"""

import customtkinter as ctk
import tkinter as tk
import threading
import time

try:
    from themes.modern_theme import MODERN_COLORS, FONTS
    from ui.window_utils import configure_window_fullscreen
    from pathlib import Path
    from PIL import Image, ImageDraw, ImageTk
except ImportError:
    MODERN_COLORS = {
        'primary': '#2E8B57',
        'secondary': '#4682B4',
        'success': '#28a745',
        'background': '#f8f9fa',
        'surface': '#ffffff',
        'text_primary': '#212529',
        'text_secondary': '#6c757d',
        'gold': '#FFD700',
        'silver': '#C0C0C0',
        'warning': '#ffc107',
        'info': '#17a2b8',
        'light': '#f8f9fa',
        'dark': '#343a40'
    }
    FONTS = {'arabic': 'Arial', 'english': 'Arial'}

class WelcomeWindow:
    """نافذة الترحيب الاحترافية"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
        self.progress_value = 0
        self.create_welcome_window()
        
    def create_welcome_window(self):
        """إنشاء نافذة الترحيب"""
        # إنشاء النافذة
        if self.parent:
            self.window = ctk.CTkToplevel(self.parent)
        else:
            self.window = ctk.CTk()
        
        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "أهلاً وسهلاً - شركة ست الكل العالمية - برنامج ست الكل للمحاسبة")

        # ضبط النافذة لملء الشاشة
        try:
            self.window
        except:
            pass
        except:
            try:
                self.window
            except:
                pass
            except:
                screen_width = self.window.winfo_screenwidth()
                screen_height = self.window.winfo_screenheight()
                self.window.geometry(f"{screen_width}x{screen_height}+0+0")

        self.window.configure(fg_color=MODERN_COLORS['background'])

        # المحتوى الرئيسي
        self.create_main_content()

        # شريط التقدم
        self.create_progress_bar()

        # بدء التحميل
        self.start_loading()
        
    def create_gradient_background(self):
        """إنشاء خلفية بسيطة"""
        # الإطار الرئيسي
        self.main_frame = ctk.CTkFrame(
            self.window,
            fg_color=MODERN_COLORS['background'],
            corner_radius=0
        )
        self.main_frame.pack(fill="both", expand=True)

        # إطار علوي للزخرفة
        top_decoration = ctk.CTkFrame(
            self.main_frame,
            height=10,
            fg_color=MODERN_COLORS['primary'],
            corner_radius=0
        )
        top_decoration.pack(fill="x", side="top")

        # إطار سفلي للزخرفة
        bottom_decoration = ctk.CTkFrame(
            self.main_frame,
            height=10,
            fg_color=MODERN_COLORS['gold'],
            corner_radius=0
        )
        bottom_decoration.pack(fill="x", side="bottom")
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إنشاء الإطار الرئيسي أولاً
        self.create_gradient_background()

        # الإطار المركزي
        center_frame = ctk.CTkFrame(
            self.main_frame,
            fg_color="transparent"
        )
        center_frame.pack(expand=True, fill="both", padx=100, pady=50)

        # العنوان الرئيسي
        welcome_title = ctk.CTkLabel(
            center_frame,
            text="🌟 أهلاً وسهلاً بكم 🌟",
            font=(FONTS['arabic'], 48, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        welcome_title.pack(pady=(50, 30))

        # إطار المحتوى المركزي
        content_frame = ctk.CTkFrame(
            center_frame,
            fg_color=MODERN_COLORS['surface'],
            corner_radius=20,
            border_width=3,
            border_color=MODERN_COLORS['gold']
        )
        content_frame.pack(expand=True, fill="both", padx=50, pady=30)

        # إطار الصورة والنص
        info_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        info_frame.pack(expand=True, fill="both", padx=40, pady=40)

        # الجانب الأيسر - الصورة
        self.create_image_section(info_frame)

        # الجانب الأيمن - النص
        self.create_text_section(info_frame)

        # معلومات المطور في الأسفل
        self.create_developer_info(content_frame)
        
    def create_image_section(self, parent):
        """إنشاء قسم الصورة"""
        image_frame = ctk.CTkFrame(parent, fg_color="transparent")
        image_frame.pack(side="left", fill="y", padx=(0, 40))
        
        try:
            # تحميل الصورة
            image_path = "assets/ceo_image.jpg"  # مسار الصورة
            if not Path(image_path).exists():
                # إنشاء صورة افتراضية إذا لم توجد الصورة
                self.create_default_image(image_frame)
            else:
                # تحميل ومعالجة الصورة
                self.load_and_process_image(image_frame, image_path)
                
        except Exception as e:
            pass
        except Exception as e:
            print(f"خطأ في تحميل الصورة: {e}")
            self.create_default_image(image_frame)
    
    def create_default_image(self, parent):
        """إنشاء صورة افتراضية"""
        # إطار دائري للصورة الافتراضية
        image_container = ctk.CTkFrame(
            parent,
            width=300,
            height=300,
            corner_radius=150,
            fg_color=MODERN_COLORS['primary']
        )
        image_container.pack(pady=50)
        image_container.pack_propagate(False)
        
        # أيقونة افتراضية
        default_icon = ctk.CTkLabel(
            image_container,
            text="👤",
            font=("Arial", 120),
            text_color="white"
        )
        default_icon.pack(expand=True)
        
    def load_and_process_image(self, parent, image_path):
        """تحميل ومعالجة الصورة"""
        try:
            # تحميل الصورة
            original_image = Image.open(image_path)
            
            # تغيير حجم الصورة
            size = (300, 300)
            image = original_image.resize(size, Image.Resampling.LANCZOS)
            
            # إنشاء قناع دائري
            mask = Image.new('L', size, 0)
            draw = ImageDraw.Draw(mask)
            draw.ellipse((0, 0) + size, fill=255)
            
            # تطبيق القناع الدائري
            circular_image = Image.new('RGBA', size, (0, 0, 0, 0))
            circular_image.paste(image, (0, 0))
            circular_image.putalpha(mask)
            
            # إضافة إطار ذهبي
            frame_image = Image.new('RGBA', (320, 320), (0, 0, 0, 0))
            frame_draw = ImageDraw.Draw(frame_image)
            
            # رسم الإطار الذهبي
            frame_draw.ellipse((0, 0, 320, 320), outline='#FFD700', width=8)
            frame_draw.ellipse((5, 5, 315, 315), outline='#FFA500', width=4)
            
            # دمج الصورة مع الإطار
            frame_image.paste(circular_image, (10, 10), circular_image)
            
            # تحويل للعرض في tkinter
            photo = ImageTk.PhotoImage(frame_image)
            
            # عرض الصورة
            image_label = ctk.CTkLabel(
                parent,
                image=photo,
                text=""
            )
            image_label.image = photo  # حفظ مرجع
            image_label.pack(pady=50)
            
        except Exception as e:
            pass
        except Exception as e:
            print(f"خطأ في معالجة الصورة: {e}")
            self.create_default_image(parent)
    
    def create_text_section(self, parent):
        """إنشاء قسم النص"""
        text_frame = ctk.CTkFrame(parent, fg_color="transparent")
        text_frame.pack(side="right", fill="both", expand=True)
        
        # اسم الشركة
        company_name = ctk.CTkLabel(
            text_frame,
            text="شــركــة ســت الكل العـالـمـية",
            font=(FONTS['arabic'], 36, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        company_name.pack(pady=(30, 20))
        
        # اسم البرنامج
        program_name = ctk.CTkLabel(
            text_frame,
            text="برنامج شركة ست الكل للمحاسبة المالية",
            font=(FONTS['arabic'], 24, "bold"),
            text_color=MODERN_COLORS['secondary']
        )
        program_name.pack(pady=(0, 30))
        
        # خط فاصل ذهبي
        separator = ctk.CTkFrame(
            text_frame,
            height=3,
            fg_color=MODERN_COLORS['gold']
        )
        separator.pack(fill="x", padx=50, pady=20)
        
        # منصب المدير
        position_label = ctk.CTkLabel(
            text_frame,
            text="مدير الشركة ورئيس مجلس الإدارة",
            font=(FONTS['arabic'], 20, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        position_label.pack(pady=(20, 10))
        
        # اسم المدير
        manager_name = ctk.CTkLabel(
            text_frame,
            text="محمود عبد الحميد",
            font=(FONTS['arabic'], 28, "bold"),
            text_color=MODERN_COLORS['gold']
        )
        manager_name.pack(pady=(0, 30))
        
        # إطار زخرفي
        decoration_frame = ctk.CTkFrame(
            text_frame,
            height=100,
            fg_color=["#f0f8ff", "#e6f3ff"],
            corner_radius=15,
            border_width=2,
            border_color=MODERN_COLORS['primary']
        )
        decoration_frame.pack(fill="x", padx=20, pady=20)
        
        # نص ترحيبي إضافي
        welcome_text = ctk.CTkLabel(
            decoration_frame,
            text="🎯 نحو مستقبل أفضل في عالم المحاسبة والإدارة المالية 🎯",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        welcome_text.pack(expand=True)
        
    def create_developer_info(self, parent):
        """إنشاء معلومات المطور"""
        dev_frame = ctk.CTkFrame(
            parent,
            height=120,
            fg_color=MODERN_COLORS['primary'],
            corner_radius=15
        )
        dev_frame.pack(fill="x", side="bottom", padx=20, pady=20)
        dev_frame.pack_propagate(False)
        
        # عنوان المطور
        dev_title = ctk.CTkLabel(
            dev_frame,
            text="💻 تطوير وبرمجة",
            font=(FONTS['arabic'], 16, "bold"),
            text_color="white"
        )
        dev_title.pack(pady=(10, 5))
        
        # اسم المطور
        dev_name = ctk.CTkLabel(
            dev_frame,
            text="برنامج ست الكل إعداد وتنفيذ م. حمزة الناعم",
            font=(FONTS['arabic'], 18, "bold"),
            text_color=MODERN_COLORS['gold']
        )
        dev_name.pack(pady=2)
        
        # معلومات الاتصال
        contact_frame = ctk.CTkFrame(dev_frame, fg_color="transparent")
        contact_frame.pack(pady=5)
        
        # البريد الإلكتروني
        email_label = ctk.CTkLabel(
            contact_frame,
            text="📧 mail: <EMAIL>",
            font=(FONTS['english'], 14, "bold"),
            text_color="white"
        )
        email_label.pack(side="left", padx=20)
        
        # رقم الهاتف
        mobile_label = ctk.CTkLabel(
            contact_frame,
            text="📱 Mobile: +963991555117",
            font=(FONTS['english'], 14, "bold"),
            text_color="white"
        )
        mobile_label.pack(side="right", padx=20)
        
    def create_progress_bar(self):
        """إنشاء شريط التقدم"""
        # إطار شريط التقدم
        progress_frame = ctk.CTkFrame(
            self.main_frame,
            height=60,
            fg_color="transparent"
        )
        progress_frame.pack(fill="x", side="bottom", padx=100, pady=20)
        
        # نص التحميل
        self.loading_label = ctk.CTkLabel(
            progress_frame,
            text="🚀 جاري تحميل النظام...",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        self.loading_label.pack(pady=(0, 10))
        
        # شريط التقدم
        self.progress_bar = ctk.CTkProgressBar(
            progress_frame,
            width=600,
            height=20,
            progress_color=MODERN_COLORS['primary'],
            fg_color=MODERN_COLORS['surface']
        )
        self.progress_bar.pack()
        self.progress_bar.set(0)
        
    def start_loading(self):
        """بدء عملية التحميل"""
        def loading_animation():
            loading_texts = [
                "🔄 تهيئة النظام...",
                "📊 تحميل قاعدة البيانات...",
                "🎨 إعداد الواجهة...",
                "🔐 فحص الصلاحيات...",
                "📦 تحميل الوحدات...",
                "✅ اكتمل التحميل!"
            ]
            
            for i, text in enumerate(loading_texts):
                if self.window and self.window.winfo_exists():
                    self.loading_label.configure(text=text)
                    progress = (i + 1) / len(loading_texts)
                    self.progress_bar.set(progress)
                    time.sleep(1.5)
                else:
                    break
            
            # إغلاق نافذة الترحيب بعد التحميل
            if self.window and self.window.winfo_exists():
                time.sleep(1)
                self.close_welcome()
        
        # تشغيل التحميل في خيط منفصل
        loading_thread = threading.Thread(target=loading_animation)
        loading_thread.daemon = True
        loading_thread.start()
        
    def close_welcome(self):
        """إغلاق نافذة الترحيب"""
        if self.window and self.window.winfo_exists():
            self.if window and hasattr(window, "destroy"):
    if window and hasattr(window, "destroy"):
    window.destroy()
    
    def show(self):
        """عرض نافذة الترحيب"""
        if self.window:
            self.window.mainloop()

def main():
    """تشغيل نافذة الترحيب للاختبار"""
    welcome = WelcomeWindow()
    welcome.show()

if __name__ == "__main__":
    main()
