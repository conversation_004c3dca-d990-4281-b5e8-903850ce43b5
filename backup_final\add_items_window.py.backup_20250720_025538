# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة إضافة وإدارة الأصناف
Add Items Management Window
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime
import re

try:
    from themes.modern_theme import MODERN_COLORS, FONTS
    from core.barcode_scanner import BarcodeScannerWindow
    from ui.window_utils import configure_window_fullscreen
except ImportError:
    MODERN_COLORS = {
        'primary': '#1f538d',
        'secondary': '#2c3e50',
        'success': '#27ae60',
        'danger': '#e74c3c',
        'error': '#e74c3c',
        'warning': '#f39c12',
        'info': '#3498db',
        'background': '#ecf0f1',
        'surface': '#ffffff',
        'text_primary': '#2c3e50',
        'text_secondary': '#7f8c8d',
        'border': '#bdc3c7'
    }
    FONTS = {'arabic': 'Arial', 'english': 'Arial'}
    BarcodeScannerWindow = None

class AddItemsWindow:
    """نافذة إضافة وإدارة الأصناف الاحترافية"""
    
    def __init__(self, parent, db_manager=None):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_item_id = None
        self.items_data = []
        self.categories_data = []
        self.units_data = []
        
        # إعداد النافذة
        self.create_window()
        self.load_initial_data()

    def create_window(self):
        """إنشاء نافذة إدارة الأصناف والمنتجات"""

        self.window = ctk.CTkToplevel(self.parent)
        self.window.title("🏪 إدارة الأصناف والمنتجات")

        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "إدارة الأصناف والمنتجات - برنامج ست الكل للمحاسبة")
        self.window.configure(fg_color=MODERN_COLORS['background'])

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # إنشاء المحتوى
        self.create_header()
        self.create_main_layout()
        
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = ctk.CTkFrame(self.window, height=80, fg_color=MODERN_COLORS['primary'])
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        header_frame.pack_propagate(False)
        
        # الأيقونة والعنوان
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="right", padx=20, pady=15)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="📦 إدارة الأصناف والمنتجات",
            font=(FONTS['arabic'], 24, "bold"),
            text_color="white"
        )
        title_label.pack()
        
        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="إضافة وتعديل وإدارة جميع أصناف المخزن",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        subtitle_label.pack()
        
        # معلومات سريعة
        info_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        info_frame.pack(side="left", padx=20, pady=15)
        
        self.items_count_label = ctk.CTkLabel(
            info_frame,
            text="إجمالي الأصناف: 0",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.items_count_label.pack()
        
        self.active_items_label = ctk.CTkLabel(
            info_frame,
            text="الأصناف النشطة: 0",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.active_items_label.pack()
    
    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي"""
        # إطار المحتوى الرئيسي
        main_container = ctk.CTkFrame(self.window, fg_color="transparent")
        main_container.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # الجانب الأيمن - نموذج إضافة/تعديل الصنف
        self.create_item_form(main_container)
        
        # الجانب الأيسر - جدول الأصناف
        self.create_items_table(main_container)
    
    def create_item_form(self, parent):
        """إنشاء نموذج إضافة/تعديل الصنف"""
        # إطار النموذج
        form_frame = ctk.CTkFrame(parent, width=500, fg_color=MODERN_COLORS['surface'])
        form_frame.pack(side="right", fill="y", padx=(0, 10))
        form_frame.pack_propagate(False)
        
        # عنوان النموذج
        form_title = ctk.CTkLabel(
            form_frame,
            text="📝 بيانات الصنف",
            font=(FONTS['arabic'], 18, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        form_title.pack(pady=(20, 10))
        
        # إطار قابل للتمرير للحقول
        scroll_frame = ctk.CTkScrollableFrame(form_frame, height=600)
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # معلومات أساسية
        basic_info_label = ctk.CTkLabel(
            scroll_frame,
            text="📋 المعلومات الأساسية",
            font=(FONTS['arabic'], 14, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        basic_info_label.pack(pady=(10, 5), anchor="e")
        
        # كود الصنف مع زر قارئ الباركود
        self.create_barcode_field(scroll_frame)
        
        # اسم الصنف بالعربية
        self.create_form_field(scroll_frame, "اسم الصنف (عربي):", "name_ar", required=True)
        
        # اسم الصنف بالإنجليزية
        self.create_form_field(scroll_frame, "اسم الصنف (English):", "name_en")

        # وصف الصنف
        self.create_textarea_field(scroll_frame, "وصف الصنف:", "description")

        # التصنيف
        self.create_combobox_field(scroll_frame, "التصنيف:", "category_id", self.get_categories())

        # وحدة القياس الأساسية
        self.create_combobox_field(scroll_frame, "وحدة القياس الأساسية:", "unit_id", self.get_units())

        # وحدة القياس الثانوية
        self.create_combobox_field(scroll_frame, "وحدة القياس الثانوية:", "secondary_unit_id", self.get_units())

        # معامل التحويل
        self.create_form_field(scroll_frame, "معامل التحويل:", "conversion_factor", field_type="number")

        # المخزن الافتراضي
        self.create_combobox_field(scroll_frame, "المخزن الافتراضي:", "default_warehouse_id", self.get_warehouses())
        
        # معلومات الأسعار
        price_info_label = ctk.CTkLabel(
            scroll_frame,
            text="💰 معلومات الأسعار",
            font=(FONTS['arabic'], 14, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        price_info_label.pack(pady=(20, 5), anchor="e")
        
        # سعر التكلفة
        self.create_form_field(scroll_frame, "سعر التكلفة:", "cost_price", field_type="number")

        # سعر البيع
        self.create_form_field(scroll_frame, "سعر البيع:", "sale_price", field_type="number")

        # سعر الجملة
        self.create_form_field(scroll_frame, "سعر الجملة:", "wholesale_price", field_type="number")

        # آخر سعر شراء
        self.create_form_field(scroll_frame, "آخر سعر شراء:", "last_purchase_price", field_type="number")

        # نسبة ضريبة القيمة المضافة
        self.create_form_field(scroll_frame, "نسبة الضريبة (%):", "vat_rate", field_type="number")
        
        # معلومات المخزون
        stock_info_label = ctk.CTkLabel(
            scroll_frame,
            text="📊 معلومات المخزون",
            font=(FONTS['arabic'], 14, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        stock_info_label.pack(pady=(20, 5), anchor="e")
        
        # الحد الأدنى للمخزون
        self.create_form_field(scroll_frame, "الحد الأدنى للمخزون:", "min_stock", field_type="number")

        # الحد الأقصى للمخزون
        self.create_form_field(scroll_frame, "الحد الأقصى للمخزون:", "max_stock", field_type="number")

        # نقطة إعادة الطلب
        self.create_form_field(scroll_frame, "نقطة إعادة الطلب:", "reorder_level", field_type="number")

        # خيارات التتبع
        tracking_info_label = ctk.CTkLabel(
            scroll_frame,
            text="🔍 خيارات التتبع",
            font=(FONTS['arabic'], 14, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        tracking_info_label.pack(pady=(20, 5), anchor="e")

        # تتبع الأرقام التسلسلية
        self.create_checkbox_field(scroll_frame, "تتبع بالأرقام التسلسلية", "serial_tracking")

        # تتبع الدفعات
        self.create_checkbox_field(scroll_frame, "تتبع بالدفعات", "batch_tracking")

        # تاريخ انتهاء الصلاحية
        self.create_checkbox_field(scroll_frame, "يتطلب تاريخ انتهاء صلاحية", "expiry_date_required")

        # صنف خدمة
        self.create_checkbox_field(scroll_frame, "صنف خدمة (غير مادي)", "is_service")

        # حالة الصنف
        self.create_checkbox_field(scroll_frame, "الصنف نشط", "is_active")

        # صورة المنتج
        product_info_label = ctk.CTkLabel(
            scroll_frame,
            text="🖼️ صورة المنتج",
            font=(FONTS['arabic'], 14, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        product_info_label.pack(pady=(20, 5), anchor="e")

        # حقل صورة المنتج
        self.create_image_field(scroll_frame)
        
        # أزرار الإجراءات (خارج الإطار القابل للتمرير)
        self.create_form_buttons(form_frame)
    
    def create_form_field(self, parent, label_text, field_name, required=False, field_type="text"):
        """إنشاء حقل في النموذج"""
        # إطار الحقل
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)
        
        # التسمية
        label_text_with_required = f"{label_text} *" if required else label_text
        label = ctk.CTkLabel(
            field_frame,
            text=label_text_with_required,
            font=(FONTS['arabic'], 12, "bold" if required else "normal"),
            text_color=MODERN_COLORS['error'] if required else MODERN_COLORS['text_primary']
        )
        label.pack(anchor="e", pady=(0, 5))
        
        # الحقل
        if field_type == "number":
            entry = ctk.CTkEntry(
                field_frame,
                placeholder_text="0.00",
                font=(FONTS['english'], 12)
            )
        else:
            entry = ctk.CTkEntry(
                field_frame,
                placeholder_text=f"أدخل {label_text}",
                font=(FONTS['arabic'], 12)
            )
        
        entry.pack(fill="x", pady=(0, 5))
        
        # حفظ مرجع الحقل
        setattr(self, f"{field_name}_entry", entry)
        
        return entry
    
    def create_combobox_field(self, parent, label_text, field_name, values):
        """إنشاء حقل قائمة منسدلة"""
        # إطار الحقل
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)
        
        # التسمية
        label = ctk.CTkLabel(
            field_frame,
            text=label_text,
            font=(FONTS['arabic'], 12),
            text_color=MODERN_COLORS['text_primary']
        )
        label.pack(anchor="e", pady=(0, 5))
        
        # القائمة المنسدلة
        combobox = ctk.CTkComboBox(
            field_frame,
            values=values,
            font=(FONTS['arabic'], 12)
        )
        combobox.pack(fill="x", pady=(0, 5))
        
        # حفظ مرجع الحقل
        setattr(self, f"{field_name}_combo", combobox)
        
        return combobox
    
    def create_checkbox_field(self, parent, label_text, field_name):
        """إنشاء حقل مربع اختيار"""
        # إطار الحقل
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=10)
        
        # مربع الاختيار
        checkbox = ctk.CTkCheckBox(
            field_frame,
            text=label_text,
            font=(FONTS['arabic'], 12)
        )
        checkbox.pack(anchor="e")
        checkbox.select()  # محدد افتراضياً
        
        # حفظ مرجع الحقل
        setattr(self, f"{field_name}_checkbox", checkbox)
        
        return checkbox

    def create_textarea_field(self, parent, label_text, field_name):
        """إنشاء حقل نص متعدد الأسطر"""
        # إطار الحقل
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)

        # التسمية
        label = ctk.CTkLabel(
            field_frame,
            text=label_text,
            font=(FONTS['arabic'], 12),
            text_color=MODERN_COLORS['text_primary']
        )
        label.pack(anchor="e", pady=(0, 5))

        # حقل النص
        textbox = ctk.CTkTextbox(
            field_frame,
            height=80,
            font=(FONTS['arabic'], 12)
        )
        textbox.pack(fill="x", pady=(0, 5))

        # حفظ مرجع الحقل
        setattr(self, f"{field_name}_textbox", textbox)

        return textbox

    def create_image_field(self, parent):
        """إنشاء حقل صورة المنتج"""
        # إطار الحقل
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)

        # إطار الصورة والأزرار
        image_container = ctk.CTkFrame(field_frame, fg_color="transparent")
        image_container.pack(fill="x", pady=(0, 5))

        # عرض الصورة
        self.image_label = ctk.CTkLabel(
            image_container,
            text="📷 لا توجد صورة",
            width=150,
            height=150,
            fg_color=MODERN_COLORS['surface'],
            corner_radius=10
        )
        self.image_label.pack(side="right", padx=10)

        # أزرار الصورة
        image_buttons_frame = ctk.CTkFrame(image_container, fg_color="transparent")
        image_buttons_frame.pack(side="right", padx=10)

        # زر اختيار صورة
        select_image_btn = ctk.CTkButton(
            image_buttons_frame,
            text="📁 اختيار صورة",
            command=self.select_product_image,
            fg_color=MODERN_COLORS['info'],
            width=120,
            height=35,
            font=(FONTS['arabic'], 11)
        )
        select_image_btn.pack(pady=5)

        # زر حذف الصورة
        remove_image_btn = ctk.CTkButton(
            image_buttons_frame,
            text="🗑️ حذف الصورة",
            command=self.remove_product_image,
            fg_color=MODERN_COLORS['error'],
            width=120,
            height=35,
            font=(FONTS['arabic'], 11)
        )
        remove_image_btn.pack(pady=5)

        # متغير مسار الصورة
        self.image_path = None

        return field_frame

    def create_barcode_field(self, parent):
        """إنشاء حقل كود الصنف مع زر قارئ الباركود"""
        # إطار الحقل
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)

        # التسمية
        label = ctk.CTkLabel(
            field_frame,
            text="كود الصنف / الباركود: *",
            font=(FONTS['arabic'], 12, "bold"),
            text_color=MODERN_COLORS['error']
        )
        label.pack(anchor="e", pady=(0, 5))

        # إطار الحقل والزر
        input_frame = ctk.CTkFrame(field_frame, fg_color="transparent")
        input_frame.pack(fill="x", pady=(0, 5))

        # حقل كود الصنف
        self.item_code_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="أدخل كود الصنف أو استخدم قارئ الباركود",
            font=(FONTS['arabic'], 12)
        )
        self.item_code_entry.pack(side="right", fill="x", expand=True, padx=(0, 5))

        # زر قارئ الباركود
        barcode_btn = ctk.CTkButton(
            input_frame,
            text="📷 قارئ الباركود",
            command=self.open_barcode_scanner,
            fg_color=MODERN_COLORS['info'],
            width=120,
            height=32,
            font=(FONTS['arabic'], 10, "bold")
        )
        barcode_btn.pack(side="left")

        # زر البحث عن الصنف
        search_btn = ctk.CTkButton(
            input_frame,
            text="🔍",
            command=self.search_item_by_code,
            fg_color=MODERN_COLORS['warning'],
            width=40,
            height=32,
            font=(FONTS['arabic'], 12)
        )
        search_btn.pack(side="left", padx=(5, 0))

        return self.item_code_entry
    
    def create_form_buttons(self, parent):
        """إنشاء أزرار النموذج"""
        # إطار الأزرار مع خلفية مرئية
        buttons_frame = ctk.CTkFrame(parent, height=120, fg_color=MODERN_COLORS['surface'])
        buttons_frame.pack(fill="x", padx=10, pady=20, side="bottom")
        buttons_frame.pack_propagate(False)

        # عنوان الأزرار
        buttons_title = ctk.CTkLabel(
            buttons_frame,
            text="🔧 إجراءات الصنف",
            font=(FONTS['arabic'], 14, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        buttons_title.pack(pady=(10, 5))

        # إطار الأزرار الرئيسية (الصف الأول)
        main_actions_frame = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        main_actions_frame.pack(fill="x", padx=20, pady=(0, 5))

        # زر حفظ (الأهم - أخضر كبير)
        self.save_btn = ctk.CTkButton(
            main_actions_frame,
            text="💾 حفظ الصنف",
            command=self.save_item,
            fg_color=MODERN_COLORS['success'],
            hover_color="#1e7e34",
            width=150,
            height=50,
            font=(FONTS['arabic'], 14, "bold")
        )
        self.save_btn.pack(side="right", padx=5)

        # زر إلغاء الأمر (جديد - أحمر)
        self.cancel_btn = ctk.CTkButton(
            main_actions_frame,
            text="❌ إلغاء الأمر",
            command=self.cancel_operation,
            fg_color=MODERN_COLORS['error'],
            hover_color="#c82333",
            width=130,
            height=50,
            font=(FONTS['arabic'], 14, "bold")
        )
        self.cancel_btn.pack(side="right", padx=5)

        # زر جديد
        self.new_btn = ctk.CTkButton(
            main_actions_frame,
            text="📄 جديد",
            command=self.clear_form,
            fg_color=MODERN_COLORS['primary'],
            hover_color="#1e5a96",
            width=120,
            height=50,
            font=(FONTS['arabic'], 12, "bold")
        )
        self.new_btn.pack(side="right", padx=5)

        # زر تعديل
        self.edit_btn = ctk.CTkButton(
            main_actions_frame,
            text="✏️ تعديل",
            command=self.update_item,
            fg_color=MODERN_COLORS['info'],
            hover_color="#138496",
            width=120,
            height=50,
            font=(FONTS['arabic'], 12, "bold"),
            state="disabled"
        )
        self.edit_btn.pack(side="left", padx=5)

        # زر حذف
        self.delete_btn = ctk.CTkButton(
            main_actions_frame,
            text="🗑️ حذف",
            command=self.delete_item,
            fg_color="#dc3545",
            hover_color="#c82333",
            width=120,
            height=50,
            font=(FONTS['arabic'], 12, "bold"),
            state="disabled"
        )
        self.delete_btn.pack(side="left", padx=5)

        # إطار الأزرار الثانوية (الصف الثاني)
        secondary_actions_frame = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        secondary_actions_frame.pack(fill="x", padx=20, pady=(0, 10))

        # زر قارئ الباركود
        barcode_btn = ctk.CTkButton(
            secondary_actions_frame,
            text="📷 قارئ باركود",
            command=self.scan_barcode,
            fg_color=MODERN_COLORS['warning'],
            hover_color="#e0a800",
            width=130,
            height=35,
            font=(FONTS['arabic'], 11, "bold")
        )
        barcode_btn.pack(side="left", padx=5)

        # زر إنشاء باركود
        generate_barcode_btn = ctk.CTkButton(
            secondary_actions_frame,
            text="📊 إنشاء باركود",
            command=self.generate_barcode_for_item,
            fg_color=MODERN_COLORS['warning'],
            hover_color="#e0a800",
            width=130,
            height=35,
            font=(FONTS['arabic'], 11, "bold")
        )
        generate_barcode_btn.pack(side="left", padx=5)

        # زر طباعة
        print_btn = ctk.CTkButton(
            secondary_actions_frame,
            text="🖨️ طباعة",
            command=self.print_item,
            fg_color="#6c757d",
            hover_color="#5a6268",
            width=100,
            height=35,
            font=(FONTS['arabic'], 11, "bold")
        )
        print_btn.pack(side="right", padx=5)

    def create_items_table(self, parent):
        """إنشاء جدول الأصناف"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(parent, fg_color=MODERN_COLORS['surface'])
        table_frame.pack(side="left", fill="both", expand=True)

        # عنوان الجدول وأدوات البحث
        table_header = ctk.CTkFrame(table_frame, height=60, fg_color=MODERN_COLORS['primary'])
        table_header.pack(fill="x", padx=10, pady=(10, 0))
        table_header.pack_propagate(False)

        # عنوان الجدول
        table_title = ctk.CTkLabel(
            table_header,
            text="📋 قائمة الأصناف",
            font=(FONTS['arabic'], 16, "bold"),
            text_color="white"
        )
        table_title.pack(side="right", padx=20, pady=15)

        # شريط البحث
        search_frame = ctk.CTkFrame(table_header, fg_color="transparent")
        search_frame.pack(side="left", padx=20, pady=10)

        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="🔍 البحث في الأصناف...",
            width=200,
            font=(FONTS['arabic'], 12)
        )
        self.search_entry.pack(side="left", padx=5)
        self.search_entry.bind("<KeyRelease>", self.on_search)

        # زر تحديث
        refresh_btn = ctk.CTkButton(
            search_frame,
            text="🔄",
            command=self.refresh_items_table,
            fg_color=MODERN_COLORS['info'],
            width=40,
            height=30
        )
        refresh_btn.pack(side="left", padx=5)

        # إطار الجدول الفعلي
        table_container = ctk.CTkFrame(table_frame, fg_color="white")
        table_container.pack(fill="both", expand=True, padx=10, pady=10)

        # إنشاء Treeview للجدول
        columns = (
            "id", "item_code", "name_ar", "name_en", "category",
            "unit", "cost_price", "sale_price", "wholesale_price", "vat_rate", "min_stock", "status"
        )

        self.items_tree = ttk.Treeview(
            table_container,
            columns=columns,
            show="headings",
            height=20
        )

        # تعريف رؤوس الأعمدة
        headers = {
            "id": "الرقم",
            "item_code": "كود الصنف",
            "name_ar": "الاسم (عربي)",
            "name_en": "الاسم (English)",
            "category": "التصنيف",
            "unit": "الوحدة",
            "cost_price": "سعر التكلفة",
            "sale_price": "سعر البيع",
            "wholesale_price": "سعر الجملة",
            "vat_rate": "الضريبة %",
            "min_stock": "الحد الأدنى",
            "status": "الحالة"
        }

        # إعداد رؤوس الأعمدة
        for col, header in headers.items():
            self.items_tree.heading(col, text=header, anchor="center")

        # تحديد عرض الأعمدة
        column_widths = {
            "id": 60,
            "item_code": 100,
            "name_ar": 150,
            "name_en": 150,
            "category": 100,
            "unit": 80,
            "cost_price": 100,
            "sale_price": 100,
            "wholesale_price": 100,
            "vat_rate": 80,
            "min_stock": 80,
            "status": 80
        }

        for col, width in column_widths.items():
            self.items_tree.column(col, width=width, anchor="center")

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=v_scrollbar.set)

        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.items_tree.xview)
        self.items_tree.configure(xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.items_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # إعداد التمدد
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)

        # ربط الأحداث
        self.items_tree.bind("<ButtonRelease-1>", self.on_item_select)
        self.items_tree.bind("<Double-1>", self.on_item_double_click)
        self.items_tree.bind("<Button-3>", self.show_context_menu)

    def get_categories(self):
        """الحصول على قائمة التصنيفات"""
        # بيانات وهمية - يجب استبدالها بالبيانات الحقيقية من قاعدة البيانات
        return ["إلكترونيات", "ملابس", "طعام ومشروبات", "مستلزمات مكتبية", "أدوات منزلية", "كتب ومجلات"]

    def get_units(self):
        """الحصول على قائمة وحدات القياس"""
        # بيانات وهمية - يجب استبدالها بالبيانات الحقيقية من قاعدة البيانات
        return ["", "قطعة", "كيلو", "متر", "لتر", "علبة", "كرتونة", "دزينة", "جرام", "طن", "باوند"]

    def get_warehouses(self):
        """الحصول على قائمة المخازن"""
        # بيانات وهمية - يجب استبدالها بالبيانات الحقيقية من قاعدة البيانات
        return ["", "المخزن الرئيسي", "مخزن الفرع الأول", "مخزن الفرع الثاني", "مخزن المواد الخام", "مخزن المنتجات الجاهزة"]

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.load_items_data()
        self.update_items_count()

    def load_items_data(self):
        """تحميل بيانات الأصناف من قاعدة البيانات"""
        try:
            if self.db_manager:
                # استعلام قاعدة البيانات الحقيقية
                query = """
                SELECT
                    i.id,
                    i.item_code,
                    i.name_ar,
                    i.name_en,
                    c.name as category_name,
                    u.name as unit_name,
                    i.cost_price,
                    i.sale_price,
                    i.min_quantity,
                    i.max_quantity,
                    i.is_active,
                    i.created_at,
                    i.updated_at
                FROM items i
                LEFT JOIN categories c ON i.category_id = c.id
                LEFT JOIN units u ON i.unit_id = u.id
                ORDER BY i.created_at DESC
                """
                self.items_data = self.db_manager.fetch_all(query)
            else:
                # بيانات وهمية للاختبار
                self.items_data = self.get_sample_items_data()

            # عرض البيانات في الجدول
            self.populate_items_table()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الأصناف: {e}")

    def get_sample_items_data(self):
        """بيانات وهمية للاختبار"""
        return [
            # (id, item_code, name_ar, name_en, category, unit, cost_price, sale_price, wholesale_price, vat_rate, min_stock, max_stock, is_active, created_at, updated_at)
            (1, "ITM001", "لابتوب ديل", "Dell Laptop", "إلكترونيات", "قطعة", 2500.00, 3000.00, 2800.00, 15.0, 5, 50, True, "2024-01-15", "2024-01-15"),
            (2, "ITM002", "قميص قطني", "Cotton Shirt", "ملابس", "قطعة", 50.00, 75.00, 65.00, 15.0, 10, 100, True, "2024-01-16", "2024-01-16"),
            (3, "ITM003", "قهوة عربية", "Arabic Coffee", "طعام ومشروبات", "كيلو", 80.00, 120.00, 100.00, 0.0, 20, 200, True, "2024-01-17", "2024-01-17"),
            (4, "ITM004", "دفتر ملاحظات", "Notebook", "مستلزمات مكتبية", "قطعة", 15.00, 25.00, 20.00, 15.0, 50, 500, True, "2024-01-18", "2024-01-18"),
            (5, "ITM005", "مكنسة كهربائية", "Vacuum Cleaner", "أدوات منزلية", "قطعة", 300.00, 450.00, 400.00, 15.0, 3, 30, False, "2024-01-19", "2024-01-19"),
            (6, "ITM006", "خدمة صيانة", "Maintenance Service", "خدمات", "ساعة", 0.00, 100.00, 80.00, 15.0, 0, 0, True, "2024-01-20", "2024-01-20"),
        ]

    def populate_items_table(self):
        """ملء جدول الأصناف بالبيانات"""
        # مسح البيانات السابقة
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة البيانات الجديدة
        for item in self.items_data:
            # تنسيق الحالة
            status_text = "نشط" if item[12] else "غير نشط"  # is_active في الفهرس 12

            # إدراج البيانات
            item_id = self.items_tree.insert("", "end", values=(
                item[0],   # id
                item[1],   # item_code
                item[2],   # name_ar
                item[3],   # name_en
                item[4],   # category
                item[5],   # unit
                f"{item[6]:.2f}",  # cost_price
                f"{item[7]:.2f}",  # sale_price
                f"{item[8]:.2f}",  # wholesale_price
                f"{item[9]:.1f}%", # vat_rate
                f"{item[10]:.0f}", # min_stock
                status_text        # status
            ))

            # تلوين الصف حسب الحالة
            if not item[12]:  # غير نشط
                self.items_tree.set(item_id, "status", status_text)

    def update_items_count(self):
        """تحديث عداد الأصناف"""
        total_items = len(self.items_data)
        active_items = sum(1 for item in self.items_data if item[10])

        self.items_count_label.configure(text=f"إجمالي الأصناف: {total_items}")
        self.active_items_label.configure(text=f"الأصناف النشطة: {active_items}")

    def on_search(self, event):
        """البحث في الأصناف"""
        search_term = self.search_entry.get().lower()

        # تصفية البيانات
        if search_term:
            filtered_data = []
            for item in self.items_data:
                if (search_term in str(item[1]).lower() or  # item_code
                    search_term in str(item[2]).lower() or  # name_ar
                    search_term in str(item[3]).lower() or  # name_en
                    search_term in str(item[4]).lower()):   # category
                    filtered_data.append(item)

            # عرض النتائج المفلترة
            self.display_filtered_items(filtered_data)
        else:
            # عرض جميع البيانات
            self.populate_items_table()

    def display_filtered_items(self, filtered_data):
        """عرض الأصناف المفلترة"""
        # مسح البيانات السابقة
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة البيانات المفلترة
        for item in filtered_data:
            status_text = "نشط" if item[10] else "غير نشط"

            self.items_tree.insert("", "end", values=(
                item[0], item[1], item[2], item[3], item[4], item[5],
                f"{item[6]:.2f}", f"{item[7]:.2f}", f"{item[8]:.0f}",
                f"{item[9]:.0f}", status_text
            ))

    def refresh_items_table(self):
        """تحديث جدول الأصناف"""
        self.load_items_data()
        self.search_entry.delete(0, "end")
        messagebox.showinfo("تحديث", "تم تحديث قائمة الأصناف بنجاح")

    def on_item_select(self, event):
        """عند تحديد صنف من الجدول"""
        selected_item = self.items_tree.selection()
        if selected_item:
            # الحصول على بيانات الصنف المحدد
            item_values = self.items_tree.item(selected_item[0])['values']
            self.current_item_id = item_values[0]

            # ملء النموذج ببيانات الصنف
            self.fill_form_with_item_data(item_values)

            # تفعيل أزرار التعديل والحذف
            self.edit_btn.configure(state="normal")
            self.delete_btn.configure(state="normal")

    def fill_form_with_item_data(self, item_values):
        """ملء النموذج ببيانات الصنف المحدد"""
        # مسح النموذج أولاً
        self.clear_form()

        # ملء الحقول
        self.item_code_entry.insert(0, item_values[1])
        self.name_ar_entry.insert(0, item_values[2])
        self.name_en_entry.insert(0, item_values[3])

        # تحديد التصنيف
        if item_values[4]:
            self.category_id_combo.set(item_values[4])

        # تحديد الوحدة
        if item_values[5]:
            self.unit_id_combo.set(item_values[5])

        # الأسعار
        self.cost_price_entry.insert(0, str(item_values[6]))
        self.sale_price_entry.insert(0, str(item_values[7]))

        # الكميات
        self.min_stock_entry.insert(0, str(item_values[8]))
        self.max_stock_entry.insert(0, str(item_values[9]))

        # الحالة
        if item_values[10] == "نشط":
            self.is_active_checkbox.select()
        else:
            self.is_active_checkbox.deselect()

    def on_item_double_click(self, event):
        """عند النقر المزدوج على صنف"""
        self.on_item_select(event)

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        # إنشاء قائمة سياقية
        context_menu = tk.Menu(self.window, tearoff=0)
        context_menu.add_command(label="تعديل الصنف", command=self.edit_selected_item)
        context_menu.add_command(label="حذف الصنف", command=self.delete_item)
        context_menu.add_separator()
        context_menu.add_command(label="نسخ الكود", command=self.copy_item_code)
        context_menu.add_command(label="عرض التفاصيل", command=self.show_item_details)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def clear_form(self):
        """مسح النموذج"""
        # مسح جميع الحقول النصية
        self.item_code_entry.delete(0, "end")
        self.name_ar_entry.delete(0, "end")
        self.name_en_entry.delete(0, "end")
        self.description_textbox.delete("1.0", "end")
        self.conversion_factor_entry.delete(0, "end")
        self.cost_price_entry.delete(0, "end")
        self.sale_price_entry.delete(0, "end")
        self.wholesale_price_entry.delete(0, "end")
        self.last_purchase_price_entry.delete(0, "end")
        self.vat_rate_entry.delete(0, "end")
        self.min_stock_entry.delete(0, "end")
        self.max_stock_entry.delete(0, "end")
        self.reorder_level_entry.delete(0, "end")

        # إعادة تعيين القوائم المنسدلة
        self.category_id_combo.set("")
        self.unit_id_combo.set("")
        self.secondary_unit_id_combo.set("")
        self.default_warehouse_id_combo.set("")

        # إعادة تعيين مربعات الاختيار
        self.serial_tracking_checkbox.deselect()
        self.batch_tracking_checkbox.deselect()
        self.expiry_date_required_checkbox.deselect()
        self.is_service_checkbox.deselect()
        self.is_active_checkbox.select()  # نشط افتراضياً

        # مسح الصورة
        self.remove_product_image()

        # إعادة تعيين المعرف الحالي
        self.current_item_id = None

        # تعطيل أزرار التعديل والحذف
        self.edit_btn.configure(state="disabled")
        self.delete_btn.configure(state="disabled")

    def validate_form(self):
        """التحقق من صحة بيانات النموذج"""
        errors = []

        # التحقق من الحقول المطلوبة
        if not self.item_code_entry.get().strip():
            errors.append("كود الصنف مطلوب")

        if not self.name_ar_entry.get().strip():
            errors.append("اسم الصنف بالعربية مطلوب")

        # التحقق من معامل التحويل
        try:
            conversion_factor = float(self.conversion_factor_entry.get() or 1)
            if conversion_factor <= 0:
                errors.append("معامل التحويل يجب أن يكون أكبر من صفر")
        except ValueError:
            errors.append("معامل التحويل يجب أن يكون رقماً صحيحاً")

        # التحقق من الأسعار
        price_fields = [
            ("سعر التكلفة", self.cost_price_entry),
            ("سعر البيع", self.sale_price_entry),
            ("سعر الجملة", self.wholesale_price_entry),
            ("آخر سعر شراء", self.last_purchase_price_entry)
        ]

        for field_name, field_entry in price_fields:
            try:
                price = float(field_entry.get() or 0)
                if price < 0:
                    errors.append(f"{field_name} يجب أن يكون أكبر من أو يساوي صفر")
            except ValueError:
                errors.append(f"{field_name} يجب أن يكون رقماً صحيحاً")

        # التحقق من نسبة الضريبة
        try:
            vat_rate = float(self.vat_rate_entry.get() or 0)
            if vat_rate < 0 or vat_rate > 100:
                errors.append("نسبة الضريبة يجب أن تكون بين 0 و 100")
        except ValueError:
            errors.append("نسبة الضريبة يجب أن تكون رقماً صحيحاً")

        # التحقق من كميات المخزون
        stock_fields = [
            ("الحد الأدنى للمخزون", self.min_stock_entry),
            ("الحد الأقصى للمخزون", self.max_stock_entry),
            ("نقطة إعادة الطلب", self.reorder_level_entry)
        ]

        for field_name, field_entry in stock_fields:
            try:
                stock = float(field_entry.get() or 0)
                if stock < 0:
                    errors.append(f"{field_name} يجب أن يكون أكبر من أو يساوي صفر")
            except ValueError:
                errors.append(f"{field_name} يجب أن يكون رقماً صحيحاً")

        # التحقق من المنطق
        try:
            min_stock = float(self.min_stock_entry.get() or 0)
            max_stock = float(self.max_stock_entry.get() or 0)
            reorder_level = float(self.reorder_level_entry.get() or 0)

            if max_stock > 0 and min_stock > max_stock:
                errors.append("الحد الأدنى للمخزون لا يمكن أن يكون أكبر من الحد الأقصى")

            if reorder_level > 0 and min_stock > 0 and reorder_level < min_stock:
                errors.append("نقطة إعادة الطلب يجب أن تكون أكبر من أو تساوي الحد الأدنى")

        except ValueError:
            pass  # الأخطاء ستظهر في التحقق السابق

        return errors

    def save_item(self):
        """حفظ صنف جديد"""
        # التحقق من صحة البيانات
        errors = self.validate_form()
        if errors:
            messagebox.showerror("خطأ في البيانات", "\n".join(errors))
            return

        try:
            # جمع البيانات من النموذج
            item_data = self.get_form_data()

            if self.db_manager:
                # حفظ في قاعدة البيانات الحقيقية
                query = """
                INSERT INTO items (
                    item_code, name_ar, name_en, category_id, unit_id,
                    cost_price, sale_price, min_quantity, max_quantity,
                    is_active, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                params = (
                    item_data['item_code'],
                    item_data['name_ar'],
                    item_data['name_en'],
                    item_data['category_id'],
                    item_data['unit_id'],
                    item_data['cost_price'],
                    item_data['sale_price'],
                    item_data['min_quantity'],
                    item_data['max_quantity'],
                    item_data['is_active'],
                    datetime.now(),
                    datetime.now()
                )

                self.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم حفظ الصنف بنجاح")
            else:
                # محاكاة الحفظ للاختبار
                messagebox.showinfo("نجح", "تم حفظ الصنف بنجاح (وضع الاختبار)")

            # تحديث الجدول ومسح النموذج
            self.refresh_items_table()
            self.clear_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الصنف: {e}")

    def update_item(self):
        """تعديل الصنف المحدد"""
        if not self.current_item_id:
            messagebox.showwarning("تحذير", "يرجى تحديد صنف للتعديل")
            return

        # التحقق من صحة البيانات
        errors = self.validate_form()
        if errors:
            messagebox.showerror("خطأ في البيانات", "\n".join(errors))
            return

        try:
            # جمع البيانات من النموذج
            item_data = self.get_form_data()

            if self.db_manager:
                # تحديث في قاعدة البيانات الحقيقية
                query = """
                UPDATE items SET
                    item_code = ?, name_ar = ?, name_en = ?, category_id = ?,
                    unit_id = ?, cost_price = ?, sale_price = ?, min_quantity = ?,
                    max_quantity = ?, is_active = ?, updated_at = ?
                WHERE id = ?
                """

                params = (
                    item_data['item_code'],
                    item_data['name_ar'],
                    item_data['name_en'],
                    item_data['category_id'],
                    item_data['unit_id'],
                    item_data['cost_price'],
                    item_data['sale_price'],
                    item_data['min_quantity'],
                    item_data['max_quantity'],
                    item_data['is_active'],
                    datetime.now(),
                    self.current_item_id
                )

                self.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم تعديل الصنف بنجاح")
            else:
                # محاكاة التعديل للاختبار
                messagebox.showinfo("نجح", "تم تعديل الصنف بنجاح (وضع الاختبار)")

            # تحديث الجدول ومسح النموذج
            self.refresh_items_table()
            self.clear_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل الصنف: {e}")

    def delete_item(self):
        """حذف الصنف المحدد"""
        if not self.current_item_id:
            messagebox.showwarning("تحذير", "يرجى تحديد صنف للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الصنف رقم {self.current_item_id}؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        )

        if result:
            try:
                if self.db_manager:
                    # حذف من قاعدة البيانات الحقيقية
                    query = "DELETE FROM items WHERE id = ?"
                    self.db_manager.execute_query(query, (self.current_item_id,))
                    messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")
                else:
                    # محاكاة الحذف للاختبار
                    messagebox.showinfo("نجح", "تم حذف الصنف بنجاح (وضع الاختبار)")

                # تحديث الجدول ومسح النموذج
                self.refresh_items_table()
                self.clear_form()

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الصنف: {e}")

    def get_form_data(self):
        """جمع البيانات من النموذج"""
        # نسخ الصورة إلى مجلد الأصول إذا كانت موجودة
        image_path = None
        if self.image_path:
            image_path = self.copy_image_to_assets(self.image_path)

        return {
            'item_code': self.item_code_entry.get().strip(),
            'name_ar': self.name_ar_entry.get().strip(),
            'name_en': self.name_en_entry.get().strip(),
            'description': self.description_textbox.get("1.0", "end-1c").strip(),
            'category_id': self.get_category_id_by_name(self.category_id_combo.get()),
            'unit_id': self.get_unit_id_by_name(self.unit_id_combo.get()),
            'secondary_unit_id': self.get_unit_id_by_name(self.secondary_unit_id_combo.get()),
            'conversion_factor': float(self.conversion_factor_entry.get() or 1),
            'default_warehouse_id': self.get_warehouse_id_by_name(self.default_warehouse_id_combo.get()),
            'cost_price': float(self.cost_price_entry.get() or 0),
            'sale_price': float(self.sale_price_entry.get() or 0),
            'wholesale_price': float(self.wholesale_price_entry.get() or 0),
            'last_purchase_price': float(self.last_purchase_price_entry.get() or 0),
            'vat_rate': float(self.vat_rate_entry.get() or 0),
            'min_stock': float(self.min_stock_entry.get() or 0),
            'max_stock': float(self.max_stock_entry.get() or 0),
            'reorder_level': float(self.reorder_level_entry.get() or 0),
            'serial_tracking': self.serial_tracking_checkbox.get(),
            'batch_tracking': self.batch_tracking_checkbox.get(),
            'expiry_date_required': self.expiry_date_required_checkbox.get(),
            'is_service': self.is_service_checkbox.get(),
            'is_active': self.is_active_checkbox.get(),
            'image_path': image_path
        }

    def get_category_id_by_name(self, category_name):
        """الحصول على معرف التصنيف من الاسم"""
        # هذه دالة مؤقتة - يجب استبدالها بالاستعلام الحقيقي
        categories_map = {
            "إلكترونيات": 1,
            "ملابس": 2,
            "طعام ومشروبات": 3,
            "مستلزمات مكتبية": 4,
            "أدوات منزلية": 5,
            "كتب ومجلات": 6
        }
        return categories_map.get(category_name, 1)

    def get_unit_id_by_name(self, unit_name):
        """الحصول على معرف الوحدة من الاسم"""
        # هذه دالة مؤقتة - يجب استبدالها بالاستعلام الحقيقي
        units_map = {
            "": None,
            "قطعة": 1,
            "كيلو": 2,
            "متر": 3,
            "لتر": 4,
            "علبة": 5,
            "كرتونة": 6,
            "دزينة": 7,
            "جرام": 8,
            "طن": 9,
            "باوند": 10
        }
        return units_map.get(unit_name, None)

    def get_warehouse_id_by_name(self, warehouse_name):
        """الحصول على معرف المخزن من الاسم"""
        # هذه دالة مؤقتة - يجب استبدالها بالاستعلام الحقيقي
        warehouses_map = {
            "": None,
            "المخزن الرئيسي": 1,
            "مخزن الفرع الأول": 2,
            "مخزن الفرع الثاني": 3,
            "مخزن المواد الخام": 4,
            "مخزن المنتجات الجاهزة": 5
        }
        return warehouses_map.get(warehouse_name, None)

    def edit_selected_item(self):
        """تعديل الصنف المحدد من القائمة السياقية"""
        selected_item = self.items_tree.selection()
        if selected_item:
            self.on_item_select(None)
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد صنف للتعديل")

    def copy_item_code(self):
        """نسخ كود الصنف المحدد"""
        selected_item = self.items_tree.selection()
        if selected_item:
            item_values = self.items_tree.item(selected_item[0])['values']
            item_code = item_values[1]
            self.window.clipboard_clear()
            self.window.clipboard_append(item_code)
            messagebox.showinfo("نسخ", f"تم نسخ كود الصنف: {item_code}")
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد صنف أولاً")

    def show_item_details(self):
        """عرض تفاصيل الصنف المحدد"""
        selected_item = self.items_tree.selection()
        if selected_item:
            item_values = self.items_tree.item(selected_item[0])['values']

            details_window = ctk.CTkToplevel(self.window)
            details_window.title("تفاصيل الصنف")
            details_window.geometry("500x600")
            details_window.configure(fg_color=MODERN_COLORS['background'])

            # عنوان النافذة
            title_label = ctk.CTkLabel(
                details_window,
                text=f"تفاصيل الصنف: {item_values[2]}",
                font=(FONTS['arabic'], 18, "bold")
            )
            title_label.pack(pady=20)

            # إطار التفاصيل
            details_frame = ctk.CTkScrollableFrame(details_window)
            details_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # عرض التفاصيل
            details = [
                ("الرقم:", item_values[0]),
                ("كود الصنف:", item_values[1]),
                ("الاسم (عربي):", item_values[2]),
                ("الاسم (English):", item_values[3]),
                ("التصنيف:", item_values[4]),
                ("الوحدة:", item_values[5]),
                ("سعر التكلفة:", f"{item_values[6]} ريال"),
                ("سعر البيع:", f"{item_values[7]} ريال"),
                ("الحد الأدنى:", item_values[8]),
                ("الحد الأقصى:", item_values[9]),
                ("الحالة:", item_values[10])
            ]

            for label, value in details:
                detail_frame = ctk.CTkFrame(details_frame, fg_color="transparent")
                detail_frame.pack(fill="x", padx=10, pady=5)

                ctk.CTkLabel(detail_frame, text=str(value), font=(FONTS['arabic'], 12)).pack(side="left", padx=10)
                ctk.CTkLabel(detail_frame, text=label, font=(FONTS['arabic'], 12, "bold")).pack(side="right", padx=10)
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد صنف أولاً")

    def open_barcode_scanner(self):
        """فتح نافذة قارئ الباركود"""
        if BarcodeScannerWindow is None:
            messagebox.showerror(
                "خطأ",
                "قارئ الباركود غير متاح.\nيرجى تثبيت المكتبات المطلوبة:\npip install opencv-python pyzbar"
            )
            return

        try:
            # إنشاء نافذة قارئ الباركود
            scanner_window = BarcodeScannerWindow(
                parent=self.window,
                callback_function=self.on_barcode_scanned
            )
            scanner_window.show_scanner_window()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تشغيل قارئ الباركود: {e}")

    def on_barcode_scanned(self, code, barcode_type):
        """عند قراءة باركود جديد"""
        try:
            # وضع الكود في حقل كود الصنف
            self.item_code_entry.delete(0, "end")
            self.item_code_entry.insert(0, code)

            # البحث عن الصنف تلقائياً
            self.search_item_by_code()

            # إظهار رسالة نجح
            messagebox.showinfo("نجح", f"تم قراءة الباركود بنجاح: {code}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في معالجة الباركود: {e}")

    def search_item_by_code(self):
        """البحث عن صنف بالكود"""
        code = self.item_code_entry.get().strip()
        if not code:
            messagebox.showwarning("تحذير", "يرجى إدخال كود الصنف أولاً")
            return

        try:
            # البحث في البيانات الموجودة
            found_item = None
            for item in self.items_data:
                if item[1] == code:  # item_code
                    found_item = item
                    break

            if found_item:
                # ملء النموذج ببيانات الصنف الموجود
                self.fill_form_with_found_item(found_item)
                messagebox.showinfo("تم العثور", f"تم العثور على الصنف: {found_item[2]}")
            else:
                # الصنف غير موجود
                result = messagebox.askyesno(
                    "صنف جديد",
                    f"الصنف بالكود {code} غير موجود.\nهل تريد إضافته كصنف جديد؟"
                )
                if result:
                    # مسح النموذج للإضافة الجديدة
                    self.clear_form()
                    self.item_code_entry.insert(0, code)
                    # التركيز على حقل الاسم
                    self.name_ar_entry.focus()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {e}")

    def fill_form_with_found_item(self, item_data):
        """ملء النموذج ببيانات الصنف الموجود"""
        # مسح النموذج أولاً
        self.clear_form()

        # ملء الحقول
        self.item_code_entry.insert(0, item_data[1])
        self.name_ar_entry.insert(0, item_data[2])
        self.name_en_entry.insert(0, item_data[3])

        # تحديد التصنيف
        if item_data[4]:
            self.category_id_combo.set(item_data[4])

        # تحديد الوحدة
        if item_data[5]:
            self.unit_id_combo.set(item_data[5])

        # الأسعار
        self.cost_price_entry.insert(0, str(item_data[6]))
        self.sale_price_entry.insert(0, str(item_data[7]))

        # الكميات
        self.min_stock_entry.insert(0, str(item_data[8]))
        self.max_stock_entry.insert(0, str(item_data[9]))

        # الحالة
        if item_data[10]:  # is_active
            self.is_active_checkbox.select()
        else:
            self.is_active_checkbox.deselect()

        # تعيين المعرف الحالي
        self.current_item_id = item_data[0]

        # تفعيل أزرار التعديل والحذف
        self.edit_btn.configure(state="normal")
        self.delete_btn.configure(state="disabled")  # منع الحذف عند البحث

    def generate_barcode_for_item(self):
        """توليد باركود للصنف الحالي"""
        try:
            import barcode
            from barcode.writer import ImageWriter
            from tkinter import filedialog

            # التحقق من وجود كود الصنف
            item_code = self.item_code_entry.get().strip()
            if not item_code:
                messagebox.showwarning("تحذير", "يرجى إدخال كود الصنف أولاً")
                return

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("All files", "*.*")],
                title="حفظ الباركود",
                initialname=f"barcode_{item_code}"
            )

            if filename:
                # إنشاء الباركود
                code128 = barcode.get_barcode_class('code128')
                barcode_instance = code128(item_code, writer=ImageWriter())

                # حفظ الباركود
                barcode_instance.save(filename.replace('.png', ''))

                messagebox.showinfo("نجح", f"تم حفظ الباركود في: {filename}")

        except ImportError:
            messagebox.showerror(
                "خطأ",
                "مكتبة إنشاء الباركود غير مثبتة.\nيرجى تثبيتها:\npip install python-barcode[images]"
            )
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء الباركود: {e}")

    def select_product_image(self):
        """اختيار صورة للمنتج"""
        try:
            from PIL import Image, ImageTk

            # اختيار ملف الصورة
            file_path = filedialog.askopenfilename(
                title="اختيار صورة المنتج",
                filetypes=[
                    ("صور", "*.png *.jpg *.jpeg *.gif *.bmp"),
                    ("PNG", "*.png"),
                    ("JPEG", "*.jpg *.jpeg"),
                    ("جميع الملفات", "*.*")
                ]
            )

            if file_path:
                # تحميل وعرض الصورة
                self.load_and_display_image(file_path)
                self.image_path = file_path
                messagebox.showinfo("نجح", "تم تحديد صورة المنتج بنجاح")

        except ImportError:
            messagebox.showerror("خطأ", "مكتبة Pillow غير مثبتة.\nيرجى تثبيتها: pip install Pillow")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في اختيار الصورة: {e}")

    def load_and_display_image(self, image_path):
        """تحميل وعرض الصورة"""
        try:

            # فتح الصورة
            image = Image.open(image_path)

            # تغيير حجم الصورة
            image = image.resize((140, 140), Image.Resampling.LANCZOS)

            # تحويل للعرض في tkinter
            photo = ImageTk.PhotoImage(image)

            # عرض الصورة
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # حفظ مرجع لمنع حذف الصورة

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الصورة: {e}")

    def remove_product_image(self):
        """حذف صورة المنتج"""
        self.image_path = None
        self.image_label.configure(image="", text="📷 لا توجد صورة")
        if hasattr(self.image_label, 'image'):
            delattr(self.image_label, 'image')
        messagebox.showinfo("تم", "تم حذف صورة المنتج")

    def copy_image_to_assets(self, source_path):
        """نسخ الصورة إلى مجلد الأصول"""
        try:
            import shutil
            import os

            # إنشاء مجلد الصور إذا لم يكن موجود
            assets_dir = "assets/product_images"
            os.makedirs(assets_dir, exist_ok=True)

            # إنشاء اسم ملف فريد
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_extension = os.path.splitext(source_path)[1]
            item_code = self.item_code_entry.get().strip() or "unknown"
            new_filename = f"{item_code}_{timestamp}{file_extension}"

            # المسار الجديد
            new_path = os.path.join(assets_dir, new_filename)

            # نسخ الملف من مسار المصدر إلى مسار الوجهة مع الحفاظ على كافة بيانات الملف مثل التاريخ والوقت
            shutil.copy2(source_path, new_path)

            # إرجاع المسار الجديد للملف المنسوخ
            return new_path

        except Exception as e:
            # في حالة حدوث أي خطأ أثناء النسخ، إظهار رسالة خطأ تحتوي على تفاصيل الخطأ للمستخدم
            messagebox.showerror("خطأ", f"خطأ في نسخ الصورة: {e}")
            # إرجاع مسار الملف الأصلي في حالة الفشل
            return source_path

    def scan_barcode(self):
        """
        تشغيل قارئ الباركود لقراءة رمز الباركود باستخدام مكتبات خارجية

        يقوم هذا الدالة بفتح نافذة قارئ الباركود لالتقاط رمز الباركود من خلال الكاميرا أو صورة،
        ثم تعبئة حقل "كود الصنف" في الواجهة بالنتيجة المقروءة.

        Parameters:
        None

        Returns:
        None
        """
        try:
            # استيراد فئة BarcodeScanner من الوحدة core.barcode_scanner ديناميكياً لتشغيل قارئ الباركود
            from core.barcode_scanner import BarcodeScanner
            scanner = BarcodeScanner()

            # تشغيل نافذة مسح الباركود وانتظار الحصول على نتيجة القراءة
            result = scanner.scan_barcode()

            if result:
                # تنظيف حقل إدخال كود الصنف من المحتويات السابقة
                self.item_code_entry.delete(0, "end")
                # إدخال الكود الذي تم قراءته في حقل النص الخاص به
                self.item_code_entry.insert(0, result)
                # إخطار المستخدم بنجاح عملية القراءة مع عرض الكود المقروء
                messagebox.showinfo("نجح", f"تم قراءة الباركود: {result}")

        except ImportError:
            # في حالة عدم وجود مكتبات القراءة المطلوبة، إظهار رسالة توضح كيفية تثبيتها
            messagebox.showerror(
                "خطأ",
                "مكتبات قارئ الباركود غير مثبتة\nيرجى تثبيتها:\npip install opencv-python pyzbar"
            )
        except Exception as e:
            # في حالة حدوث أي خطأ غير متوقع أثناء عملية المسح، إظهار رسالة توضح الخطأ
            messagebox.showerror("خطأ", f"خطأ في قارئ الباركود: {e}")

    def cancel_operation(self):
        """إلغاء العملية الحالية"""
        try:
            # التحقق من وجود تغييرات غير محفوظة
            if self.has_unsaved_changes():
                result = messagebox.askyesnocancel(
                    "تأكيد الإلغاء",
                    "هناك تغييرات غير محفوظة.\n\nهل تريد حفظ التغييرات قبل الإلغاء؟",
                    icon="warning"
                )

                if result is True:  # نعم - احفظ ثم ألغي
                    self.save_item()
                    return
                elif result is False:  # لا - ألغي بدون حفظ
                    pass
                else:  # إلغاء - لا تفعل شيئاً
                    return

            # مسح النموذج والعودة للحالة الافتراضية
            self.clear_form()
            self.current_item_id = None

            # تعطيل أزرار التعديل والحذف
            self.edit_btn.configure(state="disabled")
            self.delete_btn.configure(state="disabled")

            # تفعيل زر الحفظ والجديد
            self.save_btn.configure(state="normal")
            self.new_btn.configure(state="normal")

            # رسالة تأكيد
            messagebox.showinfo("تم الإلغاء", "تم إلغاء العملية بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إلغاء العملية: {e}")

    def has_unsaved_changes(self):
        """التحقق من وجود تغييرات غير محفوظة"""
        try:
            # التحقق من الحقول الأساسية
            if (self.item_code_entry.get().strip() or
                self.name_ar_entry.get().strip() or
                self.name_en_entry.get().strip()):
                return True

            # التحقق من الحقول الرقمية
            if (self.cost_price_entry.get().strip() or
                self.sale_price_entry.get().strip()):
                return True

            return False

        except Exception:
            return False

    def print_item(self):
        """طباعة بيانات الصنف"""
        try:
            # التحقق من وجود صنف محدد أو بيانات في النموذج
            if not self.current_item_id and not self.has_unsaved_changes():
                messagebox.showwarning("تحذير", "لا توجد بيانات للطباعة")
                return

            # جمع البيانات للطباعة
            if self.current_item_id:
                # طباعة الصنف المحدد من قاعدة البيانات
                item_data = self.get_item_by_id(self.current_item_id)
            else:
                # طباعة البيانات الحالية في النموذج
                item_data = self.get_form_data()

            # إنشاء تقرير الطباعة
            self.generate_item_report(item_data)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الطباعة: {e}")

    def get_item_by_id(self, item_id):
        """جلب بيانات الصنف من قاعدة البيانات"""
        try:
            if self.db_manager:
                query = """
                SELECT i.*, c.name_ar as category_name, u.name_ar as unit_name
                FROM items i
                LEFT JOIN item_categories c ON i.category_id = c.id
                LEFT JOIN units u ON i.unit_id = u.id
                WHERE i.id = ?
                """
                result = self.db_manager.fetch_one(query, (item_id,))
                return result
            return None

        except Exception as e:
            print(f"خطأ في جلب بيانات الصنف: {e}")
            return None

    def generate_item_report(self, item_data):
        """إنشاء تقرير طباعة للصنف"""
        try:
            import tempfile
            from PIL import Image
            from PIL import ImageTk
            from tkinter import filedialog
            import os

            # إنشاء محتوى التقرير
            report_content = f"""
═══════════════════════════════════════════════════════════════
                            تقرير بيانات الصنف
═══════════════════════════════════════════════════════════════

📦 كود الصنف: {item_data.get('item_code', 'غير محدد')}
📝 اسم الصنف (عربي): {item_data.get('name_ar', 'غير محدد')}
📝 اسم الصنف (English): {item_data.get('name_en', 'غير محدد')}
📋 الوصف: {item_data.get('description', 'غير محدد')}

═══════════════════════════════════════════════════════════════
                            معلومات التصنيف
═══════════════════════════════════════════════════════════════

📂 التصنيف: {item_data.get('category_name', 'غير محدد')}
📏 وحدة القياس: {item_data.get('unit_name', 'غير محدد')}

═══════════════════════════════════════════════════════════════
                            معلومات الأسعار
═══════════════════════════════════════════════════════════════

💰 سعر التكلفة: {item_data.get('cost_price', 0)} ريال
💰 سعر البيع: {item_data.get('sale_price', 0)} ريال
💰 سعر الجملة: {item_data.get('wholesale_price', 0)} ريال

═══════════════════════════════════════════════════════════════
                            معلومات المخزون
═══════════════════════════════════════════════════════════════

📊 الحد الأدنى: {item_data.get('min_stock', 0)}
📊 الحد الأقصى: {item_data.get('max_stock', 0)}
📊 نقطة إعادة الطلب: {item_data.get('reorder_level', 0)}

═══════════════════════════════════════════════════════════════
تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
شركة ست الكل العالمية - برنامج المحاسبة المالية
═══════════════════════════════════════════════════════════════
            """

            # حفظ التقرير في ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(report_content)
                temp_file = f.name

            # فتح التقرير للطباعة
            os.startfile(temp_file)

            messagebox.showinfo("نجح", "تم إنشاء تقرير الطباعة بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {e}")

    def close_window(self):
        """إغلاق النافذة مع التحقق من التغييرات غير المحفوظة"""
        try:
            # التحقق من وجود تغييرات غير محفوظة قبل الإغلاق
            if self.has_unsaved_changes():
                result = messagebox.askyesnocancel(
                    "تأكيد الإغلاق",
                    "هناك تغييرات غير محفوظة.\n\nهل تريد حفظ التغييرات قبل الإغلاق؟",
                    icon="warning"
                )

                if result is True:  # نعم - احفظ ثم أغلق
                    self.save_item()
                elif result is False:  # لا - أغلق بدون حفظ
                    pass
                else:  # إلغاء - لا تغلق
                    return

            # تدمير النافذة الحالية وإغلاقها نهائياً
            self.if window and hasattr(window, "destroy"):
    if window and hasattr(window, "destroy"):
    window.destroy()

        except Exception as e:
            print(f"خطأ في إغلاق النافذة: {e}")
            self.if window and hasattr(window, "destroy"):
    if window and hasattr(window, "destroy"):
    window.destroy()