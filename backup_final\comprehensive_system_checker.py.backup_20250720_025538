#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فاحص النظام الشامل والمنهجي
Comprehensive System Checker and Analyzer
"""

import ast
import sys
import os
import re
import json
import sqlite3
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

class ComprehensiveSystemChecker:
    """فاحص النظام الشامل والمنهجي"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "project_structure": {},
            "syntax_errors": [],
            "import_errors": [],
            "database_issues": [],
            "ui_issues": [],
            "performance_issues": [],
            "security_issues": [],
            "recommendations": [],
            "fixed_issues": [],
            "test_results": {},
            "overall_status": "unknown"
        }
        
        # إعداد نظام السجلات
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/system_check.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # الملفات الحرجة
        self.critical_files = [
            "main.py",
            "ui/main_window.py",
            "database/hybrid_database_manager.py",
            "services/sales_manager.py",
            "core/scheduler_manager.py",
            "themes/theme_manager.py",
            "auth/auth_manager.py"
        ]
        
        # الوحدات المطلوبة
        self.required_modules = [
            "customtkinter", "tkinter", "sqlite3", "pathlib",
            "datetime", "json", "logging", "traceback"
        ]
    
    def run_comprehensive_check(self):
        """تشغيل الفحص الشامل"""
        print("🔍 بدء الفحص الشامل للنظام...")
        print("=" * 60)
        
        # 1. فحص هيكل المشروع
        self.check_project_structure()
        
        # 2. فحص الأخطاء النحوية
        self.check_syntax_errors()
        
        # 3. فحص الاستيرادات
        self.check_import_errors()
        
        # 4. فحص قاعدة البيانات
        self.check_database_integrity()
        
        # 5. فحص واجهة المستخدم
        self.check_ui_issues()
        
        # 6. اختبار الوحدات الأساسية
        self.test_core_modules()
        
        # 7. إصلاح الأخطاء المكتشفة
        self.fix_discovered_issues()
        
        # 8. إنشاء التقرير النهائي
        self.generate_final_report()
        
        return self.report
    
    def check_project_structure(self):
        """فحص هيكل المشروع"""
        print("\n📁 فحص هيكل المشروع...")
        
        required_dirs = ["ui", "database", "services", "core", "themes", "auth", "config"]
        missing_dirs = []
        
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if not dir_path.exists():
                missing_dirs.append(dir_name)
            else:
                # فحص الملفات في كل مجلد
                py_files = list(dir_path.glob("*.py"))
                self.report["project_structure"][dir_name] = {
                    "exists": True,
                    "files_count": len(py_files),
                    "files": [f.name for f in py_files]
                }
        
        if missing_dirs:
            self.report["project_structure"]["missing_directories"] = missing_dirs
            print(f"⚠️  مجلدات مفقودة: {missing_dirs}")
        else:
            print("✅ هيكل المشروع سليم")
    
    def check_syntax_errors(self):
        """فحص الأخطاء النحوية"""
        print("\n🔍 فحص الأخطاء النحوية...")
        
        python_files = list(self.project_root.rglob("*.py"))
        syntax_errors = []
        
        for file_path in python_files:
            if any(skip in str(file_path) for skip in ["__pycache__", ".git", "venv"]):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # محاولة تحليل الملف نحوياً
                try:
                    ast.parse(content)
                except SyntaxError as e:
                    error_info = {
                        "file": str(file_path),
                        "line": e.lineno,
                        "column": e.offset,
                        "error": str(e),
                        "severity": "high" if str(file_path) in self.critical_files else "medium"
                    }
                    syntax_errors.append(error_info)
                    print(f"❌ خطأ نحوي في {file_path.name}: السطر {e.lineno}")
                    
            except Exception as e:
                print(f"⚠️  خطأ في قراءة {file_path}: {e}")
        
        self.report["syntax_errors"] = syntax_errors
        if not syntax_errors:
            print("✅ لا توجد أخطاء نحوية")
    
    def check_import_errors(self):
        """فحص أخطاء الاستيراد"""
        print("\n📦 فحص أخطاء الاستيراد...")
        
        import_errors = []
        
        # فحص الملفات الحرجة
        for file_path in self.critical_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                import_errors.append({
                    "file": file_path,
                    "error": "الملف غير موجود",
                    "severity": "critical"
                })
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # البحث عن استيرادات مشكوك فيها
                problematic_patterns = [
                    r'except ImportError:\s*pass',  # except فارغ
                    r'from\s+\w+\s+import\s+\*',   # import *
                    r'import\s+\w+\s*\n\s*except',  # import متبوع بـ except مباشرة
                ]
                
                for pattern in problematic_patterns:
                    if re.search(pattern, content):
                        import_errors.append({
                            "file": file_path,
                            "error": f"نمط استيراد مشكوك فيه: {pattern}",
                            "severity": "medium"
                        })
                        
            except Exception as e:
                import_errors.append({
                    "file": file_path,
                    "error": f"خطأ في قراءة الملف: {e}",
                    "severity": "high"
                })
        
        self.report["import_errors"] = import_errors
        if not import_errors:
            print("✅ لا توجد مشاكل في الاستيرادات")
    
    def check_database_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        print("\n🗄️  فحص سلامة قاعدة البيانات...")
        
        db_issues = []
        db_path = self.project_root / "database" / "accounting.db"
        
        if not db_path.exists():
            db_issues.append({
                "issue": "ملف قاعدة البيانات غير موجود",
                "severity": "critical",
                "file": str(db_path)
            })
        else:
            try:
                # فحص الاتصال بقاعدة البيانات
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # فحص الجداول الأساسية
                required_tables = ["users", "products", "invoices", "customers"]
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                existing_tables = [row[0] for row in cursor.fetchall()]
                
                missing_tables = [table for table in required_tables if table not in existing_tables]
                if missing_tables:
                    db_issues.append({
                        "issue": f"جداول مفقودة: {missing_tables}",
                        "severity": "high"
                    })
                
                conn.close()
                
            except Exception as e:
                db_issues.append({
                    "issue": f"خطأ في الاتصال بقاعدة البيانات: {e}",
                    "severity": "critical"
                })
        
        self.report["database_issues"] = db_issues
        if not db_issues:
            print("✅ قاعدة البيانات سليمة")
    
    def check_ui_issues(self):
        """فحص مشاكل واجهة المستخدم"""
        print("\n🖥️  فحص مشاكل واجهة المستخدم...")
        
        ui_issues = []
        ui_files = list((self.project_root / "ui").glob("*.py"))
        
        for file_path in ui_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص مشاكل customtkinter الشائعة
                if 'customtkinter' in content:
                    # فحص استخدام after بدون معالجة أخطاء
                    if re.search(r'\.after\(\d+', content) and 'try:' not in content:
                        ui_issues.append({
                            "file": str(file_path),
                            "issue": "استخدام after بدون معالجة أخطاء",
                            "severity": "medium"
                        })
                    
                    # فحص استخدام destroy بدون فحص
                    if '.destroy()' in content and 'if' not in content:
                        ui_issues.append({
                            "file": str(file_path),
                            "issue": "استخدام destroy بدون فحص الحالة",
                            "severity": "low"
                        })
                        
            except Exception as e:
                ui_issues.append({
                    "file": str(file_path),
                    "issue": f"خطأ في قراءة الملف: {e}",
                    "severity": "high"
                })
        
        self.report["ui_issues"] = ui_issues
        if not ui_issues:
            print("✅ لا توجد مشاكل في واجهة المستخدم")
    
    def test_core_modules(self):
        """اختبار الوحدات الأساسية"""
        print("\n🧪 اختبار الوحدات الأساسية...")
        
        test_results = {}
        
        # اختبار استيراد الوحدات المطلوبة
        for module in self.required_modules:
            try:
                __import__(module)
                test_results[module] = {"status": "success", "error": None}
                print(f"✅ {module}")
            except ImportError as e:
                test_results[module] = {"status": "failed", "error": str(e)}
                print(f"❌ {module}: {e}")
        
        # اختبار الملفات الحرجة
        for file_path in self.critical_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    # محاولة تحليل الملف
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    ast.parse(content)
                    test_results[file_path] = {"status": "success", "error": None}
                    print(f"✅ {file_path}")
                except Exception as e:
                    test_results[file_path] = {"status": "failed", "error": str(e)}
                    print(f"❌ {file_path}: {e}")
            else:
                test_results[file_path] = {"status": "missing", "error": "الملف غير موجود"}
                print(f"⚠️  {file_path}: الملف غير موجود")
        
        self.report["test_results"] = test_results
    
    def fix_discovered_issues(self):
        """إصلاح الأخطاء المكتشفة"""
        print("\n🔧 إصلاح الأخطاء المكتشفة...")
        
        fixed_issues = []
        
        # إصلاح الأخطاء النحوية البسيطة
        for error in self.report["syntax_errors"]:
            if self.fix_syntax_error(error):
                fixed_issues.append(error)
        
        # إصلاح مشاكل الاستيراد
        for error in self.report["import_errors"]:
            if self.fix_import_error(error):
                fixed_issues.append(error)
        
        self.report["fixed_issues"] = fixed_issues
        print(f"✅ تم إصلاح {len(fixed_issues)} مشكلة")
    
    def fix_syntax_error(self, error):
        """إصلاح خطأ نحوي محدد"""
        # هذه دالة مبسطة - يمكن توسيعها
        return False
    
    def fix_import_error(self, error):
        """إصلاح خطأ استيراد محدد"""
        # هذه دالة مبسطة - يمكن توسيعها
        return False
    
    def generate_final_report(self):
        """إنشاء التقرير النهائي"""
        print("\n📊 إنشاء التقرير النهائي...")
        
        # حساب الحالة العامة
        total_issues = (
            len(self.report["syntax_errors"]) +
            len(self.report["import_errors"]) +
            len(self.report["database_issues"]) +
            len(self.report["ui_issues"])
        )
        
        if total_issues == 0:
            self.report["overall_status"] = "excellent"
        elif total_issues <= 5:
            self.report["overall_status"] = "good"
        elif total_issues <= 15:
            self.report["overall_status"] = "fair"
        else:
            self.report["overall_status"] = "needs_attention"
        
        # إضافة التوصيات
        self.add_recommendations()
        
        # حفظ التقرير
        report_file = f"comprehensive_system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 تم حفظ التقرير في: {report_file}")
    
    def add_recommendations(self):
        """إضافة التوصيات"""
        recommendations = []
        
        if self.report["syntax_errors"]:
            recommendations.append("إصلاح الأخطاء النحوية فوراً")
        
        if self.report["database_issues"]:
            recommendations.append("فحص وإصلاح قاعدة البيانات")
        
        if self.report["import_errors"]:
            recommendations.append("مراجعة وتنظيم الاستيرادات")
        
        recommendations.extend([
            "إجراء نسخة احتياطية قبل أي تعديلات",
            "اختبار جميع الوحدات بعد الإصلاحات",
            "تحديث التوثيق والتعليقات"
        ])
        
        self.report["recommendations"] = recommendations

def main():
    """تشغيل الفاحص الشامل"""
    checker = ComprehensiveSystemChecker()
    report = checker.run_comprehensive_check()
    
    print("\n" + "="*60)
    print("📊 ملخص النتائج:")
    print(f"   🔍 أخطاء نحوية: {len(report['syntax_errors'])}")
    print(f"   📦 مشاكل استيراد: {len(report['import_errors'])}")
    print(f"   🗄️  مشاكل قاعدة البيانات: {len(report['database_issues'])}")
    print(f"   🖥️  مشاكل واجهة المستخدم: {len(report['ui_issues'])}")
    print(f"   🔧 مشاكل تم إصلاحها: {len(report['fixed_issues'])}")
    print(f"   📊 الحالة العامة: {report['overall_status']}")
    print("="*60)

if __name__ == "__main__":
    main()
