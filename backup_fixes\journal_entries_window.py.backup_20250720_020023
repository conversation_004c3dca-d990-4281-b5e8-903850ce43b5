# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة القيود المحاسبية
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from themes.modern_theme import MODERN_COLORS, FONTS
from database.journal_entries_manager import JournalEntriesManager
from database.accounts_manager import AccountsManager
from ui.window_utils import configure_window_fullscreen
from datetime import date

class JournalEntriesWindow:
    """نافذة القيود المحاسبية"""
    
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.journal_manager = JournalEntriesManager()
        self.accounts_manager = AccountsManager()
        self.entry_details = []
        self.accounts_list = []
        
        self.create_window()
        self.load_accounts()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = ctk.CTkToplevel(self.parent)
        
        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "القيود المحاسبية - برنامج ست الكل للمحاسبة")
        self.window.configure(fg_color=MODERN_COLORS['background'])
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إنشاء المحتوى
        self.create_header()
        self.create_main_content()
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = ctk.CTkFrame(self.window, height=80, fg_color=MODERN_COLORS['primary'])
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = ctk.CTkLabel(
            header_frame,
            text="📊 القيود المحاسبية",
            font=ctk.CTkFont(family=FONTS['arabic'], size=24, weight="bold"),
            text_color="white"
        )
        title_label.pack(side="right", padx=20, pady=20)
        
        # زر إغلاق
        close_btn = ctk.CTkButton(
            header_frame,
            text="✖ إغلاق",
            width=100,
            command=self.close_window,
            fg_color=MODERN_COLORS['error']
        )
        close_btn.pack(side="left", padx=20, pady=20)
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار رئيسي
        main_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إطار إدخال القيد
        self.create_entry_form(main_frame)
        
        # إطار تفاصيل القيد
        self.create_details_section(main_frame)
        
        # إطار الأزرار
        self.create_buttons_section(main_frame)
    
    def create_entry_form(self, parent):
        """إنشاء نموذج إدخال القيد"""
        form_frame = ctk.CTkFrame(parent)
        form_frame.pack(fill="x", pady=(0, 10))
        
        # عنوان القسم
        section_title = ctk.CTkLabel(
            form_frame,
            text="بيانات القيد",
            font=ctk.CTkFont(family=FONTS['arabic'], size=18, weight="bold")
        )
        section_title.pack(pady=10)
        
        # إطار الحقول
        fields_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        fields_frame.pack(fill="x", padx=20, pady=10)
        
        # الصف الأول
        row1 = ctk.CTkFrame(fields_frame, fg_color="transparent")
        row1.pack(fill="x", pady=5)
        
        # تاريخ القيد
        date_label = ctk.CTkLabel(row1, text="تاريخ القيد:", font=ctk.CTkFont(family=FONTS['arabic'], size=12))
        date_label.pack(side="right", padx=10)
        
        self.entry_date = ctk.CTkEntry(row1, width=150, placeholder_text="YYYY-MM-DD")
        self.entry_date.pack(side="right", padx=10)
        self.entry_date.insert(0, date.today().strftime("%Y-%m-%d"))
        
        # رقم القيد (للعرض فقط)
        number_label = ctk.CTkLabel(row1, text="رقم القيد:", font=ctk.CTkFont(family=FONTS['arabic'], size=12))
        number_label.pack(side="left", padx=10)
        
        self.entry_number = ctk.CTkLabel(row1, text="سيتم توليده تلقائياً", 
                                        font=ctk.CTkFont(family=FONTS['arabic'], size=12))
        self.entry_number.pack(side="left", padx=10)
        
        # الصف الثاني
        row2 = ctk.CTkFrame(fields_frame, fg_color="transparent")
        row2.pack(fill="x", pady=5)
        
        # وصف القيد
        desc_label = ctk.CTkLabel(row2, text="وصف القيد:", font=ctk.CTkFont(family=FONTS['arabic'], size=12))
        desc_label.pack(side="right", padx=10)
        
        self.entry_description = ctk.CTkEntry(row2, width=400, placeholder_text="وصف القيد المحاسبي")
        self.entry_description.pack(side="right", padx=10)
    
    def create_details_section(self, parent):
        """إنشاء قسم تفاصيل القيد"""
        details_frame = ctk.CTkFrame(parent)
        details_frame.pack(fill="both", expand=True, pady=10)
        
        # عنوان القسم
        section_title = ctk.CTkLabel(
            details_frame,
            text="تفاصيل القيد",
            font=ctk.CTkFont(family=FONTS['arabic'], size=18, weight="bold")
        )
        section_title.pack(pady=10)
        
        # إطار إضافة سطر جديد
        add_frame = ctk.CTkFrame(details_frame, fg_color="transparent")
        add_frame.pack(fill="x", padx=20, pady=5)
        
        # اختيار الحساب
        account_label = ctk.CTkLabel(add_frame, text="الحساب:", font=ctk.CTkFont(family=FONTS['arabic'], size=12))
        account_label.pack(side="right", padx=5)
        
        self.account_combo = ctk.CTkComboBox(add_frame, width=200, values=["جاري تحميل الحسابات..."])
        self.account_combo.pack(side="right", padx=5)
        
        # المبلغ المدين
        debit_label = ctk.CTkLabel(add_frame, text="مدين:", font=ctk.CTkFont(family=FONTS['arabic'], size=12))
        debit_label.pack(side="right", padx=5)
        
        self.debit_entry = ctk.CTkEntry(add_frame, width=100, placeholder_text="0.00")
        self.debit_entry.pack(side="right", padx=5)
        
        # المبلغ الدائن
        credit_label = ctk.CTkLabel(add_frame, text="دائن:", font=ctk.CTkFont(family=FONTS['arabic'], size=12))
        credit_label.pack(side="right", padx=5)
        
        self.credit_entry = ctk.CTkEntry(add_frame, width=100, placeholder_text="0.00")
        self.credit_entry.pack(side="right", padx=5)
        
        # البيان
        desc_label = ctk.CTkLabel(add_frame, text="البيان:", font=ctk.CTkFont(family=FONTS['arabic'], size=12))
        desc_label.pack(side="right", padx=5)
        
        self.line_description = ctk.CTkEntry(add_frame, width=150, placeholder_text="بيان السطر")
        self.line_description.pack(side="right", padx=5)
        
        # زر إضافة
        add_btn = ctk.CTkButton(
            add_frame,
            text="➕ إضافة",
            width=80,
            command=self.add_detail_line,
            fg_color=MODERN_COLORS['success']
        )
        add_btn.pack(side="left", padx=5)
        
        # جدول التفاصيل
        table_frame = ctk.CTkFrame(details_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء Treeview
        columns = ("account", "description", "debit", "credit")
        self.details_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعريف الأعمدة
        self.details_tree.heading("account", text="الحساب")
        self.details_tree.heading("description", text="البيان")
        self.details_tree.heading("debit", text="مدين")
        self.details_tree.heading("credit", text="دائن")
        
        # تحديد عرض الأعمدة
        self.details_tree.column("account", width=250)
        self.details_tree.column("description", width=200)
        self.details_tree.column("debit", width=100)
        self.details_tree.column("credit", width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.details_tree.yview)
        self.details_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.details_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط النقر المزدوج للحذف
        self.details_tree.bind("<Double-1>", self.delete_detail_line)
        
        # إطار الإجماليات
        totals_frame = ctk.CTkFrame(details_frame, fg_color="transparent")
        totals_frame.pack(fill="x", padx=20, pady=5)
        
        self.totals_label = ctk.CTkLabel(
            totals_frame,
            text="إجمالي المدين: 0.00 | إجمالي الدائن: 0.00 | الفرق: 0.00",
            font=ctk.CTkFont(family=FONTS['arabic'], size=14, weight="bold")
        )
        self.totals_label.pack()
    
    def create_buttons_section(self, parent):
        """إنشاء قسم الأزرار"""
        buttons_frame = ctk.CTkFrame(parent, height=60, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=10)
        buttons_frame.pack_propagate(False)
        
        # زر حفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ القيد",
            width=120,
            command=self.save_entry,
            fg_color=MODERN_COLORS['success']
        )
        save_btn.pack(side="right", padx=10, pady=15)
        
        # زر مسح
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح الكل",
            width=120,
            command=self.clear_all,
            fg_color=MODERN_COLORS['warning']
        )
        clear_btn.pack(side="right", padx=10, pady=15)
        
        # زر عرض القيود
        view_btn = ctk.CTkButton(
            buttons_frame,
            text="📋 عرض القيود",
            width=120,
            command=self.view_entries,
            fg_color=MODERN_COLORS['info']
        )
        view_btn.pack(side="left", padx=10, pady=15)
    
    def load_accounts(self):
        """تحميل قائمة الحسابات"""
        try:
            self.accounts_list = self.accounts_manager.get_all_accounts()
            account_values = [f"{acc['account_code']} - {acc['account_name']}" for acc in self.accounts_list]
            self.account_combo.configure(values=account_values)
            if account_values:
                self.account_combo.set("اختر الحساب")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الحسابات: {e}")
    
    def add_detail_line(self):
        """إضافة سطر تفصيل جديد"""
        try:
            # التحقق من البيانات
            if self.account_combo.get() == "اختر الحساب" or not self.account_combo.get():
                messagebox.showerror("خطأ", "يرجى اختيار الحساب")
                return
            
            debit_text = self.debit_entry.get().strip()
            credit_text = self.credit_entry.get().strip()
            
            debit_amount = float(debit_text) if debit_text else 0.0
            credit_amount = float(credit_text) if credit_text else 0.0
            
            if debit_amount == 0 and credit_amount == 0:
                messagebox.showerror("خطأ", "يجب إدخال مبلغ مدين أو دائن")
                return
            
            if debit_amount > 0 and credit_amount > 0:
                messagebox.showerror("خطأ", "لا يمكن إدخال مدين ودائن في نفس السطر")
                return
            
            # العثور على الحساب المختار
            selected_account = None
            account_text = self.account_combo.get()
            for acc in self.accounts_list:
                if account_text.startswith(acc['account_code']):
                    selected_account = acc
                    break
            
            if not selected_account:
                messagebox.showerror("خطأ", "الحساب المختار غير صحيح")
                return
            
            # إضافة السطر للجدول
            self.details_tree.insert("", "end", values=(
                f"{selected_account['account_code']} - {selected_account['account_name']}",
                self.line_description.get(),
                f"{debit_amount:.2f}" if debit_amount > 0 else "",
                f"{credit_amount:.2f}" if credit_amount > 0 else ""
            ))
            
            # إضافة للقائمة
            self.entry_details.append({
                'account_id': selected_account['id'],
                'account_code': selected_account['account_code'],
                'account_name': selected_account['account_name'],
                'description': self.line_description.get(),
                'debit_amount': debit_amount,
                'credit_amount': credit_amount
            })
            
            # مسح الحقول
            self.debit_entry.delete(0, "end")
            self.credit_entry.delete(0, "end")
            self.line_description.delete(0, "end")
            self.account_combo.set("اختر الحساب")
            
            # تحديث الإجماليات
            self.update_totals()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للمبالغ")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة السطر: {e}")
    
    def delete_detail_line(self, event):
        """حذف سطر تفصيل"""
        try:
            selected_item = self.details_tree.selection()[0]
            index = self.details_tree.index(selected_item)
            
            # حذف من الجدول
            self.details_tree.delete(selected_item)
            
            # حذف من القائمة
            if 0 <= index < len(self.entry_details):
                del self.entry_details[index]
            
            # تحديث الإجماليات
            self.update_totals()
            
        except IndexError:
            pass  # لا يوجد عنصر محدد
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حذف السطر: {e}")
    
    def update_totals(self):
        """تحديث الإجماليات"""
        total_debit = sum(detail['debit_amount'] for detail in self.entry_details)
        total_credit = sum(detail['credit_amount'] for detail in self.entry_details)
        difference = total_debit - total_credit
        
        # تحديد لون النص حسب التوازن
        color = MODERN_COLORS['success'] if abs(difference) < 0.01 else MODERN_COLORS['error']
        
        self.totals_label.configure(
            text=f"إجمالي المدين: {total_debit:.2f} | إجمالي الدائن: {total_credit:.2f} | الفرق: {difference:.2f}",
            text_color=color
        )
    
    def save_entry(self):
        """حفظ القيد"""
        try:
            # التحقق من البيانات
            if not self.entry_description.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال وصف القيد")
                return
            
            if len(self.entry_details) < 2:
                messagebox.showerror("خطأ", "يجب أن يحتوي القيد على سطرين على الأقل")
                return
            
            # التحقق من التوازن
            total_debit = sum(detail['debit_amount'] for detail in self.entry_details)
            total_credit = sum(detail['credit_amount'] for detail in self.entry_details)
            
            if abs(total_debit - total_credit) >= 0.01:
                messagebox.showerror("خطأ", "القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن")
                return
            
            # إعداد بيانات القيد
            entry_data = {
                'entry_date': self.entry_date.get(),
                'description': self.entry_description.get().strip(),
                'details': self.entry_details,
                'created_by': 1  # يجب تمرير معرف المستخدم الحالي
            }
            
            # حفظ القيد
            result = self.journal_manager.create_journal_entry(entry_data)
            
            if result['success']:
                messagebox.showinfo("نجح", f"تم حفظ القيد بنجاح\nرقم القيد: {result['entry_number']}")
                self.clear_all()
            else:
                error_msg = "\n".join(result['errors'])
                messagebox.showerror("خطأ", f"فشل في حفظ القيد:\n{error_msg}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ القيد: {e}")
    
    def clear_all(self):
        """مسح جميع البيانات"""
        self.entry_description.delete(0, "end")
        self.entry_details.clear()
        
        # مسح الجدول
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        # تحديث الإجماليات
        self.update_totals()
        
        # إعادة تعيين التاريخ
        self.entry_date.delete(0, "end")
        self.entry_date.insert(0, date.today().strftime("%Y-%m-%d"))
    
    def view_entries(self):
        """عرض القيود المحفوظة"""
        try:
            # إنشاء نافذة بسيطة لعرض القيود
            entries_window = ctk.CTkToplevel(self.window)
            entries_window.title("عرض القيود المحاسبية")
            entries_window
            entries_window.configure(fg_color=MODERN_COLORS['background'])

            # جلب القيود
            entries = self.journal_manager.get_all_journal_entries()

            # إنشاء جدول
            columns = ("number", "date", "description", "debit", "credit", "status")
            tree = ttk.Treeview(entries_window, columns=columns, show="headings")

            # تعريف الأعمدة
            tree.heading("number", text="رقم القيد")
            tree.heading("date", text="التاريخ")
            tree.heading("description", text="الوصف")
            tree.heading("debit", text="إجمالي المدين")
            tree.heading("credit", text="إجمالي الدائن")
            tree.heading("status", text="الحالة")

            # إضافة البيانات
            for entry in entries:
                status_text = "مسودة" if entry['status'] == 'draft' else "مرحل"
                tree.insert("", "end", values=(
                    entry['entry_number'],
                    entry['entry_date'],
                    entry['description'][:50] + "..." if len(entry['description']) > 50 else entry['description'],
                    f"{entry['total_debit']:.2f}",
                    f"{entry['total_credit']:.2f}",
                    status_text
                ))

            tree.pack(fill="both", expand=True, padx=20, pady=20)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة القيود: {e}")
    
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
