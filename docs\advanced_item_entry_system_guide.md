# دليل نظام إدخال الأصناف الشامل والمتقدم

## نظرة عامة

تم تطوير نظام إدخال الأصناف الشامل والمتقدم ليكون الحل الأكثر تطوراً وشمولية لإدارة الأصناف في البرنامج المحاسبي. يجمع النظام بين الذكاء الاصطناعي والتحليلات المتقدمة والواجهة الاحترافية لتوفير تجربة مستخدم متميزة.

## الميزات الرئيسية

### 🤖 الذكاء الاصطناعي
- **اقتراح التصنيف التلقائي**: يحلل اسم ووصف الصنف لاقتراح الفئة المناسبة
- **توقع الأسعار الذكي**: يستخدم خوارزميات متقدمة لتوقع أسعار البيع المناسبة
- **كشف التكرار**: يحدد الأصناف المشابهة لتجنب الازدواجية
- **تقييم جودة البيانات**: يقيم مدى اكتمال وجودة بيانات الصنف

### 📊 التحليلات المتقدمة
- **اتجاه المبيعات**: رسوم بيانية تفاعلية لتتبع أداء المبيعات
- **توزيع الفئات**: تحليل توزيع الأصناف حسب الفئات
- **تحليل الربحية**: مراقبة هوامش الربح والأداء المالي
- **حالة المخزون**: متابعة مستويات المخزون والتنبيهات

### 🎨 الواجهة الاحترافية
- **تصميم حديث**: واجهة عصرية مع دعم الثيمات
- **دعم اللغة العربية**: دعم كامل للنصوص العربية واتجاه RTL
- **تبويبات منظمة**: تنظيم المعلومات في تبويبات منطقية
- **اختصارات لوحة المفاتيح**: تسريع العمل من خلال الاختصارات

### 🔧 الميزات التقنية
- **قاعدة بيانات متقدمة**: نظام SQLite محسن مع فهرسة ذكية
- **إدارة التبعيات**: فحص وتثبيت التبعيات تلقائياً
- **التصدير والاستيراد**: دعم صيغ متعددة (Excel, PDF, JSON, XML, CSV)
- **الباركود والـ QR**: توليد وقراءة الرموز الشريطية

## بنية النظام

```
windows/
├── advanced_item_entry_comprehensive.py  # النافذة الرئيسية
core/
├── advanced_dependency_manager.py        # إدارة التبعيات
database/
├── advanced_items_database.py           # قاعدة البيانات المتقدمة
ai/
├── intelligent_item_manager.py          # نظام الذكاء الاصطناعي
analytics/
├── advanced_analytics_engine.py         # محرك التحليلات
```

## التبعيات المطلوبة

### التبعيات الأساسية
- `tkinter` - واجهة المستخدم الرسومية
- `sqlite3` - قاعدة البيانات
- `datetime` - التعامل مع التواريخ
- `uuid` - توليد المعرفات الفريدة

### التبعيات المتقدمة
- `matplotlib>=3.8.0` - الرسوم البيانية
- `numpy>=1.24.0` - العمليات الرياضية
- `pandas>=2.1.0` - تحليل البيانات
- `scikit-learn>=1.3.0` - خوارزميات التعلم الآلي
- `PIL/Pillow>=10.0.0` - معالجة الصور

### التبعيات الاختيارية
- `qrcode>=7.4.0` - توليد رموز QR
- `python-barcode>=0.14.0` - توليد الباركود
- `openpyxl>=3.1.0` - ملفات Excel
- `reportlab>=4.0.0` - ملفات PDF

## دليل الاستخدام

### 1. فتح النظام
```python
# من خلال البرنامج الرئيسي
python large_font_run.py

# أو مباشرة
python windows/advanced_item_entry_comprehensive.py
```

### 2. إدخال صنف جديد
1. انقر على "جديد" أو اضغط `Ctrl+N`
2. أدخل اسم الصنف (مطلوب)
3. سيتم توليد رمز تلقائي أو يمكنك إدخال رمز مخصص
4. اختر الفئة والوحدة
5. أدخل معلومات التسعير والمخزون
6. احفظ بالضغط على "حفظ" أو `Ctrl+S`

### 3. استخدام الذكاء الاصطناعي
- **اقتراح التصنيف**: انقر على "🤖 اقتراح" بعد إدخال اسم الصنف
- **توقع السعر**: انقر على "💰 توقع السعر" بعد إدخال سعر التكلفة
- **كشف التكرار**: انقر على "🔍 كشف التكرار" للبحث عن أصناف مشابهة

### 4. عرض التحليلات
- انتقل إلى تبويب "تحليلات"
- اختر نوع التحليل المطلوب
- ستظهر النتائج في نافذة منفصلة

## اختصارات لوحة المفاتيح

| الاختصار | الوظيفة |
|----------|---------|
| `Ctrl+N` | صنف جديد |
| `Ctrl+S` | حفظ |
| `Ctrl+D` | حذف |
| `Ctrl+F` | البحث |
| `Ctrl+R` | تحديث البيانات |
| `F1` | دليل المستخدم |
| `F5` | تحديث |
| `Escape` | مسح النموذج |

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. فشل في تحميل النظام
```
خطأ: No module named 'matplotlib'
```
**الحل**: تثبيت التبعيات المطلوبة
```bash
pip install matplotlib numpy pandas scikit-learn pillow
```

#### 2. مشاكل قاعدة البيانات
```
خطأ: database is locked
```
**الحل**: إغلاق جميع الاتصالات وإعادة تشغيل النظام

#### 3. مشاكل الذكاء الاصطناعي
```
خطأ: AI features not available
```
**الحل**: التأكد من تثبيت scikit-learn وnumpy

### سجل الأخطاء
يتم حفظ سجل الأخطاء في:
- `logs/item_entry_errors.log`
- `logs/ai_operations.log`
- `logs/database_operations.log`

## التطوير والتخصيص

### إضافة ميزات جديدة
1. إنشاء ملف جديد في المجلد المناسب
2. تحديث ملف `__init__.py`
3. إضافة الاستيراد في النافذة الرئيسية
4. تحديث التوثيق

### تخصيص الواجهة
```python
# تغيير الألوان
self.ui_config['colors']['primary'] = '#YOUR_COLOR'

# تغيير الخط
self.ui_config['font_family'] = 'YOUR_FONT'
```

### إضافة خوارزميات ذكاء اصطناعي جديدة
```python
# في ai/intelligent_item_manager.py
def new_ai_feature(self, data):
    # تنفيذ الخوارزمية الجديدة
    return result
```

## الدعم والمساعدة

### الحصول على المساعدة
- دليل المستخدم: اضغط `F1` داخل النظام
- التوثيق التقني: `docs/` folder
- أمثلة الاستخدام: `examples/` folder

### الإبلاغ عن المشاكل
1. جمع معلومات الخطأ
2. تحديد خطوات إعادة الإنتاج
3. إرفاق سجل الأخطاء
4. وصف السلوك المتوقع

## الإصدارات والتحديثات

### الإصدار الحالي: v1.0.0
- ✅ نظام الذكاء الاصطناعي الكامل
- ✅ محرك التحليلات المتقدم
- ✅ واجهة المستخدم الاحترافية
- ✅ قاعدة البيانات المحسنة
- ✅ نظام إدارة التبعيات

### التحديثات المستقبلية
- 🔄 دعم قواعد بيانات إضافية
- 🔄 تحليلات أكثر تقدماً
- 🔄 ميزات ذكاء اصطناعي إضافية
- 🔄 تحسينات الأداء

---

**تم تطوير هذا النظام بواسطة فريق التطوير المتخصص لتوفير أفضل تجربة لإدارة الأصناف في البرامج المحاسبية.**
