# -*- coding: utf-8 -*-
"""
إعدادات PostgreSQL
PostgreSQL Configuration
"""

import os

class PostgreSQLConfig:
    """إعدادات PostgreSQL"""

    # الإعدادات الافتراضية
    DEFAULT_CONFIG = {
        'host': 'localhost',
        'port': 5432,
        'database': 'accounting_db',
        'user': 'postgres',
        'password': 'postgres'
    }

    # إعدادات الإنتاج
    PRODUCTION_CONFIG = {
        'host': os.getenv('POSTGRES_HOST', 'localhost'),
        'port': int(os.getenv('POSTGRES_PORT', 5432)),
        'database': os.getenv('POSTGRES_DB', 'accounting_production'),
        'user': os.getenv('POSTGRES_USER', 'postgres'),
        'password': os.getenv('POSTGRES_PASSWORD', 'postgres')
    }

    # إعدادات التطوير
    DEVELOPMENT_CONFIG = {
        'host': 'localhost',
        'port': 5432,
        'database': 'accounting_dev',
        'user': 'postgres',
        'password': 'postgres'
    }

    # إعدادات الاختبار
    TEST_CONFIG = {
        'host': 'localhost',
        'port': 5432,
        'database': 'accounting_test',
        'user': 'postgres',
        'password': 'postgres'
    }

    @classmethod
    def get_config(cls, environment: str = 'default') -> Dict:
        """
        الحصول على إعدادات PostgreSQL

        Args:
            environment (str): البيئة (default, production, development, test)

        Returns:
            Dict: إعدادات الاتصال
        """
        if environment == 'production':
            return cls.PRODUCTION_CONFIG.copy()
        elif environment == 'development':
            return cls.DEVELOPMENT_CONFIG.copy()
        elif environment == 'test':
            return cls.TEST_CONFIG.copy()
        else:
            return cls.DEFAULT_CONFIG.copy()

    @classmethod
    def from_url(cls, database_url: str) -> Dict:
        """
        إنشاء إعدادات من URL

        Args:
            database_url (str): رابط قاعدة البيانات

        Returns:
            Dict: إعدادات الاتصال
        """
        try:
            from urllib.parse import urlparse

            parsed = urlparse(database_url)

            return {
                'host': parsed.hostname,
                'port': parsed.port or 5432,
                'database': parsed.path[1:],  # إزالة الشرطة المائلة الأولى
                'user': parsed.username,
                'password': parsed.password
            }

        except Exception as e:
            pass
        except Exception as e:
            print(f"خطأ في تحليل URL قاعدة البيانات: {e}")
            return cls.DEFAULT_CONFIG.copy()

    @classmethod
    def validate_config(cls, config: Dict) -> Dict:
        """
        التحقق من صحة الإعدادات

        Args:
            config (Dict): إعدادات الاتصال

        Returns:
            Dict: نتيجة التحقق
        """
        errors = []

        required_fields = ['host', 'port', 'database', 'user', 'password']

        for field in required_fields:
            if field not in config or not config[field]:
                errors.append(f"الحقل {field} مطلوب")

        # التحقق من نوع البورت
        try:
            port = int(config.get('port', 0))
            if port <= 0 or port > 65535:
                errors.append("رقم البورت يجب أن يكون بين 1 و 65535")
        except (ValueError, TypeError):
            pass
        except (ValueError, TypeError):
            errors.append("رقم البورت يجب أن يكون رقم صحيح")

        return {
            'is_valid': len(errors) == 0,
            'errors': errors
        }

    @classmethod
    def test_connection(cls, config: Dict) -> Dict:
        """
        اختبار الاتصال بقاعدة البيانات

        Args:
            config (Dict): إعدادات الاتصال

        Returns:
            Dict: نتيجة الاختبار
        """
        try:
            import psycopg2
        except Exception as e:
            print(f"خطأ: {e}")
from typing import Dict
from typing import List, Dict, Optional, Tuple, Any, Union, Callable

            # محاولة الاتصال
            conn = psycopg2.connect(**config)
            cursor = conn.cursor()

            # اختبار استعلام بسيط
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            return {
                'success': True,
                'message': 'تم الاتصال بنجاح',
                'version': version
            }

        except psycopg2.OperationalError as e:
            return {
                'success': False,
                'error': 'connection_failed',
                'message': f'فشل في الاتصال: {str(e)}'
            }
        except ImportError:
            return {
                'success': False,
                'error': 'missing_driver',
                'message': 'مكتبة psycopg2 غير مثبتة'
            }
        except Exception as e:
            return {
                'success': False,
                'error': 'unknown_error',
                'message': f'خطأ غير معروف: {str(e)}'
            }

# إعدادات اتصال سريعة
QUICK_CONFIGS = {
    'local': {
        'host': 'localhost',
        'port': 5432,
        'database': 'accounting_db',
        'user': 'postgres',
        'password': 'postgres'
    },
    'docker': {
        'host': 'localhost',
        'port': 5433,  # البورت المعتاد لـ Docker
        'database': 'accounting_db',
        'user': 'postgres',
        'password': 'postgres'
    },
    'heroku': {
        # سيتم ملؤها من متغيرات البيئة
        'host': os.getenv('DATABASE_HOST'),
        'port': int(os.getenv('DATABASE_PORT', 5432)),
        'database': os.getenv('DATABASE_NAME'),
        'user': os.getenv('DATABASE_USER'),
        'password': os.getenv('DATABASE_PASSWORD')
    }
}

def get_quick_config(name: str) -> Dict:
    """الحصول على إعداد سريع"""
    return QUICK_CONFIGS.get(name, PostgreSQLConfig.DEFAULT_CONFIG).copy()

def setup_environment_variables():
    """إعداد متغيرات البيئة للتطوير"""
    env_vars = {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_PORT': '5432',
        'POSTGRES_DB': 'accounting_db',
        'POSTGRES_USER': 'postgres',
        'POSTGRES_PASSWORD': 'postgres'
    }

    for key, value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = value

    print("تم إعداد متغيرات البيئة للتطوير")

if __name__ == "__main__":
    # اختبار الإعدادات
    print("اختبار إعدادات PostgreSQL:")

    config = PostgreSQLConfig.get_config('default')
    print(f"الإعدادات الافتراضية: {config}")

    validation = PostgreSQLConfig.validate_config(config)
    print(f"نتيجة التحقق: {validation}")

    if validation['is_valid']:
        connection_test = PostgreSQLConfig.test_connection(config)
        print(f"اختبار الاتصال: {connection_test}")
    else:
        print("الإعدادات غير صحيحة، لا يمكن اختبار الاتصال")
