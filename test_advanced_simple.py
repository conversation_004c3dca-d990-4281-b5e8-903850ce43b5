# -*- coding: utf-8 -*-
"""
اختبار بسيط للنافذة المتقدمة
"""

import tkinter as tk
from tkinter import messagebox

def test_imports():
    """اختبار الاستيرادات"""
    try:
        print("🔍 اختبار الاستيرادات...")
        
        # اختبار المكتبات الأساسية
        import tkinter as tk
        print("✅ tkinter")
        
        from tkinter import ttk, messagebox
        print("✅ ttk, messagebox")
        
        from PIL import Image, ImageTk
        print("✅ PIL")
        
        import matplotlib.pyplot as plt
        print("✅ matplotlib")
        
        import numpy as np
        print("✅ numpy")
        
        import pandas as pd
        print("✅ pandas")
        
        # اختبار استيراد النافذة المتقدمة
        from windows.advanced_item_entry import AdvancedItemEntry
        print("✅ AdvancedItemEntry")
        
        print("🎉 جميع الاستيرادات نجحت!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_window_creation():
    """اختبار إنشاء النافذة"""
    try:
        print("🏗️ اختبار إنشاء النافذة...")
        
        from windows.advanced_item_entry import AdvancedItemEntry
        
        # إنشاء النافذة
        app = AdvancedItemEntry()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # إغلاق النافذة فوراً
        app.window.destroy()
        print("✅ تم إغلاق النافذة بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار البسيط...")
    print("=" * 50)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("❌ فشل في اختبار الاستيرادات")
        return
    
    print("\n" + "=" * 50)
    
    # اختبار إنشاء النافذة
    if not test_window_creation():
        print("❌ فشل في اختبار إنشاء النافذة")
        return
    
    print("\n" + "=" * 50)
    print("🎊 جميع الاختبارات نجحت!")
    print("✅ النافذة المتقدمة جاهزة للاستخدام")

if __name__ == "__main__":
    main()
