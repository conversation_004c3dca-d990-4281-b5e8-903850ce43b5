# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة قائمة الدخل الشامل المحاسبية
Comprehensive Income Statement Window
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk, filedialog
from datetime import datetime, date, timedelta
from database.comprehensive_income_manager import ComprehensiveIncomeManager
from database.database_manager import DatabaseManager
try:
    from themes.modern_theme import MODERN_COLORS, FONTS
    from ui.window_utils import configure_window_fullscreen
except ImportError:
    MODERN_COLORS = {
        'primary': '#1f538d',
        'background': '#f0f0f0',
        'surface': '#ffffff',
        'text_primary': '#000000',
        'success': '#28a745',
        'error': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8'
    }
    FONTS = {'arabic': 'Arial'}

class ComprehensiveIncomeWindow:
    """نافذة قائمة الدخل الشامل وفقاً للمعايير المحاسبية المهنية"""
    
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.db_manager = DatabaseManager()
        self.income_manager = ComprehensiveIncomeManager(self.db_manager)
        self.current_report = None
        
        self.create_window()
    
    def create_window(self):
        """إنشاء النافذة الرئيسية"""
        self.window = ctk.CTkToplevel(self.parent)
        
        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "قائمة الدخل الشامل المحاسبية - برنامج ست الكل للمحاسبة")
        self.window.configure(fg_color=MODERN_COLORS['background'])
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إنشاء المحتوى
        self.create_header()
        self.create_controls()
        self.create_report_area()
        self.create_buttons()
        
        # تحميل التقرير الافتراضي
        self.load_default_report()
    
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = ctk.CTkFrame(self.window, height=80, fg_color=MODERN_COLORS['primary'])
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = ctk.CTkLabel(
            header_frame,
            text="📊 قائمة الدخل الشامل المحاسبية",
            font=(FONTS['arabic'], 24, "bold"),
            text_color="white"
        )
        title_label.pack(side="right", padx=20, pady=20)
        
        # معلومات سريعة
        info_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        info_frame.pack(side="left", padx=20, pady=15)
        
        self.period_label = ctk.CTkLabel(
            info_frame,
            text="الفترة: غير محددة",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.period_label.pack()
        
        self.status_label = ctk.CTkLabel(
            info_frame,
            text="الحالة: جاهز",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.status_label.pack()
    
    def create_controls(self):
        """إنشاء عناصر التحكم"""
        controls_frame = ctk.CTkFrame(self.window, height=100)
        controls_frame.pack(fill="x", padx=10, pady=10)
        controls_frame.pack_propagate(False)
        
        # إطار التواريخ
        dates_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        dates_frame.pack(side="right", padx=20, pady=15)
        
        # تاريخ البداية
        ctk.CTkLabel(dates_frame, text="من تاريخ:", font=(FONTS['arabic'], 12)).grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.start_date_entry = ctk.CTkEntry(dates_frame, width=120, placeholder_text="YYYY-MM-DD")
        self.start_date_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # تاريخ النهاية
        ctk.CTkLabel(dates_frame, text="إلى تاريخ:", font=(FONTS['arabic'], 12)).grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.end_date_entry = ctk.CTkEntry(dates_frame, width=120, placeholder_text="YYYY-MM-DD")
        self.end_date_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # أزرار الفترات السريعة
        quick_periods_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        quick_periods_frame.pack(side="left", padx=20, pady=15)
        
        ctk.CTkLabel(quick_periods_frame, text="فترات سريعة:", font=(FONTS['arabic'], 12, "bold")).pack(anchor="w")
        
        periods_buttons_frame = ctk.CTkFrame(quick_periods_frame, fg_color="transparent")
        periods_buttons_frame.pack(fill="x", pady=5)
        
        # أزرار الفترات
        ctk.CTkButton(periods_buttons_frame, text="هذا الشهر", command=self.set_current_month, width=80, height=30).pack(side="right", padx=2)
        ctk.CTkButton(periods_buttons_frame, text="هذا العام", command=self.set_current_year, width=80, height=30).pack(side="right", padx=2)
        ctk.CTkButton(periods_buttons_frame, text="الربع الحالي", command=self.set_current_quarter, width=80, height=30).pack(side="right", padx=2)
        ctk.CTkButton(periods_buttons_frame, text="آخر 30 يوم", command=self.set_last_30_days, width=80, height=30).pack(side="right", padx=2)
        
        # زر إنشاء التقرير
        generate_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        generate_frame.pack(side="left", padx=20, pady=15)
        
        self.generate_btn = ctk.CTkButton(
            generate_frame,
            text="🔄 إنشاء التقرير",
            command=self.generate_report,
            fg_color=MODERN_COLORS['success'],
            width=120,
            height=40
        )
        self.generate_btn.pack()
    
    def create_report_area(self):
        """إنشاء منطقة عرض التقرير"""
        report_frame = ctk.CTkFrame(self.window)
        report_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(report_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تبويب التقرير المفصل
        self.detailed_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.detailed_frame, text="التقرير المفصل")
        
        # تبويب التقرير المختصر
        self.summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.summary_frame, text="التقرير المختصر")
        
        # تبويب النسب المالية
        self.ratios_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.ratios_frame, text="النسب المالية")
        
        # إنشاء محتوى التبويبات
        self.create_detailed_tab()
        self.create_summary_tab()
        self.create_ratios_tab()
    
    def create_detailed_tab(self):
        """إنشاء تبويب التقرير المفصل"""
        # منطقة النص للتقرير المفصل
        self.detailed_text = tk.Text(
            self.detailed_frame,
            font=('Courier New', 10),
            wrap=tk.WORD,
            bg='white',
            fg='black',
            direction='rtl'
        )
        
        # شريط التمرير
        detailed_scrollbar = ttk.Scrollbar(self.detailed_frame, orient="vertical", command=self.detailed_text.yview)
        self.detailed_text.configure(yscrollcommand=detailed_scrollbar.set)
        
        # تخطيط العناصر
        self.detailed_text.pack(side="right", fill="both", expand=True)
        detailed_scrollbar.pack(side="left", fill="y")
    
    def create_summary_tab(self):
        """إنشاء تبويب التقرير المختصر"""
        # إطار للجدول
        table_frame = ctk.CTkFrame(self.summary_frame)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء Treeview للجدول
        columns = ("item", "amount", "percentage")
        self.summary_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف الأعمدة
        self.summary_tree.heading("item", text="البند")
        self.summary_tree.heading("amount", text="المبلغ")
        self.summary_tree.heading("percentage", text="النسبة %")
        
        # تحديد عرض الأعمدة
        self.summary_tree.column("item", width=300, anchor="e")
        self.summary_tree.column("amount", width=150, anchor="center")
        self.summary_tree.column("percentage", width=100, anchor="center")
        
        # شريط التمرير للجدول
        summary_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.summary_tree.yview)
        self.summary_tree.configure(yscrollcommand=summary_scrollbar.set)
        
        # تخطيط الجدول
        self.summary_tree.pack(side="right", fill="both", expand=True)
        summary_scrollbar.pack(side="left", fill="y")
    
    def create_ratios_tab(self):
        """إنشاء تبويب النسب المالية"""
        ratios_container = ctk.CTkScrollableFrame(self.ratios_frame)
        ratios_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان النسب المالية
        ctk.CTkLabel(
            ratios_container,
            text="📈 النسب المالية الأساسية",
            font=(FONTS['arabic'], 18, "bold")
        ).pack(pady=(0, 20))
        
        # إطار النسب
        self.ratios_display_frame = ctk.CTkFrame(ratios_container)
        self.ratios_display_frame.pack(fill="x", pady=10)
        
        # سيتم ملء هذا الإطار بالنسب المالية عند إنشاء التقرير
    
    def create_buttons(self):
        """إنشاء أزرار النافذة"""
        buttons_frame = ctk.CTkFrame(self.window, height=60, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=10, pady=(0, 10))
        buttons_frame.pack_propagate(False)
        
        # زر تصدير Excel
        export_excel_btn = ctk.CTkButton(
            buttons_frame,
            text="📊 تصدير Excel",
            command=self.export_to_excel,
            fg_color=MODERN_COLORS['success'],
            width=120
        )
        export_excel_btn.pack(side="right", padx=5, pady=15)
        
        # زر طباعة
        print_btn = ctk.CTkButton(
            buttons_frame,
            text="🖨️ طباعة",
            command=self.print_report,
            fg_color=MODERN_COLORS['info'],
            width=100
        )
        print_btn.pack(side="right", padx=5, pady=15)
        
        # زر حفظ PDF
        save_pdf_btn = ctk.CTkButton(
            buttons_frame,
            text="📄 حفظ PDF",
            command=self.save_as_pdf,
            fg_color=MODERN_COLORS['warning'],
            width=100
        )
        save_pdf_btn.pack(side="right", padx=5, pady=15)
        
        # زر إغلاق
        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            command=self.close_window,
            fg_color=MODERN_COLORS['error'],
            width=100
        )
        close_btn.pack(side="left", padx=5, pady=15)
        
        # زر تحديث
        refresh_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث",
            command=self.generate_report,
            fg_color=MODERN_COLORS['primary'],
            width=100
        )
        refresh_btn.pack(side="left", padx=5, pady=15)
    
    def set_current_month(self):
        """تعيين الشهر الحالي"""
        today = date.today()
        start_date = date(today.year, today.month, 1)
        end_date = today
        
        self.start_date_entry.delete(0, "end")
        self.start_date_entry.insert(0, start_date.strftime('%Y-%m-%d'))
        
        self.end_date_entry.delete(0, "end")
        self.end_date_entry.insert(0, end_date.strftime('%Y-%m-%d'))
    
    def set_current_year(self):
        """تعيين العام الحالي"""
        today = date.today()
        start_date = date(today.year, 1, 1)
        end_date = today
        
        self.start_date_entry.delete(0, "end")
        self.start_date_entry.insert(0, start_date.strftime('%Y-%m-%d'))
        
        self.end_date_entry.delete(0, "end")
        self.end_date_entry.insert(0, end_date.strftime('%Y-%m-%d'))
    
    def set_current_quarter(self):
        """تعيين الربع الحالي"""
        today = date.today()
        quarter = (today.month - 1) // 3 + 1
        start_month = (quarter - 1) * 3 + 1
        start_date = date(today.year, start_month, 1)
        end_date = today
        
        self.start_date_entry.delete(0, "end")
        self.start_date_entry.insert(0, start_date.strftime('%Y-%m-%d'))
        
        self.end_date_entry.delete(0, "end")
        self.end_date_entry.insert(0, end_date.strftime('%Y-%m-%d'))
    
    def set_last_30_days(self):
        """تعيين آخر 30 يوم"""
        today = date.today()
        start_date = today - timedelta(days=30)
        end_date = today
        
        self.start_date_entry.delete(0, "end")
        self.start_date_entry.insert(0, start_date.strftime('%Y-%m-%d'))
        
        self.end_date_entry.delete(0, "end")
        self.end_date_entry.insert(0, end_date.strftime('%Y-%m-%d'))
    
    def load_default_report(self):
        """تحميل التقرير الافتراضي"""
        # تعيين الشهر الحالي كافتراضي
        self.set_current_month()
        # إنشاء التقرير
        self.generate_report()

    def generate_report(self):
        """إنشاء تقرير الدخل الشامل"""
        try:
            # جلب التواريخ
            start_date_str = self.start_date_entry.get().strip()
            end_date_str = self.end_date_entry.get().strip()

            # التحقق من صحة التواريخ
            if not start_date_str or not end_date_str:
                messagebox.showwarning("تحذير", "يرجى إدخال تاريخ البداية والنهاية")
                return

            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            except ValueError:
                messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
                return

            if start_date > end_date:
                messagebox.showerror("خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                return

            # تحديث حالة النافذة
            self.status_label.configure(text="الحالة: جاري إنشاء التقرير...")
            self.generate_btn.configure(state="disabled", text="جاري الإنشاء...")
            self.window.update()

            # إنشاء التقرير
            self.current_report = self.income_manager.calculate_comprehensive_income(start_date, end_date)

            # تحديث عرض التقرير
            self.update_detailed_report()
            self.update_summary_report()
            self.update_ratios_display()

            # تحديث معلومات الفترة
            period_text = f"الفترة: من {start_date_str} إلى {end_date_str}"
            self.period_label.configure(text=period_text)

            # تحديث الحالة
            self.status_label.configure(text="الحالة: تم إنشاء التقرير بنجاح")
            self.generate_btn.configure(state="normal", text="🔄 إنشاء التقرير")

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {e}")
            self.status_label.configure(text="الحالة: خطأ في إنشاء التقرير")
            self.generate_btn.configure(state="normal", text="🔄 إنشاء التقرير")

    def update_detailed_report(self):
        """تحديث التقرير المفصل"""
        if not self.current_report:
            return

        # مسح المحتوى السابق
        self.detailed_text.delete(1.0, tk.END)

        # إنشاء التقرير النصي المفصل
        detailed_text = self.income_manager.generate_comprehensive_income_statement(
            datetime.strptime(self.current_report['period']['start_date'], '%Y-%m-%d').date(),
            datetime.strptime(self.current_report['period']['end_date'], '%Y-%m-%d').date()
        )

        # إدراج النص
        self.detailed_text.insert(tk.END, detailed_text)

        # تنسيق النص
        self.detailed_text.tag_configure("header", font=('Courier New', 12, 'bold'))
        self.detailed_text.tag_configure("total", font=('Courier New', 11, 'bold'), background='lightblue')

    def update_summary_report(self):
        """تحديث التقرير المختصر"""
        if not self.current_report:
            return

        # مسح الجدول السابق
        for item in self.summary_tree.get_children():
            self.summary_tree.delete(item)

        # البيانات الأساسية
        revenues = self.current_report['revenues']['total_revenues']

        # إدراج البيانات في الجدول
        data = [
            ("الإيرادات", revenues, "100.00"),
            ("تكلفة البضاعة المباعة", -self.current_report['cost_of_goods_sold']['total_cogs'],
            f"{(self.current_report['cost_of_goods_sold']['total_cogs'] / revenues * 100) if revenues > 0 else 0:.2f}"),
            ("مجمل الربح", self.current_report['gross_profit'],
            f"{self.current_report['financial_ratios']['gross_profit_margin']:.2f}"),
            ("المصروفات التشغيلية", -self.current_report['operating_expenses']['total_operating_expenses'],
            f"{(self.current_report['operating_expenses']['total_operating_expenses'] / revenues * 100) if revenues > 0 else 0:.2f}"),
            ("الربح التشغيلي", self.current_report['operating_profit'],
            f"{self.current_report['financial_ratios']['operating_profit_margin']:.2f}"),
            ("العمليات غير التشغيلية", self.current_report['non_operating']['net'],
            f"{(self.current_report['non_operating']['net'] / revenues * 100) if revenues > 0 else 0:.2f}"),
            ("الربح قبل الضريبة", self.current_report['earnings_before_tax'],
            f"{(self.current_report['earnings_before_tax'] / revenues * 100) if revenues > 0 else 0:.2f}"),
            ("الضرائب", -self.current_report['taxes']['total_taxes'],
            f"{(self.current_report['taxes']['total_taxes'] / revenues * 100) if revenues > 0 else 0:.2f}"),
            ("صافي الربح", self.current_report['net_profit'],
            f"{self.current_report['financial_ratios']['net_profit_margin']:.2f}"),
            ("الدخل الشامل الآخر", self.current_report['other_comprehensive_income']['total_oci'],
            f"{(self.current_report['other_comprehensive_income']['total_oci'] / revenues * 100) if revenues > 0 else 0:.2f}"),
            ("الدخل الشامل النهائي", self.current_report['total_comprehensive_income'],
            f"{(self.current_report['total_comprehensive_income'] / revenues * 100) if revenues > 0 else 0:.2f}")
        ]

        for item, amount, percentage in data:
            # تنسيق المبلغ
            amount_formatted = f"{amount:,.2f}"
            if amount < 0:
                amount_formatted = f"({abs(amount):,.2f})"

            self.summary_tree.insert("", "end", values=(item, amount_formatted, percentage))

    def update_ratios_display(self):
        """تحديث عرض النسب المالية"""
        if not self.current_report:
            return

        # مسح العرض السابق
        for widget in self.ratios_display_frame.winfo_children():
            if widget and hasattr(widget, "destroy"):

                widget.destroy()

        ratios = self.current_report['financial_ratios']

        # إنشاء عرض النسب
        ratios_data = [
            ("هامش مجمل الربح", ratios['gross_profit_margin'], "%", "نسبة مجمل الربح إلى الإيرادات"),
            ("هامش الربح التشغيلي", ratios['operating_profit_margin'], "%", "نسبة الربح التشغيلي إلى الإيرادات"),
            ("هامش صافي الربح", ratios['net_profit_margin'], "%", "نسبة صافي الربح إلى الإيرادات")
        ]

        for i, (name, value, unit, description) in enumerate(ratios_data):
            # إطار النسبة
            ratio_frame = ctk.CTkFrame(self.ratios_display_frame)
            ratio_frame.pack(fill="x", padx=10, pady=5)

            # اسم النسبة
            name_label = ctk.CTkLabel(
                ratio_frame,
                text=name,
                font=(FONTS['arabic'], 14, "bold")
            )
            name_label.pack(side="right", padx=10, pady=5)

            # قيمة النسبة
            value_label = ctk.CTkLabel(
                ratio_frame,
                text=f"{value:.2f}{unit}",
                font=(FONTS['arabic'], 16, "bold"),
                text_color=MODERN_COLORS['success'] if value > 0 else MODERN_COLORS['error']
            )
            value_label.pack(side="left", padx=10, pady=5)

            # وصف النسبة
            desc_label = ctk.CTkLabel(
                ratio_frame,
                text=description,
                font=(FONTS['arabic'], 10),
                text_color="gray"
            )
            desc_label.pack(side="right", padx=10, pady=(0, 5))

    def export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        if not self.current_report:
            messagebox.showwarning("تحذير", "لا يوجد تقرير لتصديره")
            return

        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ تقرير الدخل الشامل"
            )

            if filename:
                start_date = datetime.strptime(self.current_report['period']['start_date'], '%Y-%m-%d').date()
                end_date = datetime.strptime(self.current_report['period']['end_date'], '%Y-%m-%d').date()

                result = self.income_manager.export_to_excel(start_date, end_date, filename)

                if result['success']:
                    messagebox.showinfo("نجح", result['message'])
                else:
                    messagebox.showerror("خطأ", result['message'])

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير التقرير: {e}")

    def print_report(self):
        """طباعة التقرير"""
        if not self.current_report:
            messagebox.showwarning("تحذير", "لا يوجد تقرير للطباعة")
            return

        try:
            # إنشاء نافذة معاينة الطباعة
            print_window = ctk.CTkToplevel(self.window)
            print_window.title("معاينة الطباعة")
            print_window

            # منطقة النص للطباعة
            print_text = tk.Text(print_window, font=('Courier New', 10), wrap=tk.WORD)
            print_text.pack(fill="both", expand=True, padx=10, pady=10)

            # إدراج محتوى التقرير
            detailed_text = self.income_manager.generate_comprehensive_income_statement(
                datetime.strptime(self.current_report['period']['start_date'], '%Y-%m-%d').date(),
                datetime.strptime(self.current_report['period']['end_date'], '%Y-%m-%d').date()
            )
            print_text.insert(tk.END, detailed_text)

            # أزرار الطباعة
            buttons_frame = ctk.CTkFrame(print_window)
            buttons_frame.pack(fill="x", padx=10, pady=10)

            ctk.CTkButton(buttons_frame, text="طباعة",
                        command=lambda: self.do_print(print_text)).pack(side="right", padx=5)
            ctk.CTkButton(buttons_frame, text="إغلاق",
                        command=print_window.destroy).pack(side="left", padx=5)

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في معاينة الطباعة: {e}")

    def do_print(self, text_widget):
        """تنفيذ الطباعة الفعلية"""
        try:
            # هذه دالة بسيطة للطباعة - يمكن تحسينها
            content = text_widget.get(1.0, tk.END)

            # يمكن إضافة مكتبة طباعة متقدمة هنا
            messagebox.showinfo("طباعة", "تم إرسال التقرير للطباعة")

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الطباعة: {e}")

    def save_as_pdf(self):
        """حفظ التقرير كملف PDF"""
        if not self.current_report:
            messagebox.showwarning("تحذير", "لا يوجد تقرير لحفظه")
            return

        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ تقرير الدخل الشامل كـ PDF"
            )

            if filename:
                # يمكن إضافة مكتبة PDF هنا مثل reportlab
                messagebox.showinfo("معلومات", "ميزة حفظ PDF ستكون متاحة قريباً")

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ PDF: {e}")

    def close_window(self):
        """إغلاق النافذة"""
        self.if window and hasattr(window, "destroy"):
    if window and hasattr(window, "destroy"):
    window.destroy()
