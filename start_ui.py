#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 تشغيل الواجهة الرئيسية - برنامج ست الكل للمحاسبة
Start Main UI - Sit Al-Kol Accounting Program
"""

import sys
import os
from pathlib import Path

# إعداد المسار
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    try:
        import customtkinter as ctk
        print("✅ customtkinter متوفر")
    except ImportError:
        print("❌ customtkinter غير متوفر")
        print("قم بتثبيته: pip install customtkinter")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL متوفر")
    except ImportError:
        print("⚠️ PIL غير متوفر - سيتم استخدام الأيقونات النصية")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🎯 برنامج ست الكل للمحاسبة")
    print("=" * 40)
    
    # فحص المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return
    
    try:
        print("📱 تحميل الواجهة الرئيسية...")
        from ui.main_window import MainApplication
        print("✅ تم تحميل الواجهة بنجاح")
        
        print("🔧 إنشاء التطبيق...")
        app = MainApplication()
        print("✅ تم إنشاء التطبيق بنجاح")
        
        print("🚀 تشغيل الواجهة...")
        print("=" * 40)
        app.run()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")
    
    finally:
        print("🏁 انتهى التشغيل")

if __name__ == "__main__":
    main()
