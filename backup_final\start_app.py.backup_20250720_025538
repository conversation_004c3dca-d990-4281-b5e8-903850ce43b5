#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل برنامج ست الكل للمحاسبة
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def start_application():
    """تشغيل البرنامج"""
    try:
        print("🚀 بدء تشغيل برنامج ست الكل للمحاسبة...")
        
        # تهيئة نظام الجدولة
        from core.scheduler_manager import SchedulerManager
        scheduler_manager = SchedulerManager()
        scheduler_manager.start_scheduler()
        logging.info("تم تشغيل نظام الجدولة التلقائية للنسخ الاحتياطي")
        
        # تشغيل الواجهة الرئيسية
        from ui.main_window import MainApplication
        app = MainApplication()
        app.scheduler_manager = scheduler_manager
        
        print("✅ تم تشغيل البرنامج بنجاح!")
        print("🔑 بيانات الدخول: اسم المستخدم: 123، كلمة المرور: 123")
        
        app.run()
        
    except Exception as e:
        logging.error(f"خطأ في تشغيل البرنامج: {e}")
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_application()
