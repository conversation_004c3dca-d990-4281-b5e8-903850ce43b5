# -*- coding: utf-8 -*-
"""
مدير المهام المجدولة
يدير النسخ الاحتياطي التلقائي والمهام المجدولة الأخرى
"""

import logging
import atexit
from datetime import datetime, time
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from database.hybrid_database_manager import HybridDatabaseManager
from config.settings import PROJECT_ROOT
from config.scheduler_settings import (
    BACKUP_SETTINGS, CLEANUP_SETTINGS, SCHEDULER_SETTINGS,
    MESSAGES, get_backup_time, is_auto_backup_enabled,
    get_max_backups, get_timezone, get_backup_directory
)


class SchedulerManager:
    """مدير المهام المجدولة"""
    
    def __init__(self, db_manager=None):
        """
        تهيئة مدير المهام المجدولة
        
        Args:
            db_manager: مدير قاعدة البيانات (اختياري)
        """
        self.logger = logging.getLogger(__name__)
        self.scheduler = None
        self.db_manager = db_manager or HybridDatabaseManager()
        self.is_running = False
        
        # إعداد مجلد السجلات
        self.logs_dir = PROJECT_ROOT / "logs"
        self.logs_dir.mkdir(exist_ok=True)
        
        # إعداد مجلد النسخ الاحتياطية
        self.backups_dir = PROJECT_ROOT / get_backup_directory()
        self.backups_dir.mkdir(exist_ok=True)
        
        self.logger.info("تم تهيئة مدير المهام المجدولة")
    
    def start_scheduler(self):
        """بدء تشغيل المجدول"""
        try:
            if self.scheduler is not None and self.is_running:
                self.logger.warning("المجدول يعمل بالفعل")
                return
            
            # إنشاء مجدول خلفي
            self.scheduler = BackgroundScheduler(
                timezone=get_timezone(),
                daemon=SCHEDULER_SETTINGS.get('daemon', True)
            )
            
            # إضافة مستمع للأحداث
            self.scheduler.add_listener(
                self._job_listener,
                EVENT_JOB_EXECUTED | EVENT_JOB_ERROR
            )
            
            # إضافة المهام المجدولة
            self._add_scheduled_jobs()
            
            # بدء تشغيل المجدول
            self.scheduler.start()
            self.is_running = True
            
            # تسجيل إيقاف المجدول عند إغلاق البرنامج
            atexit.register(self.stop_scheduler)
            
            self.logger.info("تم بدء تشغيل مدير المهام المجدولة بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء تشغيل المجدول: {e}")
            raise
    
    def stop_scheduler(self):
        """إيقاف المجدول"""
        try:
            if self.scheduler is not None and self.is_running:
                self.scheduler.shutdown(wait=False)
                self.is_running = False
                self.logger.info("تم إيقاف مدير المهام المجدولة")
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف المجدول: {e}")
    
    def _add_scheduled_jobs(self):
        """إضافة المهام المجدولة"""
        try:
            # مهمة النسخ الاحتياطي اليومي (إذا كانت مفعلة)
            if is_auto_backup_enabled():
                backup_time = BACKUP_SETTINGS['daily_backup_time']
                self.scheduler.add_job(
                    func=self.backup_database,
                    trigger=CronTrigger(hour=backup_time['hour'], minute=backup_time['minute']),
                    id='daily_backup',
                    name='النسخ الاحتياطي اليومي',
                    replace_existing=True,
                    max_instances=SCHEDULER_SETTINGS.get('max_instances', 1)
                )
            
            # مهمة تنظيف النسخ الاحتياطية القديمة (أسبوعياً)
            if CLEANUP_SETTINGS['backup_cleanup']['enabled']:
                cleanup_backup = CLEANUP_SETTINGS['backup_cleanup']
                self.scheduler.add_job(
                    func=self.cleanup_old_backups,
                    trigger=CronTrigger(
                        day_of_week=cleanup_backup['day_of_week'],
                        hour=cleanup_backup['hour'],
                        minute=cleanup_backup['minute']
                    ),
                    id='weekly_cleanup',
                    name='تنظيف النسخ الاحتياطية القديمة',
                    replace_existing=True,
                    max_instances=SCHEDULER_SETTINGS.get('max_instances', 1)
                )

            # مهمة تنظيف ملفات السجلات القديمة (شهرياً)
            if CLEANUP_SETTINGS['logs_cleanup']['enabled']:
                cleanup_logs = CLEANUP_SETTINGS['logs_cleanup']
                self.scheduler.add_job(
                    func=self.cleanup_old_logs,
                    trigger=CronTrigger(
                        day=cleanup_logs['day'],
                        hour=cleanup_logs['hour'],
                        minute=cleanup_logs['minute']
                    ),
                    id='monthly_log_cleanup',
                    name='تنظيف ملفات السجلات القديمة',
                    replace_existing=True,
                    max_instances=SCHEDULER_SETTINGS.get('max_instances', 1)
                )
            
            self.logger.info("تم إضافة جميع المهام المجدولة بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المهام المجدولة: {e}")
            raise
    
    def backup_database(self):
        """تنفيذ النسخ الاحتياطي لقاعدة البيانات"""
        try:
            self.logger.info(MESSAGES['backup_success'])

            # إنشاء اسم ملف النسخة الاحتياطية
            timestamp = datetime.now().strftime(BACKUP_SETTINGS['timestamp_format'])
            backup_filename = f"{BACKUP_SETTINGS['backup_filename_prefix']}{timestamp}.db"
            backup_path = self.backups_dir / backup_filename
            
            # تنفيذ النسخ الاحتياطي
            result = self.db_manager.backup_database(str(backup_path))
            
            if result:
                self.logger.info(f"تم إنشاء النسخة الاحتياطية المجدولة بنجاح: {backup_filename}")
                
                # إضافة معلومات إضافية لملف السجل
                backup_size = backup_path.stat().st_size if backup_path.exists() else 0
                self.logger.info(f"حجم النسخة الاحتياطية: {backup_size / 1024:.2f} KB")
                
                return True
            else:
                self.logger.error("فشل في إنشاء النسخة الاحتياطية المجدولة")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في النسخ الاحتياطي المجدول: {e}")
            return False
    
    def cleanup_old_backups(self, keep_count=None):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            if keep_count is None:
                keep_count = get_max_backups()

            self.logger.info("بدء تنظيف النسخ الاحتياطية القديمة")
            
            # البحث عن جميع ملفات النسخ الاحتياطية
            backup_files = list(self.backups_dir.glob("*.db"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # حذف النسخ الاحتياطية الزائدة
            deleted_count = 0
            for backup_file in backup_files[keep_count:]:
                try:
                    backup_file.unlink()
                    deleted_count += 1
                    self.logger.info(f"تم حذف النسخة الاحتياطية القديمة: {backup_file.name}")
                except Exception as e:
                    self.logger.error(f"خطأ في حذف النسخة الاحتياطية {backup_file.name}: {e}")
            
            self.logger.info(f"تم تنظيف {deleted_count} نسخة احتياطية قديمة")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
            return 0
    
    def cleanup_old_logs(self, keep_days=90):
        """تنظيف ملفات السجلات القديمة"""
        try:
            self.logger.info("بدء تنظيف ملفات السجلات القديمة")

            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            # البحث عن ملفات السجلات القديمة
            log_files = list(self.logs_dir.glob("*.log*"))
            deleted_count = 0
            
            for log_file in log_files:
                try:
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_time < cutoff_date and log_file.name != "app.log":
                        log_file.unlink()
                        deleted_count += 1
                        self.logger.info(f"تم حذف ملف السجل القديم: {log_file.name}")
                except Exception as e:
                    self.logger.error(f"خطأ في حذف ملف السجل {log_file.name}: {e}")
            
            self.logger.info(f"تم تنظيف {deleted_count} ملف سجل قديم")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف ملفات السجلات القديمة: {e}")
            return 0
    
    def _job_listener(self, event):
        """مستمع أحداث المهام المجدولة"""
        try:
            job_name = event.job_id
            
            if event.exception:
                self.logger.error(f"فشل في تنفيذ المهمة المجدولة '{job_name}': {event.exception}")
            else:
                self.logger.info(f"تم تنفيذ المهمة المجدولة '{job_name}' بنجاح")
                
        except Exception as e:
            self.logger.error(f"خطأ في مستمع أحداث المهام: {e}")
    
    def get_scheduled_jobs(self):
        """الحصول على قائمة المهام المجدولة"""
        try:
            if self.scheduler is None:
                return []
            
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run': job.next_run_time,
                    'trigger': str(job.trigger)
                })
            
            return jobs
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المهام المجدولة: {e}")
            return []
    
    def add_custom_job(self, func, trigger, job_id, name, **kwargs):
        """إضافة مهمة مخصصة"""
        try:
            if self.scheduler is None:
                raise Exception("المجدول غير مفعل")
            
            self.scheduler.add_job(
                func=func,
                trigger=trigger,
                id=job_id,
                name=name,
                replace_existing=True,
                **kwargs
            )
            
            self.logger.info(f"تم إضافة المهمة المخصصة '{name}' بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المهمة المخصصة: {e}")
            return False
    
    def remove_job(self, job_id):
        """حذف مهمة مجدولة"""
        try:
            if self.scheduler is None:
                raise Exception("المجدول غير مفعل")
            
            self.scheduler.remove_job(job_id)
            self.logger.info(f"تم حذف المهمة '{job_id}' بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف المهمة '{job_id}': {e}")
            return False
