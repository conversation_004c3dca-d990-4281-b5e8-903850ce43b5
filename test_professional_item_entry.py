#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة إدخال الأصناف الاحترافية
Test Professional Item Entry Window
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from windows.item_entry_professional import ProfessionalItemEntry
    
    def test_professional_item_entry():
        """اختبار نافذة إدخال الأصناف الاحترافية"""
        print("🚀 بدء اختبار نافذة إدخال الأصناف الاحترافية...")
        print("=" * 60)
        print("📋 المميزات الجديدة:")
        print("   🎨 تصميم حديث ومتطور مع ألوان متدرجة")
        print("   🖥️ ملء الشاشة بالكامل مع تخطيط احترافي")
        print("   📦 قسم تعديل الأصناف على الجانب الأيمن")
        print("   🔍 بحث وفلترة الأصناف")
        print("   ✏️ تعديل وحذف الأصناف مباشرة")
        print("   🖼️ تحميل وعرض صور الأصناف")
        print("   💰 حساب الربحية مع مؤشرات بصرية")
        print("   📝 وصف مفصل للأصناف")
        print("   🎯 أيقونات ورموز تعبيرية جميلة")
        print("   ⌨️ اختصارات لوحة مفاتيح شاملة")
        print("")
        print("🎯 التخطيط:")
        print("   📌 الجانب الأيسر (70%): نموذج إدخال الصنف")
        print("   📌 الجانب الأيمن (30%): قائمة الأصناف الموجودة")
        print("   📌 شريط علوي: عنوان وأزرار التحكم")
        print("   📌 شريط سفلي: أزرار الحفظ والإلغاء")
        print("")
        print("⌨️ اختصارات لوحة المفاتيح:")
        print("   F11/Escape: تبديل ملء الشاشة")
        print("   Ctrl+S: حفظ الصنف")
        print("   Ctrl+N: صنف جديد")
        print("   Ctrl+F: البحث")
        print("   Ctrl+Q: إغلاق النافذة")
        print("")
        print("🎨 التصميم:")
        print("   🌈 ألوان متدرجة وحديثة")
        print("   ✨ تأثيرات بصرية وظلال")
        print("   🔤 خطوط Cairo العربية الجميلة")
        print("   📱 تصميم متجاوب مع جميع الشاشات")
        print("")
        print("🔧 الوظائف:")
        print("   ➕ إضافة أصناف جديدة")
        print("   ✏️ تعديل الأصناف الموجودة")
        print("   🗑️ حذف الأصناف")
        print("   🔍 البحث والفلترة")
        print("   🎲 توليد رموز تلقائي")
        print("   📊 حساب الربحية")
        print("   🖼️ إدارة الصور")
        print("")
        print("🎉 فتح النافذة الاحترافية...")
        print("=" * 60)
        
        # إنشاء وعرض النافذة
        app = ProfessionalItemEntry()
        app.show()
        
        print("✅ تم إغلاق النافذة بنجاح")
        print("🎊 انتهى الاختبار بنجاح!")

    if __name__ == "__main__":
        test_professional_item_entry()
        
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
