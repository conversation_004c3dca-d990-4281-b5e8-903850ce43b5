# -*- coding: utf-8 -*-
"""
اختبار تصغير الشعار
Test Logo Resize
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_logo_resize():
    """اختبار تصغير الشعار"""
    try:
        print("🧪 اختبار تصغير الشعار...")
        
        # استيراد البرنامج
        import large_font_run
        print("✅ تم استيراد البرنامج بنجاح")
        
        print("🎯 التغييرات المطبقة:")
        print("   📏 تم تصغير إطار الشعار من 320x160 إلى 200x100")
        print("   🖼️ تم تصغير صورة الشعار من 320x160 إلى 200x100")
        print("   📝 تم تصغير خط النص الاحتياطي من 28 إلى 18")
        print("   ✨ الشعار أصبح أكثر تناسقاً مع باقي العناصر")
        
        print("\n🎉 تم تصغير الشعار بنجاح!")
        print("📋 الفوائد:")
        print("   • توفير مساحة أكبر للأيقونات")
        print("   • تحسين التوازن البصري")
        print("   • مظهر أكثر احترافية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = test_logo_resize()
    if success:
        print("\n🚀 يمكنك الآن تشغيل البرنامج لرؤية الشعار المصغر:")
        print("   python large_font_run.py")
    sys.exit(0 if success else 1)
