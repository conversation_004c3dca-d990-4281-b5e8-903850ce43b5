#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البرنامج المحاسبي المُصلح
برنامج ست الكل للمحاسبة - الإصدار المحسن
تاريخ الإصلاح: 2025-07-16
"""

import sys
import logging
import traceback

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_enhanced_logging():
    """إعداد نظام السجلات المحسن"""
    try:
        # إنشاء مجلد السجلات
        logs_dir = project_root / "logs"
        logs_dir.mkdir(exist_ok=True)

        # إعداد التسجيل
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(logs_dir / 'app_fixed.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

        logger = logging.getLogger(__name__)
        logger.info("✅ تم إعداد نظام السجلات المحسن")
        return logger

    except Exception as e:
        pass
    except Exception as e:
        print(f"❌ خطأ في إعداد السجلات: {e}")
        return logging.getLogger(__name__)

def check_system_requirements():
    """فحص متطلبات النظام"""
    logger = logging.getLogger(__name__)

    try:
        # فحص إصدار Python
        if sys.version_info < (3, 8):
            raise Exception("يتطلب Python 3.8 أو أحدث")

        logger.info(f"✅ إصدار Python: {sys.version}")

        # فحص المكتبات المطلوبة
        required_modules = {
            'customtkinter': 'واجهة المستخدم الحديثة',
            'PIL': 'معالجة الصور',
            'sqlite3': 'قاعدة البيانات',
            'tkinter': 'واجهة المستخدم الأساسية',
            'threading': 'المعالجة المتوازية',
            'datetime': 'التاريخ والوقت'
        }

        missing_modules = []
        for module, description in required_modules.items():
            try:
                __import__(module)
                logger.info(f"✅ {module} ({description}): متوفر")
            except ImportError:
                missing_modules.append(f"{module} ({description})")
                logger.error(f"❌ {module} ({description}): غير متوفر")

        if missing_modules:
            raise Exception(f"المكتبات المفقودة:\n" + "\n".join(f"- {mod}" for mod in missing_modules))

        logger.info("✅ جميع المتطلبات متوفرة")
        return True

    except Exception as e:
        pass
    except Exception as e:
        logger.error(f"❌ خطأ في فحص المتطلبات: {e}")
        print(f"خطأ في فحص المتطلبات: {e}")
        return False

def check_project_structure():
    """فحص هيكل المشروع"""
    logger = logging.getLogger(__name__)

    try:
        # المجلدات الأساسية
        required_dirs = {
            'ui': 'واجهات المستخدم',
            'auth': 'نظام المصادقة',
            'database': 'قاعدة البيانات',
            'themes': 'الثيمات والألوان',
            'assets': 'الأصول والصور',
            'config': 'الإعدادات'
        }

        missing_dirs = []
        for dir_name, description in required_dirs.items():
            dir_path = project_root / dir_name
            if dir_path.exists():
                logger.info(f"✅ مجلد {dir_name} ({description}): موجود")
            else:
                missing_dirs.append(f"{dir_name} ({description})")
                logger.warning(f"⚠️ مجلد {dir_name} ({description}): غير موجود")

        # الملفات الأساسية
        required_files = {
            'main.py': 'الملف الرئيسي',
            'ui/main_window.py': 'النافذة الرئيسية',
            'ui/login_window.py': 'نافذة تسجيل الدخول',
            'ui/simple_welcome_window.py': 'نافذة الترحيب',
            'auth/auth_manager.py': 'مدير المصادقة'
        }

        missing_files = []
        for file_name, description in required_files.items():
            file_path = project_root / file_name
            if file_path.exists():
                logger.info(f"✅ ملف {file_name} ({description}): موجود")
            else:
                missing_files.append(f"{file_name} ({description})")
                logger.error(f"❌ ملف {file_name} ({description}): غير موجود")

        if missing_files:
            raise Exception(f"الملفات المفقودة:\n" + "\n".join(f"- {file}" for file in missing_files))

        logger.info("✅ هيكل المشروع سليم")
        return True

    except Exception as e:
        pass
    except Exception as e:
        logger.error(f"❌ خطأ في فحص هيكل المشروع: {e}")
        print(f"خطأ في هيكل المشروع: {e}")
        return False

def run_main_application():
    """تشغيل التطبيق الرئيسي"""
    logger = logging.getLogger(__name__)

    try:
        logger.info("🚀 بدء تشغيل برنامج ست الكل للمحاسبة المُصلح...")
        print("🚀 تشغيل البرنامج...")

        # استيراد التطبيق الرئيسي
        from ui.main_window import MainApplication
    except Exception as e:
        print(f"خطأ: {e}")
from pathlib import Path
        logger.info("✅ تم استيراد التطبيق الرئيسي")

        # إنشاء التطبيق
        app = MainApplication()
        logger.info("✅ تم إنشاء التطبيق")

        # تشغيل التطبيق
        logger.info("🎯 بدء تشغيل الواجهة الرسومية...")
        app.run()

        logger.info("✅ تم إنهاء التطبيق بنجاح")

    except ImportError as e:
        error_msg = f"خطأ في استيراد الوحدات: {e}"
        logger.error(f"❌ {error_msg}")
        print(f"❌ {error_msg}")
        print("تأكد من وجود جميع الملفات المطلوبة")

    except Exception as e:
        error_msg = f"خطأ في تشغيل التطبيق: {e}"
        logger.error(f"❌ {error_msg}")
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
        print(f"❌ {error_msg}")
        print("راجع ملف السجلات للحصول على تفاصيل أكثر")

def main():
    """الدالة الرئيسية المحسنة"""
    print("=" * 70)
    print("🏪 برنامج ست الكل للمحاسبة - الإصدار المُصلح والمحسن")
    print("📅 تاريخ الإصلاح: 2025-07-16")
    print("🔧 تم إصلاح: نافذة الترحيب، النافذة الرئيسية، عرض الصور")
    print("=" * 70)

    # إعداد السجلات
    logger = setup_enhanced_logging()

    try:
        # فحص متطلبات النظام
        print("🔍 فحص متطلبات النظام...")
        if not check_system_requirements():
            print("❌ فشل في فحص متطلبات النظام")
            input("اضغط Enter للخروج...")
            return

        # فحص هيكل المشروع
        print("📁 فحص هيكل المشروع...")
        if not check_project_structure():
            print("❌ فشل في فحص هيكل المشروع")
            input("اضغط Enter للخروج...")
            return

        print("✅ جميع الفحوصات نجحت!")
        print("🎉 البرنامج جاهز للتشغيل")

        # تشغيل التطبيق
        run_main_application()

    except KeyboardInterrupt:
        pass
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        print("\n⏹️ تم إيقاف البرنامج")

    except Exception as e:
        error_msg = f"خطأ عام في البرنامج: {e}"
        logger.error(f"❌ {error_msg}")
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
        print(f"❌ {error_msg}")

    finally:
        print("\n" + "=" * 50)
        print("👋 شكراً لاستخدام برنامج ست الكل للمحاسبة")
        print("💡 للدعم الفني: <EMAIL>")
        print("=" * 50)
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
