# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة إدارة المخزون المحسنة
Inventory Management Window
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from database.database_manager import DatabaseManager
try:
    from themes.modern_theme import MODERN_COLORS, FONTS
    from ui.window_utils import configure_window_fullscreen
except ImportError:
    MODERN_COLORS = {
        'primary': '#1f538d',
        'background': '#f0f0f0',
        'surface': '#ffffff',
        'text_primary': '#000000',
        'success': '#28a745',
        'error': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8'
    }
    FONTS = {'arabic': 'Arial'}

class InventoryWindow:
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.db_manager = DatabaseManager()
        self.inventory_items = []
        self.create_window()
        
    def create_window(self):
        """إنشاء نافذة إدارة المخزون"""
        try:
            self.window = ctk.CTkToplevel(self.parent)

            # إعداد النافذة لتملأ الشاشة
            configure_window_fullscreen(self.window, "إدارة المخزون - برنامج ست الكل للمحاسبة")

            # جعل النافذة في المقدمة
            self.window.transient(self.parent)
            self.window.grab_set()
            
            # إنشاء المحتوى
            self.create_header()
            self.create_main_content()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء نافذة المخزون: {str(e)}")
            
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = ctk.CTkFrame(self.window, height=100, fg_color=MODERN_COLORS['warning'])
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)
        
        # عنوان النافذة
        title_label = ctk.CTkLabel(
            header_frame,
            text="📦 إدارة المخزون والمستودعات",
            font=("Arial", 24, "bold"),
            text_color="white"
        )
        title_label.pack(side="right", padx=20, pady=15)
        
        # إحصائيات المخزون
        stats_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        stats_frame.pack(side="left", padx=20, pady=15)
        
        # عدد الأصناف
        items_frame = ctk.CTkFrame(stats_frame, fg_color="transparent")
        items_frame.pack(side="left", padx=10)
        
        ctk.CTkLabel(items_frame, text="عدد الأصناف:", 
                    font=("Arial", 12), text_color="white").pack()
        self.items_count_label = ctk.CTkLabel(
            items_frame,
            text="0",
            font=("Arial", 18, "bold"),
            text_color="yellow"
        )
        self.items_count_label.pack()
        
        # قيمة المخزون
        value_frame = ctk.CTkFrame(stats_frame, fg_color="transparent")
        value_frame.pack(side="left", padx=10)
        
        ctk.CTkLabel(value_frame, text="قيمة المخزون:", 
                    font=("Arial", 12), text_color="white").pack()
        self.inventory_value_label = ctk.CTkLabel(
            value_frame,
            text="0.00 ل.س",
            font=("Arial", 18, "bold"),
            text_color="yellow"
        )
        self.inventory_value_label.pack()
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        main_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تقسيم إلى قسمين
        # القسم العلوي - إضافة صنف جديد
        add_item_frame = ctk.CTkFrame(main_frame, height=200)
        add_item_frame.pack(fill="x", pady=(0, 10))
        add_item_frame.pack_propagate(False)
        
        # القسم السفلي - قائمة المخزون
        inventory_list_frame = ctk.CTkFrame(main_frame)
        inventory_list_frame.pack(fill="both", expand=True)
        
        # إنشاء محتوى الأقسام
        self.create_add_item_section(add_item_frame)
        self.create_inventory_list_section(inventory_list_frame)
        
    def create_add_item_section(self, parent):
        """إنشاء قسم إضافة صنف جديد"""
        # عنوان القسم
        title = ctk.CTkLabel(
            parent,
            text="إضافة صنف جديد للمخزون",
            font=("Arial", 18, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=(15, 10))
        
        # إطار الحقول
        fields_frame = ctk.CTkFrame(parent, fg_color="transparent")
        fields_frame.pack(fill="x", padx=20, pady=10)
        
        # الصف الأول
        row1 = ctk.CTkFrame(fields_frame, fg_color="transparent")
        row1.pack(fill="x", pady=5)
        
        # كود الصنف
        ctk.CTkLabel(row1, text="كود الصنف:", font=("Arial", 12)).pack(side="right", padx=10)
        self.item_code = ctk.CTkEntry(row1, width=120, placeholder_text="كود الصنف")
        self.item_code.pack(side="right", padx=10)
        
        # اسم الصنف
        ctk.CTkLabel(row1, text="اسم الصنف:", font=("Arial", 12)).pack(side="right", padx=10)
        self.item_name = ctk.CTkEntry(row1, width=200, placeholder_text="اسم الصنف")
        self.item_name.pack(side="right", padx=10)
        
        # الفئة
        ctk.CTkLabel(row1, text="الفئة:", font=("Arial", 12)).pack(side="right", padx=10)
        self.item_category = ctk.CTkComboBox(
            row1,
            values=["مواد خام", "منتجات تامة", "قطع غيار", "مستلزمات مكتبية", "أدوات", "أخرى"],
            width=150
        )
        self.item_category.pack(side="right", padx=10)
        self.item_category.set("منتجات تامة")
        
        # الصف الثاني
        row2 = ctk.CTkFrame(fields_frame, fg_color="transparent")
        row2.pack(fill="x", pady=5)
        
        # الكمية الحالية
        ctk.CTkLabel(row2, text="الكمية الحالية:", font=("Arial", 12)).pack(side="right", padx=10)
        self.item_quantity = ctk.CTkEntry(row2, width=120, placeholder_text="الكمية")
        self.item_quantity.pack(side="right", padx=10)
        
        # الوحدة
        ctk.CTkLabel(row2, text="الوحدة:", font=("Arial", 12)).pack(side="right", padx=10)
        self.item_unit = ctk.CTkComboBox(
            row2,
            values=["قطعة", "كيلو", "متر", "لتر", "علبة", "كرتونة", "طن"],
            width=100
        )
        self.item_unit.pack(side="right", padx=10)
        self.item_unit.set("قطعة")
        
        # سعر الشراء
        ctk.CTkLabel(row2, text="سعر الشراء:", font=("Arial", 12)).pack(side="right", padx=10)
        self.item_purchase_price = ctk.CTkEntry(row2, width=120, placeholder_text="سعر الشراء")
        self.item_purchase_price.pack(side="right", padx=10)
        
        # سعر البيع
        ctk.CTkLabel(row2, text="سعر البيع:", font=("Arial", 12)).pack(side="right", padx=10)
        self.item_sale_price = ctk.CTkEntry(row2, width=120, placeholder_text="سعر البيع")
        self.item_sale_price.pack(side="right", padx=10)
        
        # الصف الثالث
        row3 = ctk.CTkFrame(fields_frame, fg_color="transparent")
        row3.pack(fill="x", pady=5)
        
        # الحد الأدنى
        ctk.CTkLabel(row3, text="الحد الأدنى:", font=("Arial", 12)).pack(side="right", padx=10)
        self.item_min_quantity = ctk.CTkEntry(row3, width=120, placeholder_text="الحد الأدنى")
        self.item_min_quantity.pack(side="right", padx=10)
        
        # الموقع
        ctk.CTkLabel(row3, text="الموقع:", font=("Arial", 12)).pack(side="right", padx=10)
        self.item_location = ctk.CTkEntry(row3, width=150, placeholder_text="موقع التخزين")
        self.item_location.pack(side="right", padx=10)
        
        # أزرار العمليات
        ctk.CTkButton(
            row3,
            text="➕ إضافة الصنف",
            width=120,
            command=self.add_item,
            fg_color=MODERN_COLORS['success']
        ).pack(side="right", padx=20)
        
        ctk.CTkButton(
            row3,
            text="🔄 مسح الحقول",
            width=120,
            command=self.clear_fields,
            fg_color=MODERN_COLORS['warning']
        ).pack(side="left", padx=20)
        
    def create_inventory_list_section(self, parent):
        """إنشاء قسم قائمة المخزون"""
        # عنوان القسم
        title = ctk.CTkLabel(
            parent,
            text="قائمة أصناف المخزون",
            font=("Arial", 18, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=(15, 10))
        
        # شريط الأدوات
        toolbar = ctk.CTkFrame(parent, height=60)
        toolbar.pack(fill="x", padx=20, pady=5)
        toolbar.pack_propagate(False)
        
        # البحث
        ctk.CTkLabel(toolbar, text="البحث:", font=("Arial", 12)).pack(side="right", padx=5, pady=20)
        self.search_entry = ctk.CTkEntry(toolbar, width=200, placeholder_text="ابحث عن صنف...")
        self.search_entry.pack(side="right", padx=5, pady=20)
        self.search_entry.bind("<KeyRelease>", self.search_items)
        
        # فلتر الفئة
        ctk.CTkLabel(toolbar, text="الفئة:", font=("Arial", 12)).pack(side="right", padx=5, pady=20)
        self.category_filter = ctk.CTkComboBox(
            toolbar,
            values=["جميع الفئات", "مواد خام", "منتجات تامة", "قطع غيار", "مستلزمات مكتبية", "أدوات", "أخرى"],
            width=150,
            command=self.filter_items
        )
        self.category_filter.pack(side="right", padx=5, pady=20)
        self.category_filter.set("جميع الفئات")
        
        # أزرار العمليات
        ctk.CTkButton(toolbar, text="✏️ تعديل", width=80, 
                    command=self.edit_item).pack(side="left", padx=5, pady=20)
        ctk.CTkButton(toolbar, text="🗑️ حذف", width=80, 
                    command=self.delete_item, fg_color=MODERN_COLORS['error']).pack(side="left", padx=5, pady=20)
        ctk.CTkButton(toolbar, text="📊 تقرير", width=80, 
                    command=self.inventory_report, fg_color=MODERN_COLORS['info']).pack(side="left", padx=5, pady=20)
        ctk.CTkButton(toolbar, text="⚠️ نواقص", width=80, 
                    command=self.show_low_stock, fg_color=MODERN_COLORS['warning']).pack(side="left", padx=5, pady=20)
        ctk.CTkButton(toolbar, text="❌ إغلاق", width=80, 
                    command=self.close_window).pack(side="left", padx=5, pady=20)
        
        # جدول المخزون
        table_frame = ctk.CTkFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء Treeview
        columns = ("code", "name", "category", "quantity", "unit", "purchase_price", "sale_price", "min_qty", "location", "value")
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=18)
        
        # تعريف الأعمدة
        self.inventory_tree.heading("code", text="الكود")
        self.inventory_tree.heading("name", text="اسم الصنف")
        self.inventory_tree.heading("category", text="الفئة")
        self.inventory_tree.heading("quantity", text="الكمية")
        self.inventory_tree.heading("unit", text="الوحدة")
        self.inventory_tree.heading("purchase_price", text="سعر الشراء")
        self.inventory_tree.heading("sale_price", text="سعر البيع")
        self.inventory_tree.heading("min_qty", text="الحد الأدنى")
        self.inventory_tree.heading("location", text="الموقع")
        self.inventory_tree.heading("value", text="القيمة")
        
        # تحديد عرض الأعمدة
        self.inventory_tree.column("code", width=80)
        self.inventory_tree.column("name", width=150)
        self.inventory_tree.column("category", width=100)
        self.inventory_tree.column("quantity", width=80)
        self.inventory_tree.column("unit", width=60)
        self.inventory_tree.column("purchase_price", width=100)
        self.inventory_tree.column("sale_price", width=100)
        self.inventory_tree.column("min_qty", width=80)
        self.inventory_tree.column("location", width=100)
        self.inventory_tree.column("value", width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.inventory_tree.pack(side="right", fill="both", expand=True)
        scrollbar.pack(side="left", fill="y")
        
        # تحميل البيانات الحقيقية
        self.load_real_inventory()
        
    def add_item(self):
        """إضافة صنف جديد للمخزون"""
        try:
            code = self.item_code.get().strip()
            name = self.item_name.get().strip()
            category = self.item_category.get()
            quantity = self.item_quantity.get().strip()
            unit = self.item_unit.get()
            purchase_price = self.item_purchase_price.get().strip()
            sale_price = self.item_sale_price.get().strip()
            min_quantity = self.item_min_quantity.get().strip()
            location = self.item_location.get().strip()
            
            # التحقق من البيانات المطلوبة
            if not code or not name or not quantity or not purchase_price:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة: الكود، الاسم، الكمية، سعر الشراء")
                return
                
            # التحقق من صحة الأرقام
            try:
                qty = float(quantity)
                p_price = float(purchase_price)
                s_price = float(sale_price) if sale_price else p_price * 1.2  # هامش ربح افتراضي 20%
                min_qty = float(min_quantity) if min_quantity else 0
                
                if qty < 0 or p_price < 0 or s_price < 0:
                    messagebox.showerror("خطأ", "الكميات والأسعار يجب أن تكون أكبر من أو تساوي صفر")
                    return
                    
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للكمية والأسعار")
                return
                
            # التحقق من عدم تكرار الكود
            for item in self.inventory_items:
                if item['code'] == code:
                    messagebox.showerror("خطأ", "كود الصنف موجود مسبقاً")
                    return
                    
            # حساب القيمة الإجمالية
            total_value = qty * p_price
            
            # إنشاء الصنف الجديد
            inventory_item = {
                'code': code,
                'name': name,
                'category': category,
                'quantity': qty,
                'unit': unit,
                'purchase_price': p_price,
                'sale_price': s_price,
                'min_quantity': min_qty,
                'location': location or 'غير محدد',
                'value': total_value
            }
            
            self.inventory_items.append(inventory_item)
            self.update_inventory_display()
            self.update_statistics()
            self.clear_fields()
            
            messagebox.showinfo("نجح", f"تم إضافة الصنف {name} بنجاح!")
            
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إضافة الصنف: {str(e)}")
            
    def clear_fields(self):
        """مسح جميع الحقول"""
        self.item_code.delete(0, "end")
        self.item_name.delete(0, "end")
        self.item_quantity.delete(0, "end")
        self.item_purchase_price.delete(0, "end")
        self.item_sale_price.delete(0, "end")
        self.item_min_quantity.delete(0, "end")
        self.item_location.delete(0, "end")
        self.item_category.set("منتجات تامة")
        self.item_unit.set("قطعة")
        
    def update_inventory_display(self):
        """تحديث عرض قائمة المخزون"""
        # مسح البيانات السابقة
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
            
        # عرض الأصناف
        for item in self.inventory_items:
            # تحديد لون الصف حسب الكمية
            tags = ()
            if item['quantity'] <= item['min_quantity']:
                tags = ('low_stock',)
                
            self.inventory_tree.insert("", "end", values=(
                item['code'],
                item['name'],
                item['category'],
                f"{item['quantity']:.2f}",
                item['unit'],
                f"{item['purchase_price']:.2f}",
                f"{item['sale_price']:.2f}",
                f"{item['min_quantity']:.2f}",
                item['location'],
                f"{item['value']:.2f}"
            ), tags=tags)
            
        # تكوين ألوان الصفوف
        self.inventory_tree.tag_configure('low_stock', background='#ffcccc')
        
    def update_statistics(self):
        """تحديث إحصائيات المخزون"""
        total_items = len(self.inventory_items)
        total_value = sum(item['value'] for item in self.inventory_items)
        
        self.items_count_label.configure(text=str(total_items))
        self.inventory_value_label.configure(text=f"{total_value:,.2f} ل.س")
        
    def search_items(self, event=None):
        """البحث في الأصناف"""
        search_term = self.search_entry.get().lower().strip()
        category = self.category_filter.get()
        
        # مسح العرض السابق
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
            
        # فلترة وعرض النتائج
        for item in self.inventory_items:
            name_match = search_term in item['name'].lower() or search_term in item['code'].lower()
            category_match = category == "جميع الفئات" or item['category'] == category
            
            if name_match and category_match:
                tags = ()
                if item['quantity'] <= item['min_quantity']:
                    tags = ('low_stock',)
                    
                self.inventory_tree.insert("", "end", values=(
                    item['code'],
                    item['name'],
                    item['category'],
                    f"{item['quantity']:.2f}",
                    item['unit'],
                    f"{item['purchase_price']:.2f}",
                    f"{item['sale_price']:.2f}",
                    f"{item['min_quantity']:.2f}",
                    item['location'],
                    f"{item['value']:.2f}"
                ), tags=tags)
                
        self.inventory_tree.tag_configure('low_stock', background='#ffcccc')
        
    def filter_items(self, category):
        """فلترة الأصناف حسب الفئة"""
        self.search_items()
        
    def edit_item(self):
        """تعديل صنف محدد"""
        selected = self.inventory_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return
            
        messagebox.showinfo("تعديل", "سيتم فتح نافذة تعديل بيانات الصنف")
        
    def delete_item(self):
        """حذف صنف محدد"""
        selected = self.inventory_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الصنف؟"):
            # الحصول على بيانات الصنف المحدد
            item = self.inventory_tree.item(selected)
            item_code = item['values'][0]
            
            # حذف الصنف من القائمة
            self.inventory_items = [item for item in self.inventory_items if item['code'] != item_code]
            
            # تحديث العرض
            self.update_inventory_display()
            self.update_statistics()
            
            messagebox.showinfo("نجح", "تم حذف الصنف بنجاح!")
            
    def inventory_report(self):
        """تقرير المخزون"""
        messagebox.showinfo("تقرير المخزون", "سيتم إنشاء تقرير شامل للمخزون")
        
    def show_low_stock(self):
        """عرض الأصناف الناقصة"""
        low_stock_items = [item for item in self.inventory_items if item['quantity'] <= item['min_quantity']]
        
        if not low_stock_items:
            messagebox.showinfo("الأصناف الناقصة", "لا توجد أصناف تحت الحد الأدنى")
            return
            
        # إنشاء نافذة عرض الأصناف الناقصة
        low_stock_window = ctk.CTkToplevel(self.window)
        low_stock_window.title("الأصناف الناقصة")
        low_stock_window
        low_stock_window.transient(self.window)
        
        ctk.CTkLabel(low_stock_window, text="⚠️ الأصناف تحت الحد الأدنى", 
                    font=("Arial", 16, "bold"), text_color="red").pack(pady=20)
        
        # قائمة الأصناف الناقصة
        listbox = tk.Listbox(low_stock_window, font=("Arial", 12), height=15)
        listbox.pack(fill="both", expand=True, padx=20, pady=10)
        
        for item in low_stock_items:
            listbox.insert(tk.END, f"{item['name']} - الكمية: {item['quantity']:.2f} - الحد الأدنى: {item['min_quantity']:.2f}")
            
        ctk.CTkButton(low_stock_window, text="إغلاق", command=low_stock_window.destroy).pack(pady=10)

    def load_real_inventory(self):
        """تحميل المخزون الحقيقي من قاعدة البيانات"""
        try:
            # مسح البيانات السابقة
            for item in self.inventory_tree.get_children():
                self.inventory_tree.delete(item)

            # جلب المنتجات من قاعدة البيانات
            query = """
                SELECT
                    barcode,
                    name,
                    category,
                    current_stock,
                    cost_price,
                    selling_price,
                    unit,
                    min_stock_level,
                    is_active
                FROM products
                WHERE is_active = 1
                ORDER BY name
            """
            products = self.db_manager.fetch_all(query)

            self.inventory_items = []
            total_value = 0
            low_stock_count = 0

            for product in products:
                product_dict = dict(product)

                # حساب قيمة المخزون
                stock_value = product_dict['current_stock'] * product_dict['cost_price']
                total_value += stock_value

                # التحقق من المخزون المنخفض
                min_level = product_dict.get('min_stock_level', 0) or 0
                if product_dict['current_stock'] <= min_level:
                    low_stock_count += 1

                # تحديد حالة المخزون
                if product_dict['current_stock'] <= 0:
                    status = "نفد"
                elif product_dict['current_stock'] <= min_level:
                    status = "منخفض"
                else:
                    status = "متوفر"

                self.inventory_tree.insert("", "end", values=(
                    product_dict.get('barcode', ''),
                    product_dict['name'],
                    product_dict.get('category', ''),
                    f"{product_dict['current_stock']:.2f}",
                    f"{product_dict['cost_price']:.2f}",
                    f"{product_dict['selling_price']:.2f}",
                    product_dict.get('unit', 'قطعة'),
                    f"{stock_value:.2f}",
                    status
                ))

                self.inventory_items.append(product_dict)

            # تحديث الإحصائيات
            self.update_inventory_statistics(len(products), total_value, low_stock_count)

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المخزون: {e}")
            # تحميل بيانات تجريبية في حالة الخطأ
            self.load_sample_inventory()

    def update_inventory_statistics(self, total_items, total_value, low_stock_count):
        """تحديث إحصائيات المخزون"""
        try:
            # تحديث عدد الأصناف
            if hasattr(self, 'total_items_label'):
                self.total_items_label.configure(text=f"إجمالي الأصناف: {total_items}")

            # تحديث قيمة المخزون
            if hasattr(self, 'total_value_label'):
                self.total_value_label.configure(text=f"قيمة المخزون: {total_value:,.2f} ريال")

            # تحديث المخزون المنخفض
            if hasattr(self, 'low_stock_label'):
                self.low_stock_label.configure(text=f"مخزون منخفض: {low_stock_count} صنف")

        except Exception as e:
            pass
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def load_sample_inventory(self):
        """تحميل بيانات مخزون تجريبية"""
        sample_items = [
            {
                'code': 'P001',
                'name': 'لابتوب ديل',
                'category': 'منتجات تامة',
                'quantity': 15.0,
                'unit': 'قطعة',
                'purchase_price': 800000.0,
                'sale_price': 950000.0,
                'min_quantity': 5.0,
                'location': 'مستودع A',
                'value': 12000000.0
            },
            {
                'code': 'P002',
                'name': 'ماوس لاسلكي',
                'category': 'منتجات تامة',
                'quantity': 50.0,
                'unit': 'قطعة',
                'purchase_price': 15000.0,
                'sale_price': 20000.0,
                'min_quantity': 20.0,
                'location': 'مستودع B',
                'value': 750000.0
            },
            {
                'code': 'R001',
                'name': 'ورق A4',
                'category': 'مستلزمات مكتبية',
                'quantity': 3.0,
                'unit': 'كرتونة',
                'purchase_price': 25000.0,
                'sale_price': 30000.0,
                'min_quantity': 10.0,
                'location': 'مستودع C',
                'value': 75000.0
            },
            {
                'code': 'T001',
                'name': 'مفك براغي',
                'category': 'أدوات',
                'quantity': 25.0,
                'unit': 'قطعة',
                'purchase_price': 5000.0,
                'sale_price': 7500.0,
                'min_quantity': 15.0,
                'location': 'مستودع D',
                'value': 125000.0
            }
        ]
        
        self.inventory_items = sample_items
        self.update_inventory_display()
        self.update_statistics()
        
    def close_window(self):
        """إغلاق النافذة"""
        self.if window and hasattr(window, "destroy"):
    if window and hasattr(window, "destroy"):
    window.destroy()
