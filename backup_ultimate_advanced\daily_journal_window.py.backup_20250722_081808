# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة الحركة اليومية المحاسبية
Daily Journal Transactions Window
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, date, timedelta
import pandas as pd
# from tkcalendar import DateEntry  # سيتم استخدام Entry عادي

try:
    from themes.modern_theme import MODERN_COLORS, FONTS
except ImportError:
    pass
    print("تحذير: فشل في استيراد المكتبة")
    MODERN_COLORS = {
        'primary': '#1f538d',
        'secondary': '#2c3e50',
        'success': '#27ae60',
        'danger': '#e74c3c',
        'error': '#e74c3c',
        'warning': '#f39c12',
        'info': '#3498db',
        'background': '#ecf0f1',
        'surface': '#ffffff',
        'text_primary': '#2c3e50',
        'text_secondary': '#7f8c8d',
        'border': '#bdc3c7'
    }
    FONTS = {'arabic': 'Arial', 'english': 'Arial'}

class DailyJournalWindow:
    """نافذة الحركة اليومية المحاسبية الاحترافية"""

    def __init__(self, parent, db_manager=None, user_role="manager", user_id=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_role = user_role
        self.user_id = user_id
        self.window = None
        self.transactions_data = []
        self.filtered_data = []

        # إعداد النافذة
        self.create_window()

    def create_window(self):
        """إنشاء النافذة الرئيسية"""
        self.window = ctk.CTkToplevel(self.parent)

        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "📊 الحركة اليومية المحاسبية - برنامج ست الكل للمحاسبة")
        self.window
        self.window.configure(fg_color=MODERN_COLORS['background'])

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # إنشاء المحتوى
        self.create_header()
        self.create_toolbar()
        self.create_main_content()
        self.create_summary_panel()
        self.create_buttons()

        # تحميل البيانات الافتراضية
        self.load_today_transactions()

    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = ctk.CTkFrame(self.window, height=80, fg_color=MODERN_COLORS['primary'])
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        header_frame.pack_propagate(False)

        # الأيقونة والعنوان
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="right", padx=20, pady=15)

        title_label = ctk.CTkLabel(
            title_frame,
            text="📊 الحركة اليومية المحاسبية",
            font=(FONTS['arabic'], 24, "bold"),
            text_color="white"
        )
        title_label.pack()

        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="عرض وتحليل جميع الحركات المحاسبية اليومية",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        subtitle_label.pack()

        # معلومات المستخدم
        user_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        user_frame.pack(side="left", padx=20, pady=15)

        user_label = ctk.CTkLabel(
            user_frame,
            text=f"المستخدم: {self.user_role}",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        user_label.pack()

        date_label = ctk.CTkLabel(
            user_frame,
            text=f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        date_label.pack()

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ctk.CTkFrame(self.window, height=120, fg_color=MODERN_COLORS['surface'])
        toolbar_frame.pack(fill="x", padx=20, pady=(0, 10))
        toolbar_frame.pack_propagate(False)

        # الصف الأول - فلاتر التاريخ ونوع الحركة
        top_row = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        top_row.pack(fill="x", padx=10, pady=5)

        # فلتر التاريخ
        date_frame = ctk.CTkFrame(top_row, fg_color="transparent")
        date_frame.pack(side="right", padx=10)

        ctk.CTkLabel(date_frame, text="التاريخ:", font=(FONTS['arabic'], 12, "bold")).pack(side="right", padx=5)

        # تاريخ من
        ctk.CTkLabel(date_frame, text="من:", font=(FONTS['arabic'], 10)).pack(side="right", padx=(10, 5))
        self.start_date = ctk.CTkEntry(
            date_frame,
            width=120,
            placeholder_text="YYYY-MM-DD"
        )
        self.start_date.pack(side="right", padx=5)

        # تاريخ إلى
        ctk.CTkLabel(date_frame, text="إلى:", font=(FONTS['arabic'], 10)).pack(side="right", padx=(10, 5))
        self.end_date = ctk.CTkEntry(
            date_frame,
            width=120,
            placeholder_text="YYYY-MM-DD"
        )
        self.end_date.pack(side="right", padx=5)

        # نوع الحركة
        transaction_frame = ctk.CTkFrame(top_row, fg_color="transparent")
        transaction_frame.pack(side="right", padx=10)

        ctk.CTkLabel(transaction_frame, text="نوع الحركة:", font=(FONTS['arabic'], 12, "bold")).pack(side="right", padx=5)

        self.transaction_type = ctk.CTkComboBox(
            transaction_frame,
            values=["جميع الحركات", "بيع", "شراء", "صرف", "قبض", "مرتجع", "تحويل مخزن", "جرد"],
            width=150,
            font=(FONTS['arabic'], 11)
        )
        self.transaction_type.pack(side="right", padx=5)
        self.transaction_type.set("جميع الحركات")

        # المستخدم
        user_frame = ctk.CTkFrame(top_row, fg_color="transparent")
        user_frame.pack(side="right", padx=10)

        ctk.CTkLabel(user_frame, text="المستخدم:", font=(FONTS['arabic'], 12, "bold")).pack(side="right", padx=5)

        self.user_filter = ctk.CTkComboBox(
            user_frame,
            values=["جميع المستخدمين", "المدير", "المحاسب", "الكاشير"],
            width=120,
            font=(FONTS['arabic'], 11)
        )
        self.user_filter.pack(side="right", padx=5)
        self.user_filter.set("جميع المستخدمين")

        # الصف الثاني - أزرار التحكم
        bottom_row = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        bottom_row.pack(fill="x", padx=10, pady=5)

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(bottom_row, fg_color="transparent")
        buttons_frame.pack(side="right", padx=10)

        # زر عرض النتائج
        self.show_results_btn = ctk.CTkButton(
            buttons_frame,
            text="🔍 عرض النتائج",
            command=self.show_results,
            fg_color=MODERN_COLORS['primary'],
            width=120,
            height=35,
            font=(FONTS['arabic'], 12, "bold")
        )
        self.show_results_btn.pack(side="right", padx=5)

        # زر تصفية متقدمة
        advanced_filter_btn = ctk.CTkButton(
            buttons_frame,
            text="⚙️ تصفية متقدمة",
            command=self.show_advanced_filter,
            fg_color=MODERN_COLORS['info'],
            width=120,
            height=35,
            font=(FONTS['arabic'], 12)
        )
        advanced_filter_btn.pack(side="right", padx=5)

        # زر تحديث
        refresh_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث",
            command=self.refresh_data,
            fg_color=MODERN_COLORS['success'],
            width=100,
            height=35,
            font=(FONTS['arabic'], 12)
        )
        refresh_btn.pack(side="right", padx=5)

        # أزرار التصدير
        export_frame = ctk.CTkFrame(bottom_row, fg_color="transparent")
        export_frame.pack(side="left", padx=10)

        # زر تصدير Excel
        excel_btn = ctk.CTkButton(
            export_frame,
            text="📊 Excel",
            command=self.export_to_excel,
            fg_color=MODERN_COLORS['success'],
            width=80,
            height=35,
            font=(FONTS['arabic'], 11)
        )
        excel_btn.pack(side="left", padx=5)

        # زر تصدير PDF
        pdf_btn = ctk.CTkButton(
            export_frame,
            text="📄 PDF",
            command=self.export_to_pdf,
            fg_color=MODERN_COLORS['danger'],
            width=80,
            height=35,
            font=(FONTS['arabic'], 11)
        )
        pdf_btn.pack(side="left", padx=5)

        # زر طباعة
        print_btn = ctk.CTkButton(
            export_frame,
            text="🖨️ طباعة",
            command=self.print_report,
            fg_color=MODERN_COLORS['warning'],
            width=80,
            height=35,
            font=(FONTS['arabic'], 11)
        )
        print_btn.pack(side="left", padx=5)

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي - جدول البيانات"""
        # إطار المحتوى الرئيسي
        main_frame = ctk.CTkFrame(self.window, fg_color=MODERN_COLORS['surface'])
        main_frame.pack(fill="both", expand=True, padx=20, pady=(0, 10))

        # عنوان الجدول
        table_header = ctk.CTkFrame(main_frame, height=40, fg_color=MODERN_COLORS['secondary'])
        table_header.pack(fill="x", padx=10, pady=(10, 0))
        table_header.pack_propagate(False)

        table_title = ctk.CTkLabel(
            table_header,
            text="📋 سجل الحركات اليومية",
            font=(FONTS['arabic'], 16, "bold"),
            text_color="white"
        )
        table_title.pack(side="right", padx=20, pady=8)

        # عداد النتائج
        self.results_count_label = ctk.CTkLabel(
            table_header,
            text="عدد النتائج: 0",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.results_count_label.pack(side="left", padx=20, pady=8)

        # إطار الجدول
        table_frame = ctk.CTkFrame(main_frame, fg_color="white")
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # إنشاء Treeview للجدول
        columns = (
            "transaction_id", "date", "type", "document_no", "user",
            "debit_account", "credit_account", "amount", "currency", "description"
        )

        self.transactions_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=20,
            style="Custom.Treeview"
        )

        # تعريف رؤوس الأعمدة
        headers = {
            "transaction_id": "رقم الحركة",
            "date": "التاريخ",
            "type": "نوع الحركة",
            "document_no": "رقم المستند",
            "user": "المستخدم",
            "debit_account": "الحساب المدين",
            "credit_account": "الحساب الدائن",
            "amount": "القيمة",
            "currency": "العملة",
            "description": "البيان"
        }

        # إعداد رؤوس الأعمدة
        for col, header in headers.items():
            self.transactions_tree.heading(col, text=header, anchor="center")

        # تحديد عرض الأعمدة
        column_widths = {
            "transaction_id": 100,
            "date": 100,
            "type": 120,
            "document_no": 100,
            "user": 100,
            "debit_account": 150,
            "credit_account": 150,
            "amount": 120,
            "currency": 80,
            "description": 200
        }

        for col, width in column_widths.items():
            self.transactions_tree.column(col, width=width, anchor="center")

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=v_scrollbar.set)

        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.transactions_tree.xview)
        self.transactions_tree.configure(xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.transactions_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # إعداد التمدد
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # ربط الأحداث
        self.transactions_tree.bind("<Double-1>", self.on_transaction_double_click)
        self.transactions_tree.bind("<Button-3>", self.show_context_menu)

    def create_summary_panel(self):
        """إنشاء لوحة الملخص والإحصائيات"""
        summary_frame = ctk.CTkFrame(self.window, height=100, fg_color=MODERN_COLORS['surface'])
        summary_frame.pack(fill="x", padx=20, pady=(0, 10))
        summary_frame.pack_propagate(False)

        # عنوان اللوحة
        summary_title = ctk.CTkLabel(
            summary_frame,
            text="📊 ملخص الحركات",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        summary_title.pack(pady=(10, 5))

        # إطار الإحصائيات
        stats_frame = ctk.CTkFrame(summary_frame, fg_color="transparent")
        stats_frame.pack(fill="x", padx=20, pady=5)

        # إجمالي الحركات
        self.total_transactions_label = ctk.CTkLabel(
            stats_frame,
            text="إجمالي الحركات: 0",
            font=(FONTS['arabic'], 12, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        self.total_transactions_label.pack(side="right", padx=20)

        # إجمالي المدين
        self.total_debit_label = ctk.CTkLabel(
            stats_frame,
            text="إجمالي المدين: 0.00 ريال",
            font=(FONTS['arabic'], 12, "bold"),
            text_color=MODERN_COLORS['success']
        )
        self.total_debit_label.pack(side="right", padx=20)

        # إجمالي الدائن
        self.total_credit_label = ctk.CTkLabel(
            stats_frame,
            text="إجمالي الدائن: 0.00 ريال",
            font=(FONTS['arabic'], 12, "bold"),
            text_color=MODERN_COLORS['danger']
        )
        self.total_credit_label.pack(side="right", padx=20)

        # الرصيد
        self.balance_label = ctk.CTkLabel(
            stats_frame,
            text="الرصيد: 0.00 ريال",
            font=(FONTS['arabic'], 12, "bold"),
            text_color=MODERN_COLORS['warning']
        )
        self.balance_label.pack(side="left", padx=20)

    def create_buttons(self):
        """إنشاء أزرار النافذة"""
        buttons_frame = ctk.CTkFrame(self.window, height=60, fg_color=MODERN_COLORS['surface'])
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        buttons_frame.pack_propagate(False)

        # زر إضافة حركة جديدة
        add_transaction_btn = ctk.CTkButton(
            buttons_frame,
            text="➕ إضافة حركة جديدة",
            command=self.add_new_transaction,
            fg_color=MODERN_COLORS['success'],
            width=150,
            height=40,
            font=(FONTS['arabic'], 12, "bold")
        )
        add_transaction_btn.pack(side="right", padx=20, pady=10)

        # زر تعديل الحركة المحددة
        edit_transaction_btn = ctk.CTkButton(
            buttons_frame,
            text="✏️ تعديل الحركة",
            command=self.edit_selected_transaction,
            fg_color=MODERN_COLORS['info'],
            width=130,
            height=40,
            font=(FONTS['arabic'], 12, "bold")
        )
        edit_transaction_btn.pack(side="right", padx=(0, 10), pady=10)

        # زر حذف الحركة المحددة
        delete_transaction_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف الحركة",
            command=self.delete_selected_transaction,
            fg_color=MODERN_COLORS['danger'],
            width=130,
            height=40,
            font=(FONTS['arabic'], 12, "bold")
        )
        delete_transaction_btn.pack(side="right", padx=(0, 10), pady=10)

        # زر إغلاق
        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            command=self.close_window,
            fg_color=MODERN_COLORS['secondary'],
            width=100,
            height=40,
            font=(FONTS['arabic'], 12, "bold")
        )
        close_btn.pack(side="left", padx=20, pady=10)

        # زر مساعدة
        help_btn = ctk.CTkButton(
            buttons_frame,
            text="❓ مساعدة",
            command=self.show_help,
            fg_color=MODERN_COLORS['primary'],
            width=100,
            height=40,
            font=(FONTS['arabic'], 12)
        )
        help_btn.pack(side="left", padx=(0, 10), pady=10)

    def load_today_transactions(self):
        """تحميل حركات اليوم الحالي"""
        today = date.today().strftime("%Y-%m-%d")
        self.start_date.insert(0, today)
        self.end_date.insert(0, today)
        self.show_results()

    def show_results(self):
        """عرض النتائج حسب الفلاتر المحددة"""
        try:
            # الحصول على قيم الفلاتر
            start_date = self.start_date.get() or date.today().strftime("%Y-%m-%d")
            end_date = self.end_date.get() or date.today().strftime("%Y-%m-%d")
            transaction_type = self.transaction_type.get()
            user_filter = self.user_filter.get()

            # تحميل البيانات من قاعدة البيانات
            self.transactions_data = self.load_transactions_from_db(
                start_date, end_date, transaction_type, user_filter
            )

            # عرض البيانات في الجدول
            self.populate_transactions_table()

            # تحديث الملخص
            self.update_summary()

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {e}")

    def load_transactions_from_db(self, start_date, end_date, transaction_type, user_filter):
        """تحميل الحركات من قاعدة البيانات"""
        try:
            # بناء الاستعلام الأساسي
            query = """
            SELECT
                t.id as transaction_id,
                t.transaction_date as date,
                t.transaction_type as type,
                t.document_number as document_no,
                u.username as user,
                da.account_name as debit_account,
                ca.account_name as credit_account,
                t.amount,
                t.currency,
                t.description
            FROM transactions t
            LEFT JOIN users u ON t.user_id = u.id
            LEFT JOIN accounts da ON t.debit_account_id = da.id
            LEFT JOIN accounts ca ON t.credit_account_id = ca.id
            WHERE DATE(t.transaction_date) BETWEEN ? AND ?
            """

            params = [start_date, end_date]

            # إضافة فلتر نوع الحركة
            if transaction_type != "جميع الحركات":
                query += " AND t.transaction_type = ?"
                params.append(transaction_type)

            # إضافة فلتر المستخدم
            if user_filter != "جميع المستخدمين":
                query += " AND u.role = ?"
                params.append(user_filter)

            # إضافة فلتر الصلاحيات
            if self.user_role == "محاسب":
                query += " AND t.user_id = ?"
                params.append(self.user_id)
            elif self.user_role == "مستخدم":
                query += " AND (t.user_id = ? OR t.related_user_id = ?)"
                params.extend([self.user_id, self.user_id])

            query += " ORDER BY t.transaction_date DESC, t.id DESC"

            # تنفيذ الاستعلام
            if self.db_manager:
                results = self.db_manager.fetch_all(query, params)
            else:
                # بيانات وهمية للاختبار
                results = self.get_sample_data()

            return results

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الاستعلام: {e}")
            return []

    def get_sample_data(self):
        """بيانات وهمية للاختبار"""
        today = date.today()
        return [
            (1, today, "بيع", "INV-001", "الكاشير", "الصندوق", "المبيعات", 1500.00, "ريال", "بيع منتجات متنوعة"),
            (2, today, "شراء", "PUR-001", "المحاسب", "المشتريات", "الصندوق", 800.00, "ريال", "شراء مواد خام"),
            (3, today, "صرف", "EXP-001", "المدير", "مصاريف إدارية", "الصندوق", 200.00, "ريال", "مصاريف مكتبية"),
            (4, today, "قبض", "REC-001", "الكاشير", "الصندوق", "العملاء", 1200.00, "ريال", "تحصيل من عميل"),
            (5, today, "بيع", "INV-002", "الكاشير", "الصندوق", "المبيعات", 950.00, "ريال", "بيع أجهزة إلكترونية"),
        ]

    def populate_transactions_table(self):
        """ملء جدول الحركات بالبيانات"""
        # مسح البيانات السابقة
        for item in self.transactions_tree.get_children():
            self.transactions_tree.delete(item)

        # إضافة البيانات الجديدة
        for transaction in self.transactions_data:
            # تنسيق التاريخ
            if isinstance(transaction[1], str):
                formatted_date = transaction[1]
            else:
                formatted_date = transaction[1].strftime("%Y-%m-%d")

            # تنسيق المبلغ
            formatted_amount = f"{transaction[7]:,.2f}"

            # إدراج البيانات
            self.transactions_tree.insert("", "end", values=(
                transaction[0],  # رقم الحركة
                formatted_date,  # التاريخ
                transaction[2],  # نوع الحركة
                transaction[3],  # رقم المستند
                transaction[4],  # المستخدم
                transaction[5],  # الحساب المدين
                transaction[6],  # الحساب الدائن
                formatted_amount,  # المبلغ
                transaction[8],  # العملة
                transaction[9]   # البيان
            ))

        # تحديث عداد النتائج
        count = len(self.transactions_data)
        self.results_count_label.configure(text=f"عدد النتائج: {count}")

    def update_summary(self):
        """تحديث ملخص الإحصائيات"""
        if not self.transactions_data:
            self.total_transactions_label.configure(text="إجمالي الحركات: 0")
            self.total_debit_label.configure(text="إجمالي المدين: 0.00 ريال")
            self.total_credit_label.configure(text="إجمالي الدائن: 0.00 ريال")
            self.balance_label.configure(text="الرصيد: 0.00 ريال")
            return

        # حساب الإحصائيات
        total_count = len(self.transactions_data)
        total_debit = sum(float(t[7]) for t in self.transactions_data if t[2] in ["شراء", "صرف"])
        total_credit = sum(float(t[7]) for t in self.transactions_data if t[2] in ["بيع", "قبض"])
        balance = total_credit - total_debit

        # تحديث التسميات
        self.total_transactions_label.configure(text=f"إجمالي الحركات: {total_count}")
        self.total_debit_label.configure(text=f"إجمالي المدين: {total_debit:,.2f} ريال")
        self.total_credit_label.configure(text=f"إجمالي الدائن: {total_credit:,.2f} ريال")

        # تلوين الرصيد حسب القيمة
        balance_color = MODERN_COLORS['success'] if balance >= 0 else MODERN_COLORS['danger']
        self.balance_label.configure(
            text=f"الرصيد: {balance:,.2f} ريال",
            text_color=balance_color
        )

    def refresh_data(self):
        """تحديث البيانات"""
        self.show_results()
        messagebox.showinfo("تحديث", "تم تحديث البيانات بنجاح")

    def show_advanced_filter(self):
        """عرض نافذة التصفية المتقدمة"""
        messagebox.showinfo("تصفية متقدمة", "نافذة التصفية المتقدمة ستكون متاحة قريباً")

    def on_transaction_double_click(self, event):
        """عند النقر المزدوج على حركة"""
        selected_item = self.transactions_tree.selection()
        if selected_item:
            transaction_data = self.transactions_tree.item(selected_item[0])['values']
            self.show_transaction_details(transaction_data)

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        # إنشاء قائمة سياقية
        context_menu = tk.Menu(self.window, tearoff=0)
        context_menu.add_command(label="عرض التفاصيل", command=self.show_selected_transaction_details)
        context_menu.add_command(label="تعديل", command=self.edit_selected_transaction)
        context_menu.add_command(label="حذف", command=self.delete_selected_transaction)
        context_menu.add_separator()
        context_menu.add_command(label="نسخ", command=self.copy_selected_transaction)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            pass
        finally:
            context_menu.grab_release()

    def show_transaction_details(self, transaction_data):
        """عرض تفاصيل الحركة"""
        details_window = ctk.CTkToplevel(self.window)
        details_window.title("تفاصيل الحركة")
        details_window
        details_window.configure(fg_color=MODERN_COLORS['background'])

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            details_window,
            text=f"تفاصيل الحركة رقم: {transaction_data[0]}",
            font=(FONTS['arabic'], 18, "bold")
        )
        title_label.pack(pady=20)

        # إطار التفاصيل
        details_frame = ctk.CTkFrame(details_window)
        details_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عرض التفاصيل
        details = [
            ("رقم الحركة:", transaction_data[0]),
            ("التاريخ:", transaction_data[1]),
            ("نوع الحركة:", transaction_data[2]),
            ("رقم المستند:", transaction_data[3]),
            ("المستخدم:", transaction_data[4]),
            ("الحساب المدين:", transaction_data[5]),
            ("الحساب الدائن:", transaction_data[6]),
            ("القيمة:", transaction_data[7]),
            ("العملة:", transaction_data[8]),
            ("البيان:", transaction_data[9])
        ]

        for i, (label, value) in enumerate(details):
            detail_frame = ctk.CTkFrame(details_frame, fg_color="transparent")
            detail_frame.pack(fill="x", padx=10, pady=5)

            ctk.CTkLabel(detail_frame, text=str(value), font=(FONTS['arabic'], 12)).pack(side="left", padx=10)
            ctk.CTkLabel(detail_frame, text=label, font=(FONTS['arabic'], 12, "bold")).pack(side="right", padx=10)

    def show_selected_transaction_details(self):
        """عرض تفاصيل الحركة المحددة"""
        selected_item = self.transactions_tree.selection()
        if selected_item:
            transaction_data = self.transactions_tree.item(selected_item[0])['values']
            self.show_transaction_details(transaction_data)
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد حركة أولاً")

    def add_new_transaction(self):
        """إضافة حركة جديدة"""
        messagebox.showinfo("إضافة حركة", "نافذة إضافة حركة جديدة ستكون متاحة قريباً")

    def edit_selected_transaction(self):
        """تعديل الحركة المحددة"""
        selected_item = self.transactions_tree.selection()
        if selected_item:
            transaction_data = self.transactions_tree.item(selected_item[0])['values']
            messagebox.showinfo("تعديل", f"تعديل الحركة رقم: {transaction_data[0]}")
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد حركة للتعديل")

    def delete_selected_transaction(self):
        """حذف الحركة المحددة"""
        selected_item = self.transactions_tree.selection()
        if selected_item:
            transaction_data = self.transactions_tree.item(selected_item[0])['values']
            result = messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الحركة رقم: {transaction_data[0]}؟"
            )
            if result:
                # هنا يتم حذف الحركة من قاعدة البيانات
                messagebox.showinfo("حذف", "تم حذف الحركة بنجاح")
                self.refresh_data()
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد حركة للحذف")

    def copy_selected_transaction(self):
        """نسخ الحركة المحددة"""
        selected_item = self.transactions_tree.selection()
        if selected_item:
            transaction_data = self.transactions_tree.item(selected_item[0])['values']
            # نسخ البيانات إلى الحافظة
            clipboard_text = "\t".join(str(item) for item in transaction_data)
            self.window.clipboard_clear()
            self.window.clipboard_append(clipboard_text)
            messagebox.showinfo("نسخ", "تم نسخ بيانات الحركة إلى الحافظة")
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد حركة للنسخ")

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            if not self.transactions_data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            from tkinter import filedialog

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ تقرير الحركة اليومية"
            )

            if filename:
                # تحضير البيانات
                df_data = []
                for transaction in self.transactions_data:
                    df_data.append({
                        'رقم الحركة': transaction[0],
                        'التاريخ': transaction[1],
                        'نوع الحركة': transaction[2],
                        'رقم المستند': transaction[3],
                        'المستخدم': transaction[4],
                        'الحساب المدين': transaction[5],
                        'الحساب الدائن': transaction[6],
                        'القيمة': transaction[7],
                        'العملة': transaction[8],
                        'البيان': transaction[9]
                    })

                # إنشاء DataFrame
                df = pd.DataFrame(df_data)

                # حفظ الملف
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='الحركة اليومية', index=False)

                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى: {filename}")

        except ImportError:
    print("تحذير: فشل في استيراد المكتبة")
        except ImportError:
            messagebox.showerror("خطأ", "مكتبة pandas غير مثبتة. يرجى تثبيتها أولاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير Excel: {e}")

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        try:
            if not self.transactions_data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            from reportlab.lib.pagesizes import A4, landscape
            from reportlab.pdfgen import canvas
            from reportlab.lib.units import inch
        except Exception as e:
            print(f"خطأ: {e}")
from ui.window_utils import configure_window_fullscreen
from tkinter import filedialog

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ تقرير الحركة اليومية"
            )

            if filename:
                # إنشاء ملف PDF
                c = canvas.Canvas(filename, pagesize=landscape(A4))
                width, height = landscape(A4)

                # العنوان
                c.setFont("Helvetica-Bold", 16)
                c.drawString(100, height - 50, "Daily Journal Transactions Report")
                c.drawString(100, height - 70, f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}")

                # رؤوس الأعمدة
                headers = ["ID", "Date", "Type", "Doc#", "User", "Debit", "Credit", "Amount", "Currency"]
                x_positions = [50, 100, 150, 220, 280, 350, 450, 550, 650]

                y_position = height - 120
                c.setFont("Helvetica-Bold", 10)
                for i, header in enumerate(headers):
                    c.drawString(x_positions[i], y_position, header)

                # البيانات
                c.setFont("Helvetica", 9)
                y_position -= 20

                for transaction in self.transactions_data[:30]:  # أول 30 حركة:
                    if y_position < 50:
                        c.showPage()
                        y_position = height - 50

                    data_row = [
                        str(transaction[0]),
                        str(transaction[1])[:10],
                        str(transaction[2])[:15],
                        str(transaction[3])[:10],
                        str(transaction[4])[:15],
                        str(transaction[5])[:20],
                        str(transaction[6])[:20],
                        f"{transaction[7]:,.2f}",
                        str(transaction[8])
                    ]

                    for i, data in enumerate(data_row):
                        c.drawString(x_positions[i], y_position, data)

                    y_position -= 15

                c.save()
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى: {filename}")

        except ImportError:
            messagebox.showerror("خطأ", "مكتبة reportlab غير مثبتة. يرجى تثبيتها أولاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير PDF: {e}")

    def print_report(self):
        """طباعة التقرير"""
        if not self.transactions_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للطباعة")
            return

        messagebox.showinfo("طباعة", "ميزة الطباعة ستكون متاحة قريباً")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
📊 نافذة الحركة اليومية المحاسبية

الميزات الرئيسية:
• عرض جميع الحركات المحاسبية اليومية
• تصفية حسب التاريخ ونوع الحركة والمستخدم
• عرض ملخص الإحصائيات والأرصدة
• تصدير إلى Excel و PDF
• طباعة التقارير

كيفية الاستخدام:
1. اختر الفترة الزمنية المطلوبة
2. حدد نوع الحركة والمستخدم
3. اضغط "عرض النتائج"
4. استخدم النقر المزدوج لعرض التفاصيل
5. استخدم القائمة السياقية للإجراءات

الصلاحيات:
• المدير: يرى جميع الحركات
• المحاسب: يرى الحركات التي أضافها
• المستخدم: يرى الحركات المرتبطة به
        """

        help_window = ctk.CTkToplevel(self.window)
        help_window.title("مساعدة - الحركة اليومية")
        help_window
        help_window.configure(fg_color=MODERN_COLORS['background'])

        help_text_widget = ctk.CTkTextbox(help_window)
        help_text_widget.pack(fill="both", expand=True, padx=20, pady=20)
        help_text_widget.insert("1.0", help_text)
        help_text_widget.configure(state="disabled")

    def close_window(self):
        """إغلاق النافذة"""
        if hasattr(self, 'window') and self.window:

            self.window.destroy()
