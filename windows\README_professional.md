# 🏪 نافذة إدخال الأصناف الاحترافية
## Professional Item Entry Window

### 📋 نظرة عامة
تم تطوير نافذة إدخال الأصناف الاحترافية (`item_entry_professional.py`) لتوفير واجهة حديثة ومتطورة لإدارة الأصناف في نظام المحاسبة. تتميز هذه النافذة بتصميم عصري وألوان متدرجة ووظائف متقدمة.

### 🎨 المميزات الجديدة

#### التصميم والجمالية
- **🌈 ألوان متدرجة حديثة**: نظام ألوان احترافي مع تدرجات جميلة
- **✨ تأثيرات بصرية**: ظلال وتأثيرات hover للعناصر التفاعلية
- **🔤 خطوط Cairo العربية**: دعم كامل للغة العربية مع خطوط جميلة
- **📱 تصميم متجاوب**: يتكيف مع جميع أحجام الشاشات
- **🎯 أيقونات ورموز تعبيرية**: تحسين التجربة البصرية

#### التخطيط والتنظيم
- **📌 تخطيط مقسم**: الجانب الأيسر (70%) للإدخال، الأيمن (30%) لإدارة الأصناف
- **🖥️ ملء الشاشة**: استغلال كامل لمساحة الشاشة
- **📊 شريط علوي احترافي**: عنوان وأزرار التحكم
- **🔧 شريط سفلي**: أزرار الحفظ والإلغاء والإغلاق

#### الوظائف المتقدمة
- **➕ إضافة أصناف جديدة**: نموذج شامل لإدخال بيانات الصنف
- **✏️ تعديل الأصناف**: تحرير الأصناف الموجودة مباشرة
- **🗑️ حذف الأصناف**: حذف آمن مع تأكيد
- **🔍 البحث والفلترة**: بحث فوري في قائمة الأصناف
- **🎲 توليد رموز تلقائي**: نظام ذكي لتوليد رموز الأصناف
- **💰 حساب الربحية**: حساب تلقائي لنسبة الربح مع مؤشرات بصرية
- **🖼️ إدارة الصور**: تحميل وعرض صور الأصناف
- **📝 وصف مفصل**: حقل نص لوصف الصنف

### ⌨️ اختصارات لوحة المفاتيح

| الاختصار | الوظيفة |
|----------|---------|
| `F11` / `Escape` | تبديل ملء الشاشة |
| `Ctrl+S` | حفظ الصنف |
| `Ctrl+N` | صنف جديد |
| `Ctrl+F` | التركيز على البحث |
| `Ctrl+Q` | إغلاق النافذة |

### 🎯 أقسام النافذة

#### 1. الشريط العلوي
- **العنوان**: عنوان ديناميكي حسب الوضع (إضافة/تعديل)
- **الوصف**: وصف مختصر للوظيفة
- **أزرار التحكم**: جديد، ملء الشاشة، مساعدة

#### 2. الجانب الأيسر - نموذج الإدخال (70%)
##### قسم المعلومات الأساسية 📋
- **اسم الصنف**: حقل إجباري لاسم الصنف
- **رمز الصنف**: رمز فريد مع إمكانية التوليد التلقائي
- **التصنيف**: قائمة منسدلة للتصنيفات
- **الوحدة**: وحدة القياس (قطعة، كيلو، متر، إلخ)

##### قسم الأسعار والربحية 💰
- **سعر التكلفة**: تكلفة الصنف
- **سعر البيع**: سعر البيع للعملاء
- **نسبة الربح**: حساب تلقائي مع مؤشر لوني

##### قسم الصورة 🖼️
- **عرض الصورة**: معاينة صورة الصنف
- **تحميل صورة**: رفع صورة من الجهاز
- **حذف صورة**: إزالة الصورة الحالية

##### قسم الوصف 📝
- **حقل نص متعدد الأسطر**: وصف تفصيلي للصنف

#### 3. الجانب الأيمن - إدارة الأصناف (30%)
- **شريط البحث**: بحث فوري في الأصناف
- **قائمة الأصناف**: عرض جميع الأصناف مع إمكانية التمرير
- **بطاقات الأصناف**: عرض معلومات مختصرة لكل صنف
- **أزرار العمليات**: تعديل وحذف لكل صنف

#### 4. الشريط السفلي
- **حفظ الصنف**: حفظ البيانات في قاعدة البيانات
- **إلغاء**: مسح النموذج
- **إغلاق**: إغلاق النافذة

### 🔧 التقنيات المستخدمة

#### المكتبات
- **Tkinter**: واجهة المستخدم الرئيسية
- **PIL/Pillow**: معالجة الصور
- **SQLite**: قاعدة البيانات المحلية
- **Threading**: العمليات غير المتزامنة

#### الأنماط والألوان
```python
colors = {
    'primary': '#2c3e50',      # أزرق داكن
    'secondary': '#3498db',    # أزرق فاتح
    'accent': '#e74c3c',       # أحمر
    'success': '#27ae60',      # أخضر
    'warning': '#f39c12',      # برتقالي
    'light': '#ecf0f1',        # رمادي فاتح
    'dark': '#34495e',         # رمادي داكن
    'white': '#ffffff',        # أبيض
}
```

#### الخطوط
```python
fonts = {
    'title': ('Cairo', 20, 'bold'),
    'subtitle': ('Cairo', 14, 'bold'),
    'body': ('Cairo', 12),
    'small': ('Cairo', 10),
    'button': ('Cairo', 11, 'bold'),
    'icon': ('Segoe UI Emoji', 16)
}
```

### 🚀 كيفية الاستخدام

#### 1. فتح النافذة
```python
from windows.item_entry_professional import ProfessionalItemEntry

# إنشاء نافذة مستقلة
app = ProfessionalItemEntry()
app.show()

# أو كنافذة فرعية
app = ProfessionalItemEntry(parent=main_window)
```

#### 2. إضافة صنف جديد
1. املأ اسم الصنف (مطلوب)
2. أدخل رمز الصنف أو استخدم التوليد التلقائي
3. اختر التصنيف والوحدة
4. أدخل الأسعار لحساب الربح
5. أضف صورة ووصف (اختياري)
6. اضغط "حفظ الصنف"

#### 3. تعديل صنف موجود
1. ابحث عن الصنف في القائمة اليمنى
2. اضغط زر التعديل ✏️
3. عدل البيانات المطلوبة
4. اضغط "حفظ الصنف"

#### 4. حذف صنف
1. ابحث عن الصنف في القائمة
2. اضغط زر الحذف 🗑️
3. أكد الحذف في النافذة المنبثقة

### 📊 مؤشرات الربحية

النافذة تحسب نسبة الربح تلقائياً وتعرضها بألوان مختلفة:
- **🟢 أخضر**: ربح أكثر من 30%
- **🟡 أصفر**: ربح بين 15-30%
- **🔴 أحمر**: ربح أقل من 15%

### 🔒 الأمان والتحقق

- **التحقق من البيانات**: فحص صحة البيانات قبل الحفظ
- **رسائل خطأ واضحة**: إرشادات مفصلة للمستخدم
- **تأكيد الحذف**: منع الحذف العرضي
- **حفظ آمن**: معالجة الأخطاء أثناء الحفظ

### 🐛 استكشاف الأخطاء

#### مشاكل شائعة وحلولها

1. **خطأ في تحميل الصورة**
   - تأكد من صيغة الصورة المدعومة (PNG, JPG, JPEG, GIF, BMP)
   - تحقق من حجم الصورة

2. **خطأ في حفظ البيانات**
   - تأكد من ملء الحقول المطلوبة
   - تحقق من صحة الأسعار المدخلة

3. **مشاكل في العرض**
   - تأكد من دقة الشاشة المناسبة
   - جرب تبديل وضع ملء الشاشة

### 📈 التطوير المستقبلي

- **🌐 دعم قواعد بيانات متقدمة**: PostgreSQL, MySQL
- **📱 واجهة محمولة**: تطوير نسخة للهواتف الذكية
- **🔄 مزامنة سحابية**: ربط مع الخدمات السحابية
- **📊 تقارير متقدمة**: تحليلات مفصلة للأصناف
- **🎨 ثيمات متعددة**: خيارات تخصيص إضافية

### 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- استخدم زر "❓ مساعدة" في النافذة
- راجع هذا الدليل للمعلومات التفصيلية
- تحقق من ملفات السجل للأخطاء التقنية
