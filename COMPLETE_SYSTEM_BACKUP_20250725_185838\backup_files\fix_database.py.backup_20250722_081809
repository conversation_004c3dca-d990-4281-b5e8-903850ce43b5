# -*- coding: utf-8 -*-
"""
إصلاح وتحديث قاعدة البيانات
Database Fix and Update
"""

import sqlite3
import logging

class DatabaseFixer:
    """مصلح قاعدة البيانات"""

    def __init__(self, db_path="database/accounting.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        return sqlite3.connect(self.db_path)

    def check_table_exists(self, table_name):
        """التحقق من وجود جدول"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            return cursor.fetchone() is not None

    def check_column_exists(self, table_name, column_name):
        """التحقق من وجود عمود في جدول"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [row[1] for row in cursor.fetchall()]
            return column_name in columns

    def create_missing_tables(self):
        """إنشاء الجداول المفقودة"""
        print("🔧 إنشاء الجداول المفقودة...")

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # جدول التصنيفات الهرمية
            if not self.check_table_exists('item_categories'):
                print("   📂 إنشاء جدول التصنيفات...")
                cursor.execute("""
                    CREATE TABLE item_categories (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name_ar TEXT NOT NULL,
                        name_en TEXT,
                        parent_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (parent_id) REFERENCES item_categories(id) ON DELETE SET NULL
                    )
                """)

                # إدراج بيانات أولية
                categories_data = [
                    (1, 'إلكترونيات', 'Electronics', None),
                    (2, 'أجهزة كمبيوتر', 'Computers', 1),
                    (3, 'لابتوب', 'Laptops', 2),
                    (4, 'أجهزة مكتبية', 'Desktop', 2),
                    (5, 'هواتف ذكية', 'Smartphones', 1),
                    (6, 'ملابس', 'Clothing', None),
                    (7, 'ملابس رجالية', 'Men's Clothing', 6),
                    (8, 'ملابس نسائية', 'Women's Clothing', 6),
                    (9, 'أحذية', 'Shoes', 6),
                    (10, 'طعام ومشروبات', 'Food & Beverages', None),
                    (11, 'مشروبات ساخنة', 'Hot Beverages', 10),
                    (12, 'مشروبات باردة', 'Cold Beverages', 10),
                    (13, 'خدمات', 'Services', None)
                ]

                cursor.executemany("""
                    INSERT OR IGNORE INTO item_categories (id, name_ar, name_en, parent_id) 
                    VALUES (?, ?, ?, ?)
                """, categories_data)

            # جدول وحدات القياس
            if not self.check_table_exists('units'):
                print("   📏 إنشاء جدول وحدات القياس...")
                cursor.execute("""
                    CREATE TABLE units (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name_ar TEXT NOT NULL,
                        name_en TEXT,
                        symbol TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # إدراج بيانات أولية
                units_data = [
                    (1, 'قطعة', 'Piece', 'قطعة'),
                    (2, 'كيلوجرام', 'Kilogram', 'كجم'),
                    (3, 'جرام', 'Gram', 'جم'),
                    (4, 'لتر', 'Liter', 'لتر'),
                    (5, 'متر', 'Meter', 'م'),
                    (6, 'سنتيمتر', 'Centimeter', 'سم'),
                    (7, 'علبة', 'Box', 'علبة'),
                    (8, 'كرتونة', 'Carton', 'كرتون'),
                    (9, 'دزينة', 'Dozen', 'دزينة'),
                    (10, 'طن', 'Ton', 'طن'),
                    (11, 'باوند', 'Pound', 'باوند'),
                    (12, 'ساعة', 'Hour', 'ساعة')
                ]

                cursor.executemany("""
                    INSERT OR IGNORE INTO units (id, name_ar, name_en, symbol) 
                    VALUES (?, ?, ?, ?)
                """, units_data)

            # جدول المخازن
            if not self.check_table_exists('warehouses'):
                print("   🏪 إنشاء جدول المخازن...")
                cursor.execute("""
                    CREATE TABLE warehouses (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        warehouse_code TEXT UNIQUE NOT NULL,
                        name TEXT NOT NULL,
                        description TEXT,
                        address TEXT,
                        city TEXT,
                        phone TEXT,
                        manager_name TEXT,
                        manager_phone TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # إدراج بيانات أولية
                warehouses_data = [
                    (1, 'WH001', 'المخزن الرئيسي', 'المخزن الرئيسي للشركة', '', '', '', '', '', 1),
                    (2, 'WH002', 'مخزن الفرع الأول', 'مخزن فرع المدينة', '', '', '', '', '', 1),
                    (3, 'WH003', 'مخزن الفرع الثاني', 'مخزن فرع الضواحي', '', '', '', '', '', 1),
                    (4, 'WH004', 'مخزن المواد الخام', 'مخزن خاص بالمواد الخام', '', '', '', '', '', 1),
                    (5, 'WH005', 'مخزن المنتجات الجاهزة', 'مخزن المنتجات النهائية', '', '', '', '', '', 1)
                ]

                cursor.executemany("""
                    INSERT OR IGNORE INTO warehouses 
                    (id, warehouse_code, name, description, address, city, phone, manager_name, manager_phone, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, warehouses_data)

            # جدول حركات المخزون
            if not self.check_table_exists('stock_movements'):
                print("   🔄 إنشاء جدول حركات المخزون...")
                cursor.execute("""
                    CREATE TABLE stock_movements (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        item_id INTEGER NOT NULL,
                        warehouse_id INTEGER NOT NULL,
                        movement_type TEXT NOT NULL,
                        quantity REAL NOT NULL,
                        cost_price REAL DEFAULT 0.0,
                        reference_no TEXT,
                        date DATE DEFAULT CURRENT_DATE,
                        notes TEXT,
                        created_by INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE,
                        FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE
                    )
                """)

            # جدول الأصناف المحدث
            if not self.check_table_exists('items'):
                print("   📦 إنشاء جدول الأصناف...")
                cursor.execute("""
                    CREATE TABLE items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        item_code TEXT UNIQUE NOT NULL,
                        name_ar TEXT NOT NULL,
                        name_en TEXT,
                        description TEXT,
                        category_id INTEGER,
                        unit_id INTEGER,
                        secondary_unit_id INTEGER,
                        conversion_factor REAL DEFAULT 1.0,
                        default_warehouse_id INTEGER,
                        cost_price REAL DEFAULT 0.0,
                        sale_price REAL DEFAULT 0.0,
                        wholesale_price REAL DEFAULT 0.0,
                        last_purchase_price REAL DEFAULT 0.0,
                        vat_rate REAL DEFAULT 0.0,
                        min_stock REAL DEFAULT 0.0,
                        max_stock REAL DEFAULT 0.0,
                        reorder_level REAL DEFAULT 0.0,
                        serial_tracking BOOLEAN DEFAULT 0,
                        batch_tracking BOOLEAN DEFAULT 0,
                        expiry_date_required BOOLEAN DEFAULT 0,
                        is_service BOOLEAN DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        image_path TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (category_id) REFERENCES item_categories(id) ON DELETE SET NULL,
                        FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE SET NULL,
                        FOREIGN KEY (secondary_unit_id) REFERENCES units(id) ON DELETE SET NULL,
                        FOREIGN KEY (default_warehouse_id) REFERENCES warehouses(id) ON DELETE SET NULL
                    )
                """)

                # إدراج بيانات أولية للأصناف
                items_data = [
                    (1, 'ITM001', 'لابتوب ديل', 'Dell Laptop', 'لابتوب ديل عالي الأداء', 3, 1, None, 1.0, 1, 2500.00, 3000.00, 2800.00, 2500.00, 15.0, 5, 50, 10, 0, 0, 0, 0, 1, None),
                    (2, 'ITM002', 'قميص قطني', 'Cotton Shirt', 'قميص قطني عالي الجودة', 7, 1, None, 1.0, 2, 50.00, 75.00, 65.00, 50.00, 15.0, 20, 200, 50, 0, 0, 0, 0, 1, None),
                    (3, 'ITM003', 'قهوة عربية', 'Arabic Coffee', 'قهوة عربية أصيلة', 11, 2, None, 1.0, 1, 80.00, 120.00, 100.00, 80.00, 0.0, 50, 500, 100, 0, 1, 1, 0, 1, None),
                    (4, 'ITM004', 'دفتر ملاحظات', 'Notebook', 'دفتر ملاحظات مقوى', 4, 1, None, 1.0, 1, 15.00, 25.00, 20.00, 15.00, 15.0, 100, 1000, 200, 0, 0, 0, 0, 1, None),
                    (5, 'ITM005', 'زيت محرك 5W30', 'Engine Oil 5W30', 'زيت محرك عالي الجودة', 1, 4, None, 1.0, 4, 22.50, 35.00, 30.00, 22.50, 15.0, 50, 500, 100, 0, 1, 1, 0, 1, None)
                ]

                cursor.executemany("""
                    INSERT OR IGNORE INTO items
                    (id, item_code, name_ar, name_en, description, category_id, unit_id, secondary_unit_id,
                     conversion_factor, default_warehouse_id, cost_price, sale_price, wholesale_price,
                     last_purchase_price, vat_rate, min_stock, max_stock, reorder_level, serial_tracking,
                     batch_tracking, expiry_date_required, is_service, is_active, image_path)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, items_data)

            # جدول الأرصدة اللحظية
            if not self.check_table_exists('stock_balance'):
                print("   📊 إنشاء جدول الأرصدة اللحظية...")
                cursor.execute("""
                    CREATE TABLE stock_balance (
                        item_id INTEGER NOT NULL,
                        warehouse_id INTEGER NOT NULL,
                        quantity REAL DEFAULT 0.0,
                        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (item_id, warehouse_id),
                        FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE,
                        FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE
                    )
                """)

            conn.commit()
            print("✅ تم إنشاء جميع الجداول المفقودة بنجاح")

    def update_items_table(self):
        """تحديث جدول الأصناف"""
        print("🔧 تحديث جدول الأصناف...")

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # إضافة الأعمدة المفقودة
            new_columns = [
                ('category_id', 'INTEGER'),
                ('unit_id', 'INTEGER'),
                ('secondary_unit_id', 'INTEGER'),
                ('conversion_factor', 'REAL DEFAULT 1.0'),
                ('default_warehouse_id', 'INTEGER'),
                ('wholesale_price', 'REAL DEFAULT 0.0'),
                ('last_purchase_price', 'REAL DEFAULT 0.0'),
                ('vat_rate', 'REAL DEFAULT 0.0'),
                ('min_stock', 'REAL DEFAULT 0.0'),
                ('max_stock', 'REAL DEFAULT 0.0'),
                ('reorder_level', 'REAL DEFAULT 0.0'),
                ('serial_tracking', 'BOOLEAN DEFAULT 0'),
                ('batch_tracking', 'BOOLEAN DEFAULT 0'),
                ('expiry_date_required', 'BOOLEAN DEFAULT 0'),
                ('is_service', 'BOOLEAN DEFAULT 0'),
                ('image_path', 'TEXT')
            ]

            for column_name, column_type in new_columns:
                if not self.check_column_exists('items', column_name):
                    try:
                        cursor.execute(f"ALTER TABLE items ADD COLUMN {column_name} {column_type}")
                        print(f"   ✅ تم إضافة العمود: {column_name}")
                    except sqlite3.Error as e:
                        print(f"   ⚠️ خطأ في إضافة العمود {column_name}: {e}")

            conn.commit()
            print("✅ تم تحديث جدول الأصناف بنجاح")

    def create_indexes(self):
        """إنشاء الفهارس للأداء"""
        print("🔧 إنشاء الفهارس...")

        with self.get_connection() as conn:
            cursor = conn.cursor()

            indexes = [
                # فهارس التصنيفات
                "CREATE INDEX IF NOT EXISTS idx_categories_parent ON item_categories(parent_id)",
                "CREATE INDEX IF NOT EXISTS idx_categories_name_ar ON item_categories(name_ar)",

                # فهارس الوحدات
                "CREATE INDEX IF NOT EXISTS idx_units_name_ar ON units(name_ar)",
                "CREATE INDEX IF NOT EXISTS idx_units_symbol ON units(symbol)",

                # فهارس المخازن
                "CREATE INDEX IF NOT EXISTS idx_warehouses_code ON warehouses(warehouse_code)",
                "CREATE INDEX IF NOT EXISTS idx_warehouses_active ON warehouses(is_active)",

                # فهارس الأصناف
                "CREATE INDEX IF NOT EXISTS idx_items_code ON items(item_code)",
                "CREATE INDEX IF NOT EXISTS idx_items_name_ar ON items(name_ar)",
                "CREATE INDEX IF NOT EXISTS idx_items_category ON items(category_id)",
                "CREATE INDEX IF NOT EXISTS idx_items_active ON items(is_active)",

                # فهارس حركات المخزون
                "CREATE INDEX IF NOT EXISTS idx_movements_item ON stock_movements(item_id)",
                "CREATE INDEX IF NOT EXISTS idx_movements_warehouse ON stock_movements(warehouse_id)",
                "CREATE INDEX IF NOT EXISTS idx_movements_type ON stock_movements(movement_type)",
                "CREATE INDEX IF NOT EXISTS idx_movements_date ON stock_movements(date)",

                # فهارس الأرصدة
                "CREATE INDEX IF NOT EXISTS idx_balance_item ON stock_balance(item_id)",
                "CREATE INDEX IF NOT EXISTS idx_balance_warehouse ON stock_balance(warehouse_id)"
            ]

            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                    print(f"   ✅ تم إنشاء فهرس")
                except sqlite3.Error as e:
                    print(f"   ⚠️ خطأ في إنشاء فهرس: {e}")

            conn.commit()
            print("✅ تم إنشاء جميع الفهارس بنجاح")

    def fix_database(self):
        """إصلاح قاعدة البيانات بالكامل"""
        print("🚀 بدء إصلاح قاعدة البيانات...")
        print("=" * 50)

        try:
            # إنشاء الجداول المفقودة
            self.create_missing_tables()

            # تحديث جدول الأصناف
            self.update_items_table()

            # إنشاء الفهارس
            self.create_indexes()

            print("=" * 50)
            print("🎉 تم إصلاح قاعدة البيانات بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
            raise

def main():
    """تشغيل إصلاح قاعدة البيانات"""
    fixer = DatabaseFixer()
    fixer.fix_database()

if __name__ == "__main__":
    main()
