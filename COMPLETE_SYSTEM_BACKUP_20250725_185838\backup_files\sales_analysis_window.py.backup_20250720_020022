# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة تحليل المبيعات الاحترافية
Professional Sales Analysis Window
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, date, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from database.database_manager import DatabaseManager
try:
    from themes.modern_theme import MODERN_COLORS, FONTS
except ImportError:
    MODERN_COLORS = {
        'primary': '#1f538d',
        'background': '#f0f0f0',
        'surface': '#ffffff',
        'text_primary': '#000000',
        'success': '#28a745',
        'error': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8'
    }
    FONTS = {'arabic': 'Arial'}

# تكوين matplotlib للعربية
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SalesAnalysisWindow:
    """نافذة تحليل المبيعات الاحترافية مع الرسوم البيانية التفاعلية"""
    
    def __init__(self, parent, db_manager=None):
        self.parent = parent
        self.window = None
        self.db_manager = db_manager or DatabaseManager()
        self.current_data = None
        self.chart_canvas = None
        self.chart_figure = None

        self.create_window()
    
    def create_window(self):
        """إنشاء النافذة الرئيسية"""
        self.window = ctk.CTkToplevel(self.parent)
        
        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "📊 تحليل المبيعات - برنامج ست الكل للمحاسبة")
        self.window
        self.window.configure(fg_color="#2b2b2b")  # خلفية داكنة مثل الصورة

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # إنشاء المحتوى بنفس تخطيط الصورة
        self.create_dashboard_layout()

    def create_dashboard_layout(self):
        """إنشاء تخطيط لوحة المعلومات بنفس شكل الصورة"""
        # إنشاء الإطار الرئيسي
        main_container = ctk.CTkFrame(self.window, fg_color="#2b2b2b")
        main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # الجزء العلوي - البطاقات الإحصائية
        self.create_stats_cards(main_container)

        # الجزء الأوسط - أزرار التحكم والفلاتر
        self.create_control_buttons(main_container)

        # الجزء السفلي - الرسم البياني والبيانات
        self.create_chart_and_data_section(main_container)

    def create_stats_cards(self, parent):
        """إنشاء البطاقات الإحصائية العلوية"""
        # إطار البطاقات
        cards_frame = ctk.CTkFrame(parent, fg_color="transparent", height=150)
        cards_frame.pack(fill="x", padx=10, pady=(10, 5))
        cards_frame.pack_propagate(False)

        # الصف الأول من البطاقات
        top_row = ctk.CTkFrame(cards_frame, fg_color="transparent", height=70)
        top_row.pack(fill="x", pady=(0, 5))
        top_row.pack_propagate(False)

        # بطاقة إضافة فاتورة أول
        add_invoice_frame = ctk.CTkFrame(top_row, fg_color="transparent")
        add_invoice_frame.pack(side="right", padx=5)

        add_btn = ctk.CTkButton(
            add_invoice_frame,
            text="إضافة\nفاتورة أول",
            width=80,
            height=60,
            fg_color="#e74c3c",
            font=("Arial", 12, "bold"),
            corner_radius=8
        )
        add_btn.pack(side="top")

        # بطاقة فاتورة مشتريات جديدة
        purchase_btn = ctk.CTkButton(
            add_invoice_frame,
            text="فاتورة\nمشتريات\nجديدة",
            width=80,
            height=60,
            fg_color="#f39c12",
            font=("Arial", 10, "bold"),
            corner_radius=8
        )
        purchase_btn.pack(side="bottom", pady=(5, 0))

        # البطاقات الإحصائية الرئيسية
        stats_container = ctk.CTkFrame(top_row, fg_color="transparent")
        stats_container.pack(side="right", fill="x", expand=True, padx=(10, 5))

        # بيانات البطاقات
        cards_data = [
            {"title": "الموظفين", "value": "1", "color": "#3498db"},
            {"title": "العملاء والموردين", "value": "7", "color": "#e67e22"},
            {"title": "المشتريات (هذا الشهر)", "value": "10", "color": "#1abc9c"},
            {"title": "المبيعات (هذا الشهر)", "value": "8", "color": "#9b59b6"}
        ]

        for i, card in enumerate(cards_data):
            card_frame = ctk.CTkFrame(stats_container, fg_color=card["color"], corner_radius=8)
            card_frame.pack(side="right", fill="both", expand=True, padx=2)

            # القيمة
            value_label = ctk.CTkLabel(
                card_frame,
                text=card["value"],
                font=("Arial", 36, "bold"),
                text_color="white"
            )
            value_label.pack(pady=(10, 0))

            # العنوان
            title_label = ctk.CTkLabel(
                card_frame,
                text=card["title"],
                font=("Arial", 10),
                text_color="white",
                wraplength=100
            )
            title_label.pack(pady=(0, 10))

    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم والفلاتر"""
        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(parent, fg_color="transparent", height=100)
        buttons_frame.pack(fill="x", padx=10, pady=5)
        buttons_frame.pack_propagate(False)

        # الجانب الأيمن - أزرار الإجراءات
        right_buttons = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        right_buttons.pack(side="right", padx=5)

        # فاتورة مبيعات جديدة
        sales_btn = ctk.CTkButton(
            right_buttons,
            text="فاتورة مبيعات جديدة",
            width=120,
            height=35,
            fg_color="#2980b9",
            font=("Arial", 10, "bold"),
            corner_radius=8
        )
        sales_btn.pack(side="top", pady=2)

        # أزرار الإجراءات السفلية
        action_buttons_frame = ctk.CTkFrame(right_buttons, fg_color="transparent")
        action_buttons_frame.pack(side="bottom", fill="x")

        actions = [
            {"text": "فتح", "color": "#27ae60"},
            {"text": "طبع", "color": "#e67e22"},
            {"text": "عمل", "color": "#8e44ad"}
        ]

        for action in actions:
            btn = ctk.CTkButton(
                action_buttons_frame,
                text=action["text"],
                width=35,
                height=25,
                fg_color=action["color"],
                font=("Arial", 9),
                corner_radius=5
            )
            btn.pack(side="right", padx=1)

        # الوسط - أزرار الفلاتر
        filter_buttons = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        filter_buttons.pack(side="right", fill="x", expand=True, padx=10)

        # أزرار الفلاتر الرئيسية
        filters = [
            {"text": "أرباح الشهر", "color": "#3498db"},
            {"text": "أرباح الأسبوع", "color": "#3498db"},
            {"text": "أرباح اليوم", "color": "#3498db"}
        ]

        for filter_btn in filters:
            btn = ctk.CTkButton(
                filter_buttons,
                text=filter_btn["text"],
                width=100,
                height=30,
                fg_color=filter_btn["color"],
                font=("Arial", 10),
                corner_radius=8
            )
            btn.pack(side="right", padx=5)

        # الجانب الأيسر - أزرار إضافية
        left_buttons = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        left_buttons.pack(side="left", padx=5)

        # زر إعدادات
        settings_btn = ctk.CTkButton(
            left_buttons,
            text="⚙",
            width=40,
            height=40,
            fg_color="#34495e",
            font=("Arial", 16),
            corner_radius=8
        )
        settings_btn.pack(side="bottom")

        # زر آخر
        other_btn = ctk.CTkButton(
            left_buttons,
            text="❄",
            width=40,
            height=40,
            fg_color="#e74c3c",
            font=("Arial", 16),
            corner_radius=8
        )
        other_btn.pack(side="bottom", pady=(0, 5))

        # زر حفظ صفحات
        save_btn = ctk.CTkButton(
            left_buttons,
            text="حفظ صفحات",
            width=80,
            height=25,
            fg_color="#f39c12",
            font=("Arial", 9),
            corner_radius=5
        )
        save_btn.pack(side="top")

    def create_chart_and_data_section(self, parent):
        """إنشاء قسم الرسم البياني والبيانات"""
        # إطار القسم السفلي
        bottom_section = ctk.CTkFrame(parent, fg_color="transparent")
        bottom_section.pack(fill="both", expand=True, padx=10, pady=5)

        # الجانب الأيمن - قائمة المشتريات
        right_panel = ctk.CTkFrame(bottom_section, fg_color="#34495e", width=300, corner_radius=8)
        right_panel.pack(side="right", fill="y", padx=(5, 0))
        right_panel.pack_propagate(False)

        # عنوان قائمة المشتريات
        purchases_title = ctk.CTkLabel(
            right_panel,
            text="المشتريات المبيعات",
            font=("Arial", 14, "bold"),
            text_color="white"
        )
        purchases_title.pack(pady=(10, 5))

        # قائمة المشتريات
        purchases_data = [
            {"amount": "3000", "date": "19/04/19"},
            {"amount": "320", "date": "19/04/19"},
            {"amount": "440", "date": "19/04/19"},
            {"amount": "500", "date": "19/04/19"},
            {"amount": "560", "date": "19/04/19"},
            {"amount": "150", "date": "19/04/19"}
        ]

        for item in purchases_data:
            item_frame = ctk.CTkFrame(right_panel, fg_color="#2c3e50", corner_radius=5)
            item_frame.pack(fill="x", padx=10, pady=2)

            # التاريخ
            date_label = ctk.CTkLabel(
                item_frame,
                text=item["date"],
                font=("Arial", 10),
                text_color="#bdc3c7"
            )
            date_label.pack(side="left", padx=10, pady=5)

            # المبلغ
            amount_label = ctk.CTkLabel(
                item_frame,
                text=f"{item['amount']}ريال",
                font=("Arial", 10, "bold"),
                text_color="white"
            )
            amount_label.pack(side="right", padx=10, pady=5)

        # الجانب الأيسر - الرسم البياني
        chart_panel = ctk.CTkFrame(bottom_section, fg_color="#34495e", corner_radius=8)
        chart_panel.pack(side="left", fill="both", expand=True, padx=(0, 5))

        # إنشاء الرسم البياني
        self.create_area_chart_widget(chart_panel)

    def create_area_chart_widget(self, parent):
        """إنشاء الرسم البياني المنطقي بنفس شكل الصورة"""
        try:
            import numpy as np

            # إنشاء الشكل
            fig = Figure(figsize=(10, 6), facecolor='#34495e')
            ax = fig.add_subplot(111, facecolor='#34495e')

            # بيانات الرسم البياني (مطابقة للصورة)
            dates = ['19/04/2019', '20/04/2019', '21/04/2019', '22/04/2019', '23/04/2019', '24/04/2019']

            # البيانات للمناطق المختلفة
            area1 = [2, 2.5, 3, 2.8, 3.2, 2.5]  # المنطقة الخضراء
            area2 = [1, 1.2, 1.5, 1.8, 2, 1.5]  # المنطقة الصفراء
            area3 = [0.5, 0.8, 1, 1.2, 1.5, 1]  # المنطقة البرتقالية

            x = np.arange(len(dates))

            # رسم المناطق
            ax.fill_between(x, 0, area3, color='#e67e22', alpha=0.8, label='منطقة 1')
            ax.fill_between(x, area3, [a3 + a2 for a3, a2 in zip(area3, area2)],
                            color='#f1c40f', alpha=0.8, label='منطقة 2')
            ax.fill_between(x, [a3 + a2 for a3, a2 in zip(area3, area2)],
                            [a3 + a2 + a1 for a3, a2, a1 in zip(area3, area2, area1)],
                            color='#27ae60', alpha=0.8, label='منطقة 3')

            # تخصيص المحاور
            ax.set_xlim(0, len(dates)-1)
            ax.set_ylim(0, 6)

            # إعداد التواريخ
            ax.set_xticks(x)
            ax.set_xticklabels(dates, color='white', fontsize=9)

            # إعداد المحور Y
            ax.set_yticks([0, 1, 2, 3, 4, 5, 6])
            ax.set_yticklabels(['0', '1', '2', '3', '4', '5', '6'], color='white', fontsize=9)

            # إزالة الحدود
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['bottom'].set_color('white')
            ax.spines['left'].set_color('white')

            # إعداد الشبكة
            ax.grid(True, alpha=0.3, color='white')

            # إضافة عنوان
            ax.set_title('تحليل المبيعات', color='white', fontsize=14, fontweight='bold', pad=20)

            # إنشاء Canvas
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)

        except ImportError:
            pass
        except ImportError:
            # في حالة عدم توفر matplotlib
            error_label = ctk.CTkLabel(
                parent,
                text="الرسم البياني غير متوفر\nيرجى تثبيت matplotlib",
                font=("Arial", 16),
                text_color="white"
            )
            error_label.pack(expand=True)



    def close_window(self):
        """إغلاق النافذة"""
        if self.window:
            self.window.destroy()

    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = ctk.CTkFrame(self.window, height=80, fg_color=MODERN_COLORS['primary'])
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = ctk.CTkLabel(
            header_frame,
            text="📊 تحليل المبيعات الاحترافي",
            font=(FONTS['arabic'], 24, "bold"),
            text_color="white"
        )
        title_label.pack(side="right", padx=20, pady=20)
        
        # معلومات سريعة
        info_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        info_frame.pack(side="left", padx=20, pady=15)
        
        self.period_label = ctk.CTkLabel(
            info_frame,
            text="الفترة: هذا الشهر",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.period_label.pack()
        
        self.total_sales_label = ctk.CTkLabel(
            info_frame,
            text="إجمالي المبيعات: 0.00 ريال",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.total_sales_label.pack()
    
    def create_controls(self):
        """إنشاء عناصر التحكم والفلترة"""
        controls_frame = ctk.CTkFrame(self.window, height=120)
        controls_frame.pack(fill="x", padx=10, pady=10)
        controls_frame.pack_propagate(False)
        
        # الصف الأول - الفترة الزمنية
        period_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        period_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(period_frame, text="الفترة الزمنية:", font=(FONTS['arabic'], 12, "bold")).pack(side="right", padx=5)
        
        self.period_combo = ctk.CTkComboBox(
            period_frame,
            values=["اليوم", "هذا الأسبوع", "هذا الشهر", "هذا الربع", "هذا العام", "فترة مخصصة"],
            width=150,
            command=self.on_period_change
        )
        self.period_combo.pack(side="right", padx=5)
        self.period_combo.set("هذا الشهر")
        
        # تواريخ مخصصة
        ctk.CTkLabel(period_frame, text="من:", font=(FONTS['arabic'], 12)).pack(side="right", padx=(20, 5))
        self.start_date_entry = ctk.CTkEntry(period_frame, width=120, placeholder_text="YYYY-MM-DD")
        self.start_date_entry.pack(side="right", padx=5)
        
        ctk.CTkLabel(period_frame, text="إلى:", font=(FONTS['arabic'], 12)).pack(side="right", padx=5)
        self.end_date_entry = ctk.CTkEntry(period_frame, width=120, placeholder_text="YYYY-MM-DD")
        self.end_date_entry.pack(side="right", padx=5)
        
        # الصف الثاني - نوع التحليل والرسم البياني
        analysis_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        analysis_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(analysis_frame, text="نوع التحليل:", font=(FONTS['arabic'], 12, "bold")).pack(side="right", padx=5)
        
        self.analysis_type_combo = ctk.CTkComboBox(
            analysis_frame,
            values=["حسب المنتج", "حسب الفئة", "حسب العميل", "حسب التاريخ", "حسب المبيعات اليومية"],
            width=150,
            command=self.on_analysis_type_change
        )
        self.analysis_type_combo.pack(side="right", padx=5)
        self.analysis_type_combo.set("حسب المنتج")
        
        ctk.CTkLabel(analysis_frame, text="نوع الرسم:", font=(FONTS['arabic'], 12, "bold")).pack(side="right", padx=(20, 5))
        
        self.chart_type_combo = ctk.CTkComboBox(
            analysis_frame,
            values=["رسم عمودي", "رسم دائري", "رسم خطي", "رسم مساحي"],
            width=120,
            command=self.on_chart_type_change
        )
        self.chart_type_combo.pack(side="right", padx=5)
        self.chart_type_combo.set("رسم عمودي")
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(analysis_frame, fg_color="transparent")
        buttons_frame.pack(side="left", padx=20)
        
        self.analyze_btn = ctk.CTkButton(
            buttons_frame,
            text="🔍 تحليل",
            command=self.perform_analysis,
            fg_color=MODERN_COLORS['success'],
            width=100,
            height=35
        )
        self.analyze_btn.pack(side="left", padx=5)
        
        ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث",
            command=self.refresh_data,
            fg_color=MODERN_COLORS['info'],
            width=80,
            height=35
        ).pack(side="left", padx=5)
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تبويب الرسم البياني
        self.chart_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.chart_frame, text="الرسم البياني")
        
        # تبويب البيانات التفصيلية
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text="البيانات التفصيلية")
        
        # تبويب الإحصائيات
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="الإحصائيات")
        
        # إنشاء محتوى التبويبات
        self.create_chart_tab()
        self.create_data_tab()
        self.create_stats_tab()
    
    def create_chart_tab(self):
        """إنشاء تبويب الرسم البياني"""
        # إطار الرسم البياني
        chart_container = ctk.CTkFrame(self.chart_frame)
        chart_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء Figure للرسم البياني
        self.chart_figure = Figure(figsize=(12, 8), dpi=100)
        self.chart_figure.patch.set_facecolor('white')
        
        # إنشاء Canvas
        self.chart_canvas = FigureCanvasTkAgg(self.chart_figure, chart_container)
        self.chart_canvas.draw()
        self.chart_canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)
        
        # شريط أدوات الرسم البياني
        chart_toolbar_frame = ctk.CTkFrame(chart_container, height=40, fg_color="transparent")
        chart_toolbar_frame.pack(fill="x", padx=10, pady=(0, 10))
        chart_toolbar_frame.pack_propagate(False)
        
        ctk.CTkButton(
            chart_toolbar_frame,
            text="💾 حفظ الرسم",
            command=self.save_chart,
            width=100,
            height=30
        ).pack(side="right", padx=5)
        
        ctk.CTkButton(
            chart_toolbar_frame,
            text="🖨️ طباعة",
            command=self.print_chart,
            width=80,
            height=30
        ).pack(side="right", padx=5)
        
        ctk.CTkButton(
            chart_toolbar_frame,
            text="🔍 تكبير",
            command=self.zoom_chart,
            width=80,
            height=30
        ).pack(side="left", padx=5)
    
    def create_data_tab(self):
        """إنشاء تبويب البيانات التفصيلية"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(self.data_frame)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء Treeview للبيانات
        columns = ("item", "quantity", "amount", "percentage")
        self.data_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)
        
        # تعريف الأعمدة
        self.data_tree.heading("item", text="البند")
        self.data_tree.heading("quantity", text="الكمية")
        self.data_tree.heading("amount", text="المبلغ")
        self.data_tree.heading("percentage", text="النسبة %")
        
        # تحديد عرض الأعمدة
        self.data_tree.column("item", width=300, anchor="e")
        self.data_tree.column("quantity", width=100, anchor="center")
        self.data_tree.column("amount", width=150, anchor="center")
        self.data_tree.column("percentage", width=100, anchor="center")
        
        # شريط التمرير
        data_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.data_tree.yview)
        self.data_tree.configure(yscrollcommand=data_scrollbar.set)
        
        # تخطيط الجدول
        self.data_tree.pack(side="right", fill="both", expand=True)
        data_scrollbar.pack(side="left", fill="y")
    
    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        stats_container = ctk.CTkScrollableFrame(self.stats_frame)
        stats_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان الإحصائيات
        ctk.CTkLabel(
            stats_container,
            text="📈 إحصائيات المبيعات التفصيلية",
            font=(FONTS['arabic'], 18, "bold")
        ).pack(pady=(0, 20))
        
        # إطار الإحصائيات الرئيسية
        self.main_stats_frame = ctk.CTkFrame(stats_container)
        self.main_stats_frame.pack(fill="x", pady=10)
        
        # إطار الإحصائيات المتقدمة
        self.advanced_stats_frame = ctk.CTkFrame(stats_container)
        self.advanced_stats_frame.pack(fill="x", pady=10)
        
        # إطار المقارنات
        self.comparison_frame = ctk.CTkFrame(stats_container)
        self.comparison_frame.pack(fill="x", pady=10)
    
    def create_buttons(self):
        """إنشاء أزرار النافذة"""
        buttons_frame = ctk.CTkFrame(self.window, height=60, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=10, pady=(0, 10))
        buttons_frame.pack_propagate(False)
        
        # زر تصدير Excel
        export_excel_btn = ctk.CTkButton(
            buttons_frame,
            text="📊 تصدير Excel",
            command=self.export_to_excel,
            fg_color=MODERN_COLORS['success'],
            width=120
        )
        export_excel_btn.pack(side="right", padx=5, pady=15)
        
        # زر تصدير PDF
        export_pdf_btn = ctk.CTkButton(
            buttons_frame,
            text="📄 تصدير PDF",
            command=self.export_to_pdf,
            fg_color=MODERN_COLORS['warning'],
            width=120
        )
        export_pdf_btn.pack(side="right", padx=5, pady=15)
        
        # زر طباعة التقرير
        print_btn = ctk.CTkButton(
            buttons_frame,
            text="🖨️ طباعة التقرير",
            command=self.print_report,
            fg_color=MODERN_COLORS['info'],
            width=120
        )
        print_btn.pack(side="right", padx=5, pady=15)
        
        # زر إغلاق
        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            command=self.close_window,
            fg_color=MODERN_COLORS['error'],
            width=100
        )
        close_btn.pack(side="left", padx=5, pady=15)
        
        # زر مساعدة
        help_btn = ctk.CTkButton(
            buttons_frame,
            text="❓ مساعدة",
            command=self.show_help,
            fg_color=MODERN_COLORS['primary'],
            width=100
        )
        help_btn.pack(side="left", padx=5, pady=15)

    # دوال بسيطة للوظائف المطلوبة
    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        messagebox.showinfo("تصدير", "تم تصدير البيانات إلى Excel بنجاح!")

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        messagebox.showinfo("تصدير", "تم تصدير التقرير إلى PDF بنجاح!")

    def print_report(self):
        """طباعة التقرير"""
        messagebox.showinfo("طباعة", "تم إرسال التقرير للطباعة!")

    def show_help(self):
        """عرض المساعدة"""
        messagebox.showinfo("مساعدة", "نافذة تحليل المبيعات - يمكنك عرض الإحصائيات والرسوم البيانية هنا")

    def get_date_range(self):
        """الحصول على نطاق التاريخ حسب الفترة المحددة"""
        period = self.period_combo.get()
        today = date.today()

        if period == "اليوم":
            return today, today
        elif period == "هذا الأسبوع":
            start = today - timedelta(days=today.weekday())
            return start, today
        elif period == "هذا الشهر":
            start = today.replace(day=1)
            return start, today
        elif period == "هذا الربع":
            quarter = (today.month - 1) // 3 + 1
            start = date(today.year, (quarter - 1) * 3 + 1, 1)
            return start, today
        elif period == "هذا العام":
            start = date(today.year, 1, 1)
            return start, today
        elif period == "فترة مخصصة":
            try:
                start_str = self.start_date_entry.get()
                end_str = self.end_date_entry.get()
                if start_str and end_str:
                    start = datetime.strptime(start_str, "%Y-%m-%d").date()
                    end = datetime.strptime(end_str, "%Y-%m-%d").date()
                    return start, end
            except ValueError:
                pass
            except ValueError:
                messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")

        # افتراضي: هذا الشهر
        start = today.replace(day=1)
        return start, today

    def load_sales_data(self, start_date, end_date, analysis_type):
        """تحميل بيانات المبيعات من قاعدة البيانات"""
        try:
            if analysis_type == "حسب المنتج":
                query = """
                SELECT
                    COALESCE(p.name, 'منتج غير محدد') as item_name,
                    SUM(sii.quantity) as total_quantity,
                    SUM(sii.quantity * sii.unit_price) as total_amount
                FROM sales_invoices si
                JOIN sales_invoice_items sii ON si.id = sii.invoice_id
                LEFT JOIN products p ON sii.product_id = p.id
                WHERE DATE(si.invoice_date) BETWEEN ? AND ?
                GROUP BY sii.product_id, p.name
                ORDER BY total_amount DESC
                """
                params = (start_date, end_date)

            elif analysis_type == "حسب العميل":
                query = """
                SELECT
                    COALESCE(c.name, 'عميل غير محدد') as item_name,
                    COUNT(si.id) as total_quantity,
                    SUM(si.total_amount) as total_amount
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                WHERE DATE(si.invoice_date) BETWEEN ? AND ?
                GROUP BY si.customer_id, c.name
                ORDER BY total_amount DESC
                """
                params = (start_date, end_date)

            elif analysis_type == "حسب التاريخ":
                query = """
                SELECT
                    DATE(si.invoice_date) as item_name,
                    COUNT(si.id) as total_quantity,
                    SUM(si.total_amount) as total_amount
                FROM sales_invoices si
                WHERE DATE(si.invoice_date) BETWEEN ? AND ?
                GROUP BY DATE(si.invoice_date)
                ORDER BY DATE(si.invoice_date)
                """
                params = (start_date, end_date)

            elif analysis_type == "حسب المبيعات اليومية":
                query = """
                SELECT
                    strftime('%Y-%m-%d', si.invoice_date) as item_name,
                    COUNT(si.id) as total_quantity,
                    SUM(si.total_amount) as total_amount
                FROM sales_invoices si
                WHERE DATE(si.invoice_date) BETWEEN ? AND ?
                GROUP BY strftime('%Y-%m-%d', si.invoice_date)
                ORDER BY strftime('%Y-%m-%d', si.invoice_date)
                """
                params = (start_date, end_date)

            else:  # حسب الفئة (افتراضي):
                query = """
                SELECT
                    COALESCE(p.category, 'غير محدد') as item_name,
                    SUM(sii.quantity) as total_quantity,
                    SUM(sii.quantity * sii.unit_price) as total_amount
                FROM sales_invoices si
                JOIN sales_invoice_items sii ON si.id = sii.invoice_id
                LEFT JOIN products p ON sii.product_id = p.id
                WHERE DATE(si.invoice_date) BETWEEN ? AND ?
                GROUP BY p.category
                ORDER BY total_amount DESC
                """
                params = (start_date, end_date)

            results = self.db_manager.fetch_all(query, params)

            # تحويل النتائج إلى قائمة قواميس
            data = []
            total_amount = 0

            for row in results:
                item_data = {
                    'name': row[0] or 'غير محدد',
                    'quantity': row[1] or 0,
                    'amount': row[2] or 0
                }
                data.append(item_data)
                total_amount += item_data['amount']

            # حساب النسب المئوية
            for item in data:
                if total_amount > 0:
                    item['percentage'] = (item['amount'] / total_amount) * 100
                else:
                    item['percentage'] = 0

            return data, total_amount

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {e}")
            return [], 0

    def load_sales_statistics(self, start_date, end_date):
        """تحميل إحصائيات المبيعات المتقدمة"""
        try:
            # إحصائيات أساسية
            basic_stats_query = """
            SELECT
                COUNT(DISTINCT si.id) as total_invoices,
                COUNT(DISTINCT si.customer_id) as unique_customers,
                SUM(si.total_amount) as total_sales,
                AVG(si.total_amount) as avg_invoice_amount,
                MAX(si.total_amount) as max_invoice_amount,
                MIN(si.total_amount) as min_invoice_amount
            FROM sales_invoices si
            WHERE DATE(si.invoice_date) BETWEEN ? AND ?
            """

            basic_stats = self.db_manager.fetch_one(basic_stats_query, (start_date, end_date))

            # أعلى المنتجات مبيعاً
            top_products_query = """
            SELECT
                COALESCE(p.name, 'منتج غير محدد') as name,
                SUM(sii.quantity) as total_quantity,
                SUM(sii.quantity * sii.unit_price) as total_amount
            FROM sales_invoices si
            JOIN sales_invoice_items sii ON si.id = sii.invoice_id
            LEFT JOIN products p ON sii.product_id = p.id
            WHERE DATE(si.invoice_date) BETWEEN ? AND ?
            GROUP BY sii.product_id, p.name
            ORDER BY total_amount DESC
            LIMIT 5
            """

            top_products = self.db_manager.fetch_all(top_products_query, (start_date, end_date))

            # أعلى العملاء شراءً
            top_customers_query = """
            SELECT
                COALESCE(c.name, 'عميل غير محدد') as customer_name,
                COUNT(si.id) as invoice_count,
                SUM(si.total_amount) as total_amount
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            WHERE DATE(si.invoice_date) BETWEEN ? AND ?
            GROUP BY si.customer_id, c.name
            ORDER BY total_amount DESC
            LIMIT 5
            """

            top_customers = self.db_manager.fetch_all(top_customers_query, (start_date, end_date))

            # مبيعات يومية
            daily_sales_query = """
            SELECT
                DATE(si.invoice_date) as sale_date,
                COUNT(si.id) as invoice_count,
                SUM(si.total_amount) as daily_total
            FROM sales_invoices si
            WHERE DATE(si.invoice_date) BETWEEN ? AND ?
            GROUP BY DATE(si.invoice_date)
            ORDER BY DATE(si.invoice_date)
            """

            daily_sales = self.db_manager.fetch_all(daily_sales_query, (start_date, end_date))

            return {
                'basic': basic_stats,
                'top_products': top_products,
                'top_customers': top_customers,
                'daily_sales': daily_sales
            }

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الإحصائيات: {e}")
            return None

    def on_period_change(self, value=None):
        """تغيير الفترة الزمنية"""
        if value == "فترة مخصصة":
            # تفعيل حقول التاريخ المخصصة
            self.start_date_entry.configure(state="normal")
            self.end_date_entry.configure(state="normal")

            # تعيين قيم افتراضية
            today = date.today()
            start_of_month = today.replace(day=1)
            self.start_date_entry.delete(0, "end")
            self.start_date_entry.insert(0, start_of_month.strftime("%Y-%m-%d"))
            self.end_date_entry.delete(0, "end")
            self.end_date_entry.insert(0, today.strftime("%Y-%m-%d"))
        else:
            # تعطيل حقول التاريخ المخصصة
            self.start_date_entry.configure(state="disabled")
            self.end_date_entry.configure(state="disabled")

    def on_analysis_type_change(self, value=None):
        """تغيير نوع التحليل"""
        # يمكن إضافة منطق خاص لكل نوع تحليل
        pass

    def on_chart_type_change(self, value=None):
        """تغيير نوع الرسم البياني"""
        if self.current_data:
            self.update_chart()

    def perform_analysis(self):
        """تنفيذ التحليل وعرض النتائج"""
        try:
            # الحصول على نطاق التاريخ
            start_date, end_date = self.get_date_range()

            # تحديث تسمية الفترة
            period_text = f"الفترة: من {start_date} إلى {end_date}"
            self.period_label.configure(text=period_text)

            # تحميل البيانات
            analysis_type = self.analysis_type_combo.get()
            data, total_amount = self.load_sales_data(start_date, end_date, analysis_type)

            if not data:
                messagebox.showinfo("معلومات", "لا توجد بيانات مبيعات في الفترة المحددة")
                return

            # حفظ البيانات الحالية
            self.current_data = {
                'data': data,
                'total_amount': total_amount,
                'start_date': start_date,
                'end_date': end_date,
                'analysis_type': analysis_type
            }

            # تحديث تسمية إجمالي المبيعات
            self.total_sales_label.configure(text=f"إجمالي المبيعات: {total_amount:,.2f} ريال")

            # تحديث الرسم البياني
            self.update_chart()

            # تحديث جدول البيانات
            self.update_data_table()

            # تحديث الإحصائيات
            self.update_statistics()

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تنفيذ التحليل: {e}")

    def update_chart(self):
        """تحديث الرسم البياني"""
        if not self.current_data:
            return

        try:
            # مسح الرسم السابق
            self.chart_figure.clear()

            data = self.current_data['data']
            chart_type = self.chart_type_combo.get()

            # تحضير البيانات
            names = [item['name'][:20] + '...' if len(item['name']) > 20 else item['name'] for item in data[:10]]
            amounts = [item['amount'] for item in data[:10]]

            if not amounts:
                # رسم فارغ
                ax = self.chart_figure.add_subplot(111)
                ax.text(0.5, 0.5, 'لا توجد بيانات للعرض',
                        horizontalalignment='center', verticalalignment='center',
                        transform=ax.transAxes, fontsize=16)
                ax.set_title('تحليل المبيعات', fontsize=14, pad=20)
            else:
                if chart_type == "رسم عمودي":
                    self.create_bar_chart(names, amounts)
                elif chart_type == "رسم دائري":
                    self.create_pie_chart(names, amounts)
                elif chart_type == "رسم خطي":
                    self.create_line_chart(names, amounts)
                elif chart_type == "رسم مساحي":
                    self.create_area_chart(names, amounts)

            # تحديث الرسم
            self.chart_figure.tight_layout()
            self.chart_canvas.draw()

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث الرسم البياني: {e}")

    def create_bar_chart(self, names, amounts):
        """إنشاء رسم بياني عمودي"""
        ax = self.chart_figure.add_subplot(111)

        bars = ax.bar(range(len(names)), amounts, color='#1f538d', alpha=0.8)

        # تخصيص الرسم
        ax.set_xlabel('البنود', fontsize=12)
        ax.set_ylabel('المبلغ (ريال)', fontsize=12)
        ax.set_title(f'تحليل المبيعات - {self.current_data["analysis_type"]}', fontsize=14, pad=20)

        # تدوير أسماء المحاور
        ax.set_xticks(range(len(names)))
        ax.set_xticklabels(names, rotation=45, ha='right')

        # إضافة قيم على الأعمدة
        for bar, amount in zip(bars, amounts):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(amounts)*0.01,
                    f'{amount:,.0f}', ha='center', va='bottom', fontsize=10)

        # تنسيق المحاور
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        ax.grid(True, alpha=0.3)

    def create_pie_chart(self, names, amounts):
        """إنشاء رسم بياني دائري"""
        ax = self.chart_figure.add_subplot(111)

        # ألوان متدرجة
        colors = plt.cm.Set3(range(len(names)))

        # إنشاء الرسم الدائري
        wedges, texts, autotexts = ax.pie(amounts, labels=names, autopct='%1.1f%%',
                                        colors=colors, startangle=90)

        # تخصيص النصوص
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

        ax.set_title(f'توزيع المبيعات - {self.current_data["analysis_type"]}', fontsize=14, pad=20)

        # إضافة وسيلة إيضاح
        ax.legend(wedges, [f'{name}: {amount:,.0f} ريال' for name, amount in zip(names, amounts)],
                title="التفاصيل", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))

    def create_line_chart(self, names, amounts):
        """إنشاء رسم بياني خطي"""
        ax = self.chart_figure.add_subplot(111)

        ax.plot(range(len(names)), amounts, marker='o', linewidth=2, markersize=8, color='#1f538d')

        # تخصيص الرسم
        ax.set_xlabel('البنود', fontsize=12)
        ax.set_ylabel('المبلغ (ريال)', fontsize=12)
        ax.set_title(f'اتجاه المبيعات - {self.current_data["analysis_type"]}', fontsize=14, pad=20)

        # تدوير أسماء المحاور
        ax.set_xticks(range(len(names)))
        ax.set_xticklabels(names, rotation=45, ha='right')

        # تنسيق المحاور
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        ax.grid(True, alpha=0.3)

        # إضافة قيم على النقاط
        for i, amount in enumerate(amounts):
            ax.annotate(f'{amount:,.0f}', (i, amount), textcoords="offset points",
                        xytext=(0,10), ha='center', fontsize=9)

    def create_area_chart(self, names, amounts):
        """إنشاء رسم بياني مساحي"""
        ax = self.chart_figure.add_subplot(111)

        ax.fill_between(range(len(names)), amounts, alpha=0.6, color='#1f538d')
        ax.plot(range(len(names)), amounts, marker='o', linewidth=2, markersize=6, color='#0d2a4a')

        # تخصيص الرسم
        ax.set_xlabel('البنود', fontsize=12)
        ax.set_ylabel('المبلغ (ريال)', fontsize=12)
        ax.set_title(f'توزيع المبيعات المساحي - {self.current_data["analysis_type"]}', fontsize=14, pad=20)

        # تدوير أسماء المحاور
        ax.set_xticks(range(len(names)))
        ax.set_xticklabels(names, rotation=45, ha='right')

        # تنسيق المحاور
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        ax.grid(True, alpha=0.3)

    def update_data_table(self):
        """تحديث جدول البيانات التفصيلية"""
        if not self.current_data:
            return

        try:
            # مسح البيانات السابقة
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)

            # إدراج البيانات الجديدة
            data = self.current_data['data']
            for item in data:
                self.data_tree.insert("", "end", values=(
                    item['name'],
                    f"{item['quantity']:,.0f}",
                    f"{item['amount']:,.2f} ريال",
                    f"{item['percentage']:.1f}%"
                ))

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث جدول البيانات: {e}")

    def update_statistics(self):
        """تحديث الإحصائيات التفصيلية"""
        if not self.current_data:
            return

        try:
            start_date = self.current_data['start_date']
            end_date = self.current_data['end_date']

            # تحميل الإحصائيات
            stats = self.load_sales_statistics(start_date, end_date)
            if not stats:
                return

            # مسح الإحصائيات السابقة
            for widget in self.main_stats_frame.winfo_children():
                widget.destroy()
            for widget in self.advanced_stats_frame.winfo_children():
                widget.destroy()
            for widget in self.comparison_frame.winfo_children():
                widget.destroy()

            # الإحصائيات الرئيسية
            ctk.CTkLabel(
                self.main_stats_frame,
                text="الإحصائيات الرئيسية",
                font=(FONTS['arabic'], 14, "bold")
            ).pack(pady=(10, 5))

            basic = stats['basic']
            main_stats_text = f"""
        except Exception as e:
            print(f"خطأ: {e}")
إجمالي الفواتير: {basic[0] or 0:,}
عدد العملاء الفريدين: {basic[1] or 0:,}
إجمالي المبيعات: {basic[2] or 0:,.2f} ريال
متوسط قيمة الفاتورة: {basic[3] or 0:,.2f} ريال
أعلى فاتورة: {basic[4] or 0:,.2f} ريال
أقل فاتورة: {basic[5] or 0:,.2f} ريال
            """

            ctk.CTkLabel(
                self.main_stats_frame,
                text=main_stats_text,
                font=(FONTS['arabic'], 12),
                justify="right"
            ).pack(pady=10)

            # أعلى المنتجات
            ctk.CTkLabel(
                self.advanced_stats_frame,
                text="أعلى 5 منتجات مبيعاً",
                font=(FONTS['arabic'], 14, "bold")
            ).pack(pady=(10, 5))

            if stats['top_products']:
                products_text = ""
                for i, product in enumerate(stats['top_products'], 1):
                    products_text += f"{i}. {product[0] or 'غير محدد'}: {product[2] or 0:,.2f} ريال\n"

                ctk.CTkLabel(
                    self.advanced_stats_frame,
                    text=products_text,
                    font=(FONTS['arabic'], 11),
                    justify="right"
                ).pack(pady=5)

            # أعلى العملاء
            ctk.CTkLabel(
                self.comparison_frame,
                text="أعلى 5 عملاء شراءً",
                font=(FONTS['arabic'], 14, "bold")
            ).pack(pady=(10, 5))

            if stats['top_customers']:
                customers_text = ""
                for i, customer in enumerate(stats['top_customers'], 1):
                    customers_text += f"{i}. {customer[0] or 'غير محدد'}: {customer[2] or 0:,.2f} ريال ({customer[1] or 0} فاتورة)\n"

                ctk.CTkLabel(
                    self.comparison_frame,
                    text=customers_text,
                    font=(FONTS['arabic'], 11),
                    justify="right"
                ).pack(pady=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث الإحصائيات: {e}")



    def refresh_data(self):
        """تحديث البيانات"""
        self.perform_analysis()

    def save_chart(self):
        """حفظ الرسم البياني كصورة"""
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ الرسم البياني"
            )

            if filename:
                self.chart_figure.savefig(filename, dpi=300, bbox_inches='tight')
                messagebox.showinfo("نجح", f"تم حفظ الرسم البياني في: {filename}")

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الرسم البياني: {e}")

    def print_chart(self):
        """طباعة الرسم البياني"""
        messagebox.showinfo("معلومات", "ميزة الطباعة ستكون متاحة قريباً")

    def zoom_chart(self):
        """تكبير الرسم البياني"""
        messagebox.showinfo("معلومات", "ميزة التكبير ستكون متاحة قريباً")

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        if not self.current_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return

        try:
            import csv

            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="تصدير إلى Excel"
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة الرأس
                    writer.writerow(['البند', 'الكمية', 'المبلغ', 'النسبة %'])

                    # كتابة البيانات
                    for item in self.current_data['data']:
                        writer.writerow([
                            item['name'],
                            item['quantity'],
                            item['amount'],
                            f"{item['percentage']:.1f}%"
                        ])

                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى: {filename}")

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير البيانات: {e}")

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        if not self.current_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return

        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            from reportlab.lib.units import inch
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
        except Exception as e:
            print(f"خطأ: {e}")
from ui.window_utils import configure_window_fullscreen
from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="تصدير إلى PDF"
            )

            if filename:
                # إنشاء ملف PDF
                c = canvas.Canvas(filename, pagesize=A4)
                width, height = A4

                # العنوان
                c.setFont("Helvetica-Bold", 16)
                c.drawString(100, height - 100, "Sales Analysis Report")

                # البيانات
                y_position = height - 150
                c.setFont("Helvetica", 12)

                for item in self.current_data['data'][:20]:  # أول 20 عنصر:
                    text = f"{item['name']}: {item['amount']:,.2f} SAR ({item['percentage']:.1f}%)"
                    c.drawString(100, y_position, text)
                    y_position -= 20

                    if y_position < 100:
                        break

                c.save()
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى: {filename}")

        except ImportError:
            messagebox.showerror("خطأ", "مكتبة reportlab غير مثبتة. يرجى تثبيتها أولاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير PDF: {e}")

    def print_report(self):
        """طباعة التقرير"""
        messagebox.showinfo("معلومات", "ميزة طباعة التقرير ستكون متاحة قريباً")

    def show_help(self):
        """عرض المساعدة"""
        messagebox.showinfo("مساعدة", "نافذة تحليل المبيعات - يمكنك عرض الإحصائيات والرسوم البيانية هنا")
