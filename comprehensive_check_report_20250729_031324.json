{"timestamp": "2025-07-29T03:13:23.291308", "syntax_errors": [{"file": "deep_comprehensive_system_audit.py", "line": 673, "column": 0, "error": "expected 'except' or 'finally' block (<unknown>, line 673)", "severity": "high"}, {"file": "final_cleanup_tool.py", "line": 219, "column": 12, "error": "unexpected indent (<unknown>, line 219)", "severity": "high"}, {"file": "quick_pattern_fixer.py", "line": 35, "column": 54, "error": "invalid syntax (<unknown>, line 35)", "severity": "high"}, {"file": "run_control_panel_safe.py", "line": 112, "column": 0, "error": "expected 'except' or 'finally' block (<unknown>, line 112)", "severity": "high"}, {"file": "run_control_panel_simple.py", "line": 58, "column": 8, "error": "unexpected indent (<unknown>, line 58)", "severity": "high"}, {"file": "run_fixed_app.py", "line": 155, "column": 8, "error": "unexpected indent (<unknown>, line 155)", "severity": "high"}, {"file": "safe_main.py", "line": 106, "column": 0, "error": "expected 'except' or 'finally' block (<unknown>, line 106)", "severity": "high"}, {"file": "start_app.py", "line": 40, "column": 0, "error": "expected 'except' or 'finally' block (<unknown>, line 40)", "severity": "high"}, {"file": "start_with_scheduler.py", "line": 81, "column": 8, "error": "unexpected indent (<unknown>, line 81)", "severity": "high"}, {"file": "ultimate_system_fixer.py", "line": 302, "column": 44, "error": "unterminated string literal (detected at line 302) (<unknown>, line 302)", "severity": "high"}, {"file": "تشغيل_البرنامج.py", "line": 87, "column": 8, "error": "unexpected indent (<unknown>, line 87)", "severity": "high"}, {"file": "config\\postgresql_config.py", "line": 151, "column": 12, "error": "unexpected indent (<unknown>, line 151)", "severity": "high"}, {"file": "database\\comprehensive_income_manager.py", "line": 194, "column": 79, "error": "invalid syntax (<unknown>, line 194)", "severity": "high"}, {"file": "database\\fix_database.py", "line": 69, "column": 56, "error": "unterminated string literal (detected at line 69) (<unknown>, line 69)", "severity": "high"}, {"file": "ui\\daily_journal_window.py", "line": 686, "column": 9, "error": "invalid syntax (<unknown>, line 686)", "severity": "high"}, {"file": "ui\\enhanced_pos_window.py", "line": 18, "column": 1, "error": "expected 'except' or 'finally' block (<unknown>, line 18)", "severity": "high"}, {"file": "ui\\sales_analysis_window.py", "line": 1340, "column": 12, "error": "unexpected indent (<unknown>, line 1340)", "severity": "high"}, {"file": "ui\\welcome_window.py", "line": 18, "column": 1, "error": "expected 'except' or 'finally' block (<unknown>, line 18)", "severity": "high"}], "import_errors": [], "missing_files": [], "pep8_violations": [], "logical_errors": [], "integration_issues": [], "recommendations": ["إصلاح الأخطاء النحوية أولاً"]}