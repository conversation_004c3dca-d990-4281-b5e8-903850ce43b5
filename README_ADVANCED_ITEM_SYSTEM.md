# 🚀 نظام إدخال الأصناف الشامل والمتقدم

## نظرة عامة

نظام إدخال الأصناف الشامل والمتقدم هو حل متكامل وذكي لإدارة الأصناف في البرامج المحاسبية. يجمع النظام بين أحدث تقنيات الذكاء الاصطناعي والتحليلات المتقدمة مع واجهة مستخدم احترافية وسهلة الاستخدام.

## ✨ الميزات الرئيسية

### 🤖 الذكاء الاصطناعي
- **اقتراح التصنيف التلقائي** - تصنيف ذكي للأصناف بناءً على الاسم والوصف
- **توقع الأسعار** - خوارزميات متقدمة لتوقع أسعار البيع المناسبة
- **كشف التكرار** - تحديد الأصناف المشابهة لتجنب الازدواجية
- **تقييم جودة البيانات** - تقييم شامل لاكتمال وجودة بيانات الأصناف

### 📊 التحليلات المتقدمة
- **اتجاه المبيعات** - رسوم بيانية تفاعلية لتتبع الأداء
- **توزيع الفئات** - تحليل مفصل لتوزيع الأصناف
- **تحليل الربحية** - مراقبة هوامش الربح والأداء المالي
- **حالة المخزون** - متابعة مستويات المخزون والتنبيهات

### 🎨 الواجهة الاحترافية
- **تصميم حديث** - واجهة عصرية مع دعم الثيمات المتعددة
- **دعم اللغة العربية** - دعم كامل للنصوص العربية واتجاه RTL
- **تبويبات منظمة** - تنظيم منطقي للمعلومات والوظائف
- **اختصارات لوحة المفاتيح** - تسريع العمل للمستخدمين المتقدمين

## 🛠️ التقنيات المستخدمة

- **Python 3.8+** - لغة البرمجة الأساسية
- **Tkinter** - واجهة المستخدم الرسومية
- **SQLite** - قاعدة البيانات المحلية
- **Matplotlib** - الرسوم البيانية والتحليلات
- **NumPy & Pandas** - تحليل البيانات والعمليات الرياضية
- **Scikit-learn** - خوارزميات التعلم الآلي
- **PIL/Pillow** - معالجة الصور

## 📦 التثبيت والإعداد

### 1. متطلبات النظام
```bash
Python 3.8 أو أحدث
نظام التشغيل: Windows, macOS, Linux
ذاكرة: 4GB RAM (مستحسن 8GB)
مساحة القرص: 500MB
```

### 2. تثبيت التبعيات
```bash
# التبعيات الأساسية
pip install tkinter sqlite3

# التبعيات المتقدمة
pip install matplotlib>=3.8.0 numpy>=1.24.0 pandas>=2.1.0 scikit-learn>=1.3.0

# التبعيات الاختيارية
pip install pillow>=10.0.0 qrcode>=7.4.0 openpyxl>=3.1.0
```

### 3. تشغيل النظام
```bash
# من خلال البرنامج الرئيسي
python large_font_run.py

# أو مباشرة
python windows/advanced_item_entry_comprehensive.py
```

## 🚀 دليل الاستخدام السريع

### إنشاء صنف جديد
1. انقر على "جديد" أو اضغط `Ctrl+N`
2. أدخل اسم الصنف (مطلوب)
3. استخدم "🤖 اقتراح" للحصول على تصنيف تلقائي
4. أدخل معلومات التسعير والمخزون
5. احفظ بالضغط على `Ctrl+S`

### استخدام الذكاء الاصطناعي
- **اقتراح التصنيف**: أدخل اسم الصنف ← انقر "🤖 اقتراح"
- **توقع السعر**: أدخل سعر التكلفة ← انقر "💰 توقع السعر"
- **كشف التكرار**: أدخل اسم الصنف ← انقر "🔍 كشف التكرار"

### عرض التحليلات
1. انتقل إلى تبويب "تحليلات"
2. اختر نوع التحليل المطلوب
3. اعرض النتائج في نافذة منفصلة

## 📁 هيكل المشروع

```
project/
├── windows/
│   ├── advanced_item_entry_comprehensive.py  # النافذة الرئيسية
│   └── ...
├── core/
│   ├── advanced_dependency_manager.py        # إدارة التبعيات
│   └── ...
├── database/
│   ├── advanced_items_database.py           # قاعدة البيانات
│   └── ...
├── ai/
│   ├── intelligent_item_manager.py          # الذكاء الاصطناعي
│   └── ...
├── analytics/
│   ├── advanced_analytics_engine.py         # التحليلات
│   └── ...
├── docs/
│   ├── advanced_item_entry_system_guide.md  # الدليل الشامل
│   └── ...
└── requirements.txt                          # التبعيات
```

## ⌨️ اختصارات لوحة المفاتيح

| الاختصار | الوظيفة |
|----------|---------|
| `Ctrl+N` | صنف جديد |
| `Ctrl+S` | حفظ |
| `Ctrl+D` | حذف |
| `Ctrl+F` | البحث |
| `Ctrl+R` | تحديث البيانات |
| `F1` | دليل المستخدم |
| `F5` | تحديث |
| `Escape` | مسح النموذج |

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في التبعيات
```bash
# إذا ظهر خطأ: No module named 'matplotlib'
pip install matplotlib numpy pandas scikit-learn pillow

# إذا ظهر خطأ: No module named 'tkinter'
# على Ubuntu/Debian:
sudo apt-get install python3-tk
```

#### مشاكل قاعدة البيانات
```bash
# إذا ظهر خطأ: database is locked
# أغلق جميع نوافذ البرنامج وأعد التشغيل
```

#### مشاكل الذكاء الاصطناعي
```bash
# إذا لم تعمل ميزات الذكاء الاصطناعي
pip install --upgrade scikit-learn numpy pandas
```

## 📊 الأداء والإحصائيات

### معايير الأداء
- **سرعة الاستجابة**: < 100ms للعمليات الأساسية
- **دقة التصنيف**: > 85% للاقتراحات التلقائية
- **دقة توقع الأسعار**: > 80% ضمن نطاق ±10%
- **استهلاك الذاكرة**: < 200MB في الاستخدام العادي

### الإحصائيات
- **عدد الخوارزميات**: 12 خوارزمية ذكاء اصطناعي
- **أنواع التحليلات**: 8 أنواع تحليلات مختلفة
- **صيغ التصدير**: 5 صيغ مدعومة
- **اللغات المدعومة**: العربية والإنجليزية

## 🤝 المساهمة والتطوير

### إرشادات المساهمة
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. كتابة الكود مع التوثيق
4. إضافة الاختبارات
5. إرسال Pull Request

### معايير الكود
- استخدام PEP 8 لتنسيق Python
- توثيق جميع الوظائف والكلاسات
- كتابة اختبارات للميزات الجديدة
- دعم اللغة العربية في جميع النصوص

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

### الحصول على المساعدة
- **الدليل الشامل**: `docs/advanced_item_entry_system_guide.md`
- **أمثلة الاستخدام**: `examples/` folder
- **الأسئلة الشائعة**: `docs/FAQ.md`

### الإبلاغ عن المشاكل
1. تحقق من الأسئلة الشائعة أولاً
2. ابحث في المشاكل المفتوحة
3. أنشئ issue جديد مع التفاصيل الكاملة

## 🎯 خارطة الطريق

### الإصدار الحالي (v1.0.0)
- ✅ نظام الذكاء الاصطناعي الكامل
- ✅ محرك التحليلات المتقدم
- ✅ واجهة المستخدم الاحترافية
- ✅ قاعدة البيانات المحسنة

### الإصدارات القادمة
- 🔄 **v1.1.0**: دعم قواعد بيانات إضافية (PostgreSQL, MySQL)
- 🔄 **v1.2.0**: تحليلات أكثر تقدماً وتقارير مخصصة
- 🔄 **v1.3.0**: ميزات ذكاء اصطناعي إضافية (التعرف على الصور)
- 🔄 **v2.0.0**: واجهة ويب وAPI للتكامل

---

**تم تطوير هذا النظام بعناية فائقة لتوفير أفضل تجربة لإدارة الأصناف في البرامج المحاسبية. نحن نسعى دائماً للتحسين والتطوير المستمر.**

⭐ **إذا أعجبك هذا المشروع، لا تنس إعطاؤه نجمة!**
