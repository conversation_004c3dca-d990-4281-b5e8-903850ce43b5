# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة إدارة الحسابات المحسنة
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime
from themes.modern_theme import MODERN_COLORS, FONTS
from database.accounts_manager import AccountsManager
from database.database_manager import DatabaseManager
from ui.window_utils import configure_window_fullscreen

class AccountsWindow:
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.db_manager = DatabaseManager()
        self.accounts_manager = AccountsManager(self.db_manager)
        self.create_window()
        
    def create_window(self):
        """إنشاء نافذة إدارة الحسابات"""
        self.window = ctk.CTkToplevel(self.parent)
        
        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "إدارة الحسابات - برنامج ست الكل للمحاسبة")
        self.window.configure(fg_color=MODERN_COLORS['background'])
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إنشاء المحتوى
        self.create_header()
        self.create_main_content()
        
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = ctk.CTkFrame(self.window, height=80, fg_color=MODERN_COLORS['secondary'])
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)
        
        # عنوان النافذة
        title_label = ctk.CTkLabel(
            header_frame,
            text="💼 إدارة الحسابات ودليل الحسابات",
            font=(FONTS['arabic'], 24, "bold"),
            text_color="white"
        )
        title_label.pack(side="right", padx=20, pady=20)
        
        # تاريخ اليوم
        date_label = ctk.CTkLabel(
            header_frame,
            text=f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}",
            font=(FONTS['arabic'], 14),
            text_color="white"
        )
        date_label.pack(side="left", padx=20, pady=20)
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        main_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إطار إضافة حساب جديد
        self.create_add_account_section(main_frame)
        
        # إطار دليل الحسابات
        self.create_accounts_list(main_frame)
        
        # إطار الأزرار
        self.create_buttons(main_frame)
        
    def create_add_account_section(self, parent):
        """إنشاء قسم إضافة حساب جديد"""
        add_frame = ctk.CTkFrame(parent, height=180)
        add_frame.pack(fill="x", pady=(0, 10))
        add_frame.pack_propagate(False)
        
        # العنوان
        title = ctk.CTkLabel(
            add_frame,
            text="إضافة حساب جديد",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=(10, 5))
        
        # إطار الحقول
        fields_frame = ctk.CTkFrame(add_frame, fg_color="transparent")
        fields_frame.pack(fill="x", padx=20, pady=5)
        
        # الصف الأول
        row1 = ctk.CTkFrame(fields_frame, fg_color="transparent")
        row1.pack(fill="x", pady=5)
        
        # رقم الحساب
        ctk.CTkLabel(row1, text="رقم الحساب:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10)
        self.account_number = ctk.CTkEntry(row1, width=120, placeholder_text="رقم الحساب")
        self.account_number.pack(side="right", padx=10)
        
        # اسم الحساب
        ctk.CTkLabel(row1, text="اسم الحساب:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10)
        self.account_name = ctk.CTkEntry(row1, width=200, placeholder_text="اسم الحساب")
        self.account_name.pack(side="right", padx=10)
        
        # الصف الثاني
        row2 = ctk.CTkFrame(fields_frame, fg_color="transparent")
        row2.pack(fill="x", pady=5)
        
        # نوع الحساب
        ctk.CTkLabel(row2, text="نوع الحساب:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10)
        self.account_type = ctk.CTkComboBox(
            row2,
            values=["أصول", "خصوم", "حقوق ملكية", "إيرادات", "مصروفات"],
            width=150
        )
        self.account_type.pack(side="right", padx=10)
        self.account_type.set("أصول")
        
        # الحساب الرئيسي
        ctk.CTkLabel(row2, text="الحساب الرئيسي:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10)
        self.parent_account = ctk.CTkComboBox(
            row2,
            values=["لا يوجد", "الأصول المتداولة", "الأصول الثابتة", "الخصوم المتداولة"],
            width=150
        )
        self.parent_account.pack(side="right", padx=10)
        self.parent_account.set("لا يوجد")
        
        # الصف الثالث
        row3 = ctk.CTkFrame(fields_frame, fg_color="transparent")
        row3.pack(fill="x", pady=5)
        
        # الرصيد الافتتاحي
        ctk.CTkLabel(row3, text="الرصيد الافتتاحي:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10)
        self.opening_balance = ctk.CTkEntry(row3, width=120, placeholder_text="0.00")
        self.opening_balance.pack(side="right", padx=10)
        
        # طبيعة الرصيد
        ctk.CTkLabel(row3, text="طبيعة الرصيد:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10)
        self.balance_nature = ctk.CTkComboBox(
            row3,
            values=["مدين", "دائن"],
            width=100
        )
        self.balance_nature.pack(side="right", padx=10)
        self.balance_nature.set("مدين")
        
        # زر الإضافة
        add_btn = ctk.CTkButton(
            row3,
            text="إضافة الحساب",
            width=120,
            command=self.add_account,
            fg_color=MODERN_COLORS['success']
        )
        add_btn.pack(side="right", padx=20)
        
    def create_accounts_list(self, parent):
        """إنشاء قائمة الحسابات"""
        list_frame = ctk.CTkFrame(parent)
        list_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # العنوان
        title = ctk.CTkLabel(
            list_frame,
            text="دليل الحسابات",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=(10, 5))
        
        # إطار البحث
        search_frame = ctk.CTkFrame(list_frame, height=50)
        search_frame.pack(fill="x", padx=20, pady=5)
        search_frame.pack_propagate(False)
        
        ctk.CTkLabel(search_frame, text="البحث:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10, pady=15)
        self.search_entry = ctk.CTkEntry(search_frame, width=200, placeholder_text="ابحث عن حساب...")
        self.search_entry.pack(side="right", padx=5, pady=15)
        
        search_btn = ctk.CTkButton(
            search_frame,
            text="بحث",
            width=80,
            command=self.search_accounts,
            fg_color=MODERN_COLORS['info']
        )
        search_btn.pack(side="right", padx=10, pady=15)
        
        # جدول الحسابات
        table_frame = ctk.CTkFrame(list_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء Treeview
        columns = ("number", "name", "type", "parent", "balance", "nature")
        self.accounts_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف الأعمدة
        self.accounts_tree.heading("number", text="رقم الحساب")
        self.accounts_tree.heading("name", text="اسم الحساب")
        self.accounts_tree.heading("type", text="النوع")
        self.accounts_tree.heading("parent", text="الحساب الرئيسي")
        self.accounts_tree.heading("balance", text="الرصيد")
        self.accounts_tree.heading("nature", text="الطبيعة")
        
        # تحديد عرض الأعمدة
        self.accounts_tree.column("number", width=100)
        self.accounts_tree.column("name", width=200)
        self.accounts_tree.column("type", width=100)
        self.accounts_tree.column("parent", width=150)
        self.accounts_tree.column("balance", width=100)
        self.accounts_tree.column("nature", width=80)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.accounts_tree.pack(side="right", fill="both", expand=True)
        scrollbar.pack(side="left", fill="y")
        
        # تحميل البيانات الحقيقية
        self.load_real_accounts()
        
    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ctk.CTkFrame(parent, height=60, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=(0, 10))
        buttons_frame.pack_propagate(False)
        
        # زر التعديل
        edit_btn = ctk.CTkButton(
            buttons_frame,
            text="✏️ تعديل",
            width=100,
            command=self.edit_account,
            fg_color=MODERN_COLORS['warning']
        )
        edit_btn.pack(side="right", padx=10, pady=15)
        
        # زر الحذف
        delete_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف",
            width=100,
            command=self.delete_account,
            fg_color=MODERN_COLORS['error']
        )
        delete_btn.pack(side="right", padx=10, pady=15)
        
        # زر طباعة دليل الحسابات
        print_btn = ctk.CTkButton(
            buttons_frame,
            text="🖨️ طباعة الدليل",
            width=120,
            command=self.print_accounts,
            fg_color=MODERN_COLORS['info']
        )
        print_btn.pack(side="right", padx=10, pady=15)
        
        # زر الإغلاق
        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            width=100,
            command=self.close_window,
            fg_color=MODERN_COLORS['error']
        )
        close_btn.pack(side="left", padx=10, pady=15)

    def load_real_accounts(self):
        """تحميل الحسابات الحقيقية من قاعدة البيانات"""
        try:
            # مسح البيانات السابقة
            for item in self.accounts_tree.get_children():
                self.accounts_tree.delete(item)

            # جلب جميع الحسابات
            accounts = self.accounts_manager.get_all_accounts()

            for account in accounts:
                # تحديد نوع الرصيد
                balance_type = "مدين" if account['current_balance'] >= 0 else "دائن"
                balance_amount = abs(account['current_balance'])

                # البحث عن اسم الحساب الأب
                parent_name = "لا يوجد"
                if account['parent_id']:
                    parent_account = self.accounts_manager.get_account_by_id(account['parent_id'])
                    if parent_account:
                        parent_name = parent_account['name']

                self.accounts_tree.insert("", "end", values=(
                    account['code'],
                    account['name'],
                    account['account_type'],
                    parent_name,
                    f"{balance_amount:.2f}",
                    balance_type
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الحسابات: {e}")
            # تحميل بيانات تجريبية في حالة الخطأ
            self.load_sample_accounts()

    def load_sample_accounts(self):
        """تحميل بيانات تجريبية للحسابات"""
        sample_accounts = [
            ("1000", "الأصول المتداولة", "أصول", "لا يوجد", "0.00", "مدين"),
            ("1100", "النقدية", "أصول", "الأصول المتداولة", "50000.00", "مدين"),
            ("1110", "الصندوق", "أصول", "النقدية", "10000.00", "مدين"),
            ("1120", "البنك", "أصول", "النقدية", "40000.00", "مدين"),
            ("1200", "المدينون", "أصول", "الأصول المتداولة", "25000.00", "مدين"),
            ("1300", "المخزون", "أصول", "الأصول المتداولة", "75000.00", "مدين"),
            ("2000", "الخصوم المتداولة", "خصوم", "لا يوجد", "0.00", "دائن"),
            ("2100", "الدائنون", "خصوم", "الخصوم المتداولة", "15000.00", "دائن"),
            ("2200", "أوراق الدفع", "خصوم", "الخصوم المتداولة", "8000.00", "دائن"),
            ("3000", "حقوق الملكية", "حقوق ملكية", "لا يوجد", "127000.00", "دائن"),
            ("4000", "الإيرادات", "إيرادات", "لا يوجد", "0.00", "دائن"),
            ("4100", "مبيعات", "إيرادات", "الإيرادات", "0.00", "دائن"),
            ("5000", "المصروفات", "مصروفات", "لا يوجد", "0.00", "مدين"),
            ("5100", "مصروفات إدارية", "مصروفات", "المصروفات", "0.00", "مدين"),
            ("5200", "مصروفات بيع وتسويق", "مصروفات", "المصروفات", "0.00", "مدين")
        ]
        
        for account in sample_accounts:
            self.accounts_tree.insert("", "end", values=account)
            
    def add_account(self):
        """إضافة حساب جديد"""
        try:
            number = self.account_number.get().strip()
            name = self.account_name.get().strip()
            acc_type = self.account_type.get()
            parent = self.parent_account.get()
            balance = self.opening_balance.get().strip() or "0.00"
            nature = self.balance_nature.get()
            
            if not number or not name:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الحساب واسم الحساب")
                return
                
            # التحقق من عدم تكرار رقم الحساب
            for item in self.accounts_tree.get_children():
                values = self.accounts_tree.item(item)['values']
                if values[0] == number:
                    messagebox.showerror("خطأ", "رقم الحساب موجود مسبقاً")
                    return
                    
            # إضافة الحساب إلى الجدول
            self.accounts_tree.insert("", "end", values=(number, name, acc_type, parent, balance, nature))
            
            # مسح الحقول
            self.account_number.delete(0, "end")
            self.account_name.delete(0, "end")
            self.opening_balance.delete(0, "end")
            
            messagebox.showinfo("نجح", "تم إضافة الحساب بنجاح!")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إضافة الحساب: {str(e)}")
            
    def search_accounts(self):
        """البحث في الحسابات"""
        search_term = self.search_entry.get().strip().lower()
        
        if not search_term:
            messagebox.showinfo("تنبيه", "يرجى إدخال كلمة البحث")
            return
            
        # مسح التحديد السابق
        for item in self.accounts_tree.get_children():
            self.accounts_tree.selection_remove(item)
            
        # البحث وتحديد النتائج
        found = False
        for item in self.accounts_tree.get_children():
            values = self.accounts_tree.item(item)['values']
            if (search_term in values[0].lower() or 
                search_term in values[1].lower()):
                self.accounts_tree.selection_add(item)
                self.accounts_tree.focus(item)
                self.accounts_tree.see(item)
                found = True
                
        if not found:
            messagebox.showinfo("البحث", "لم يتم العثور على نتائج")
            
    def edit_account(self):
        """تعديل الحساب المحدد"""
        selected = self.accounts_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار حساب للتعديل")
            return
            
        messagebox.showinfo("تعديل", "سيتم فتح نافذة التعديل")
        
    def delete_account(self):
        """حذف الحساب المحدد"""
        selected = self.accounts_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار حساب للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الحساب؟"):
            self.accounts_tree.delete(selected)
            messagebox.showinfo("نجح", "تم حذف الحساب بنجاح!")
            
    def print_accounts(self):
        """طباعة دليل الحسابات"""
        messagebox.showinfo("طباعة", "سيتم طباعة دليل الحسابات")
        
    def close_window(self):
        """إغلاق النافذة"""
        self.if window and hasattr(window, "destroy"):
    window.destroy()
