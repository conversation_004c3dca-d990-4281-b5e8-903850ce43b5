# -*- coding: utf-8 -*-
"""
لوحة التحكم الشاملة - وحدة الإدارة الاحترافية
Admin Panel - Comprehensive Management Module
"""

import os
import json
import sqlite3
import shutil
import tkinter as tk
from tkinter import messagebox, ttk, filedialog, colorchooser
from datetime import datetime, timedelta
import customtkinter as ctk
from pathlib import Path
import pandas as pd
import hashlib
import threading
import time

from themes.modern_theme import MODERN_COLORS, FONTS, DIMENSIONS
from ui.window_utils import configure_window_fullscreen
from config.settings import PROJECT_ROOT


class ComprehensiveAdminPanel:
    """لوحة التحكم الشاملة - وحدة الإدارة الاحترافية"""

    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
        self.current_user = getattr(parent, 'current_user', {'role': 'admin', 'username': 'admin'})

        # متغيرات الإعدادات والبيانات
        self.settings = self.load_settings()
        self.settings_vars = {}
        # سيتم تطوير مديري النظام لاحقاً
        # self.backup_manager = BackupManager()
        # self.data_manager = DataManager()
        # self.user_manager = UserManager()

        # متغيرات الواجهة
        self.tab_view = None
        self.current_tab = 0
        self.toast_queue = []

        # نظام الألوان الاحترافي المتقدم
        self.colors = {
            # الألوان الأساسية مع تدرجات
            'primary': '#1A237E',           # بنفسجي داكن احترافي
            'primary_light': '#3F51B5',     # بنفسجي فاتح
            'primary_dark': '#0D1B69',      # بنفسجي أغمق
            'primary_hover': '#283593',     # بنفسجي تمرير

            'secondary': '#00695C',         # تيل داكن
            'secondary_light': '#26A69A',   # تيل فاتح
            'secondary_dark': '#004D40',    # تيل أغمق
            'secondary_hover': '#00796B',   # تيل تمرير

            # ألوان الحالة
            'success': '#2E7D32',           # أخضر نجاح
            'success_light': '#4CAF50',     # أخضر فاتح
            'success_dark': '#1B5E20',      # أخضر داكن
            'success_hover': '#388E3C',     # أخضر تمرير

            'warning': '#F57C00',           # برتقالي تحذير
            'warning_light': '#FF9800',     # برتقالي فاتح
            'warning_dark': '#E65100',      # برتقالي داكن
            'warning_hover': '#FB8C00',     # برتقالي تمرير

            'error': '#C62828',             # أحمر خطأ
            'error_light': '#F44336',       # أحمر فاتح
            'error_dark': '#B71C1C',        # أحمر داكن
            'error_hover': '#D32F2F',       # أحمر تمرير

            'info': '#1565C0',              # أزرق معلومات
            'info_light': '#2196F3',        # أزرق فاتح
            'info_dark': '#0D47A1',         # أزرق داكن
            'info_hover': '#1976D2',        # أزرق تمرير

            # ألوان السطح والخلفية
            'surface': '#FFFFFF',           # سطح أبيض
            'surface_variant': '#F8F9FA',   # سطح متغير
            'surface_dark': '#F5F5F5',      # سطح داكن
            'surface_elevated': '#FEFEFE',  # سطح مرتفع

            'background': '#F0F2F5',        # خلفية رئيسية
            'background_dark': '#E8EAED',   # خلفية داكنة
            'background_light': '#FAFBFC',  # خلفية فاتحة

            # ألوان النص
            'text_primary': '#1A1A1A',      # نص أساسي
            'text_secondary': '#5F6368',    # نص ثانوي
            'text_tertiary': '#80868B',     # نص ثالثي
            'text_disabled': '#BDC1C6',     # نص معطل
            'text_on_primary': '#FFFFFF',   # نص على الأساسي
            'text_on_surface': '#1A1A1A',   # نص على السطح

            # ألوان الحدود والفواصل
            'border': '#E0E0E0',            # حدود
            'border_light': '#F0F0F0',      # حدود فاتحة
            'border_dark': '#CCCCCC',       # حدود داكنة
            'divider': '#E8EAED',           # فاصل

            # ألوان التفاعل
            'hover': 'rgba(26,35,126,0.08)',       # تمرير
            'pressed': 'rgba(26,35,126,0.12)',     # ضغط
            'focus': 'rgba(26,35,126,0.16)',       # تركيز
            'selected': 'rgba(26,35,126,0.12)',    # محدد

            # ألوان خاصة
            'accent': '#FF6B35',            # لون مميز
            'accent_light': '#FF8A65',      # لون مميز فاتح
            'accent_dark': '#E64A19',       # لون مميز داكن

            'neutral': '#9E9E9E',           # محايد
            'neutral_light': '#BDBDBD',     # محايد فاتح
            'neutral_dark': '#757575'       # محايد داكن
        }

        # إعدادات الخطوط المتقدمة
        self.fonts = {
            'title': ("Cairo", 24, "bold"),
            'subtitle': ("Cairo", 18, "bold"),
            'heading': ("Cairo", 16, "bold"),
            'body': ("Cairo", 14, "normal"),
            'caption': ("Cairo", 12, "normal"),
            'button': ("Cairo", 14, "bold"),
            'tab': ("Cairo", 13, "bold")
        }

        # إعدادات الأبعاد والمسافات
        self.dimensions = {
            'padding_small': 8,
            'padding_medium': 16,
            'padding_large': 24,
            'margin_small': 4,
            'margin_medium': 8,
            'margin_large': 16,
            'border_radius': 12,
            'border_radius_large': 20,
            'shadow_blur': 15,
            'animation_duration': 200
        }

        self.create_window()

    def get_color(self, color_name, variant=''):
        """الحصول على لون مع إمكانية التنويع"""
        if variant:
            key = f"{color_name}_{variant}"
            return self.colors.get(key, self.colors.get(color_name, '#000000'))
        return self.colors.get(color_name, '#000000')

    def get_hover_color(self, color_name):
        """الحصول على لون التمرير"""
        hover_key = f"{color_name}_hover"
        if hover_key in self.colors:
            return self.colors[hover_key]

        # إنشاء لون تمرير تلقائي
        base_color = self.colors.get(color_name, '#000000')
        # تطبيق تأثير تمرير بسيط (تفتيح أو تغميق)
        return self.lighten_color(base_color, 0.1)

    def lighten_color(self, color, factor):
        """تفتيح اللون بنسبة معينة"""
        try:
            # تحويل اللون من hex إلى RGB
            color = color.lstrip('#')
            rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))

            # تطبيق التفتيح
            rgb = tuple(min(255, int(c + (255 - c) * factor)) for c in rgb)

            # تحويل العودة إلى hex
            return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
        except:
            return color

    def create_gradient_frame(self, parent, color1, color2, **kwargs):
        """إنشاء إطار بتدرج لوني (محاكاة)"""
        frame = ctk.CTkFrame(parent, **kwargs)
        # في customtkinter لا يمكن تطبيق تدرج حقيقي، لكن يمكن استخدام لون متوسط
        frame.configure(fg_color=color1)
        return frame

    def create_elevated_frame(self, parent, elevation=1, **kwargs):
        """إنشاء إطار مرتفع مع ظل"""
        # تحديد لون الخلفية حسب مستوى الارتفاع
        if elevation == 1:
            bg_color = self.colors['surface']
        elif elevation == 2:
            bg_color = self.colors['surface_elevated']
        else:
            bg_color = self.colors['surface_variant']

        frame = ctk.CTkFrame(parent, fg_color=bg_color, **kwargs)
        return frame

    def create_window(self):
        """إنشاء لوحة التحكم الشاملة مع التصميم المتقدم"""
        self.window = ctk.CTkToplevel()
        self.window.title("🎛️ لوحة التحكم الشاملة - برنامج ست الكل للمحاسبة")

        # تكوين النافذة المتقدم
        configure_window_fullscreen(self.window)
        self.window.configure(fg_color=self.colors['background'])

        # منع إغلاق النافذة بطريقة خاطئة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

        # إنشاء الإطار الرئيسي مع تصميم متقدم
        main_container = ctk.CTkFrame(
            self.window,
            fg_color="transparent",
            corner_radius=0
        )
        main_container.pack(fill="both", expand=True)

        # شريط العنوان المتقدم
        self.create_advanced_header(main_container)

        # المحتوى الرئيسي
        content_frame = self.create_elevated_frame(
            main_container,
            elevation=1,
            corner_radius=self.dimensions['border_radius_large']
        )
        content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # إنشاء التبويبات المتقدمة
        self.create_advanced_tabs(content_frame)

        # إنشاء شريط الحالة
        self.create_status_bar(main_container)

        # تطبيق التأثيرات البصرية
        self.apply_visual_effects()

        # عرض النافذة
        self.window.deiconify()
        self.window.lift()
        self.window.focus_force()

    def create_advanced_header(self, parent):
        """إنشاء شريط العنوان المتقدم"""
        header_frame = self.create_elevated_frame(
            parent,
            elevation=2,
            height=80,
            corner_radius=self.dimensions['border_radius']
        )
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        header_frame.pack_propagate(False)

        # الجانب الأيمن - العنوان والوصف
        right_section = ctk.CTkFrame(header_frame, fg_color="transparent")
        right_section.pack(side="right", fill="both", expand=True, padx=20, pady=15)

        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            right_section,
            text="🎛️ لوحة التحكم الشاملة",
            font=self.fonts['title'],
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="e", pady=(0, 5))

        # الوصف
        subtitle_label = ctk.CTkLabel(
            right_section,
            text="مركز الإدارة والتحكم الاحترافي لبرنامج المحاسبة",
            font=self.fonts['caption'],
            text_color=self.colors['text_secondary']
        )
        subtitle_label.pack(anchor="e")

        # الجانب الأيسر - معلومات المستخدم والأزرار
        left_section = ctk.CTkFrame(header_frame, fg_color="transparent")
        left_section.pack(side="left", fill="y", padx=20, pady=15)

        # معلومات المستخدم
        user_frame = ctk.CTkFrame(
            left_section,
            fg_color=self.colors['primary'],
            corner_radius=25
        )
        user_frame.pack(side="left", padx=(0, 15))

        user_label = ctk.CTkLabel(
            user_frame,
            text=f"👤 {self.current_user.get('username', 'المدير')}",
            font=self.fonts['body'],
            text_color=self.colors['text_on_primary']
        )
        user_label.pack(padx=20, pady=10)

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(left_section, fg_color="transparent")
        controls_frame.pack(side="left")

        # زر التحديث
        refresh_btn = ctk.CTkButton(
            controls_frame,
            text="🔄",
            width=40,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['info'],
            hover_color=self.get_hover_color('info'),
            corner_radius=20,
            command=self.refresh_data
        )
        refresh_btn.pack(side="top", pady=(0, 5))

        # زر الإعدادات
        settings_btn = ctk.CTkButton(
            controls_frame,
            text="⚙️",
            width=40,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['secondary'],
            hover_color=self.get_hover_color('secondary'),
            corner_radius=20,
            command=self.show_quick_settings
        )
        settings_btn.pack(side="top")

    def create_advanced_tabs(self, parent):
        """إنشاء التبويبات المتقدمة مع تصميم احترافي"""
        # إنشاء حاوي التبويبات
        tabs_container = ctk.CTkFrame(parent, fg_color="transparent")
        tabs_container.pack(fill="both", expand=True, padx=15, pady=15)

        # إنشاء التبويبات
        self.tab_view = ctk.CTkTabview(
            tabs_container,
            width=1200,
            height=700,
            corner_radius=self.dimensions['border_radius'],
            fg_color=self.colors['surface'],
            segmented_button_fg_color=self.colors['surface_variant'],
            segmented_button_selected_color=self.colors['primary'],
            segmented_button_selected_hover_color=self.get_hover_color('primary'),
            segmented_button_unselected_color=self.colors['surface_dark'],
            segmented_button_unselected_hover_color=self.colors['surface_variant'],
            text_color=self.colors['text_primary'],
            text_color_disabled=self.colors['text_disabled']
        )
        self.tab_view.pack(fill="both", expand=True)

        # تكوين التبويبات
        tabs_config = [
            {
                'name': '🧩 الإعدادات العامة',
                'key': 'general',
                'icon': '🧩',
                'title': 'الإعدادات العامة',
                'description': 'معلومات الشركة والإعدادات الأساسية',
                'create_func': self.create_general_settings_tab
            },
            {
                'name': '💾 النسخ الاحتياطي',
                'key': 'backup',
                'icon': '💾',
                'title': 'النسخ الاحتياطي',
                'description': 'إدارة النسخ الاحتياطية والاستعادة',
                'create_func': self.create_backup_tab
            },
            {
                'name': '👥 المستخدمون والصلاحيات',
                'key': 'users',
                'icon': '👥',
                'title': 'المستخدمون والصلاحيات',
                'description': 'إدارة المستخدمين والأدوار',
                'create_func': self.create_users_tab
            },
            {
                'name': '🔄 التحكم بالبيانات',
                'key': 'data',
                'icon': '🔄',
                'title': 'التحكم بالبيانات',
                'description': 'ضبط المصنع وإعادة التعيين',
                'create_func': self.create_data_control_tab
            },
            {
                'name': '📥 استيراد وتصدير',
                'key': 'import_export',
                'icon': '📥',
                'title': 'استيراد وتصدير',
                'description': 'إدارة البيانات من Excel',
                'create_func': self.create_import_export_tab
            },
            {
                'name': '⚙️ إعدادات النظام',
                'key': 'system',
                'icon': '⚙️',
                'title': 'إعدادات النظام',
                'description': 'تكوين النظام المتقدم',
                'create_func': self.create_system_settings_tab
            },
            {
                'name': '🛡️ الأمان والحماية',
                'key': 'security',
                'icon': '🛡️',
                'title': 'الأمان والحماية',
                'description': 'إعدادات الأمان والمراقبة',
                'create_func': self.create_security_tab
            }
        ]

        # إنشاء التبويبات
        for tab_config in tabs_config:
            tab = self.tab_view.add(tab_config['name'])
            tab_config['create_func'](tab, tab_config)

        # تحديد التبويب الافتراضي
        self.tab_view.set('🧩 الإعدادات العامة')

    def create_status_bar(self, parent):
        """إنشاء شريط الحالة المتقدم"""
        status_frame = self.create_elevated_frame(
            parent,
            elevation=1,
            height=50,
            corner_radius=self.dimensions['border_radius']
        )
        status_frame.pack(fill="x", padx=20, pady=(0, 20))
        status_frame.pack_propagate(False)

        # الجانب الأيمن - معلومات الحالة
        right_status = ctk.CTkFrame(status_frame, fg_color="transparent")
        right_status.pack(side="right", fill="y", padx=15, pady=10)

        # حالة الاتصال
        connection_label = ctk.CTkLabel(
            right_status,
            text="🟢 متصل",
            font=self.fonts['caption'],
            text_color=self.colors['success']
        )
        connection_label.pack(side="right", padx=(0, 20))

        # الوقت الحالي
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        time_label = ctk.CTkLabel(
            right_status,
            text=f"🕐 {current_time}",
            font=self.fonts['caption'],
            text_color=self.colors['text_secondary']
        )
        time_label.pack(side="right", padx=(0, 20))

        # الجانب الأيسر - أزرار الإجراءات
        left_status = ctk.CTkFrame(status_frame, fg_color="transparent")
        left_status.pack(side="left", fill="y", padx=15, pady=10)

        # زر الحفظ السريع
        quick_save_btn = ctk.CTkButton(
            left_status,
            text="💾 حفظ سريع",
            width=100,
            height=30,
            font=self.fonts['caption'],
            fg_color=self.colors['success'],
            hover_color=self.get_hover_color('success'),
            corner_radius=15,
            command=self.quick_save
        )
        quick_save_btn.pack(side="left", padx=(0, 10))

        # زر المساعدة
        help_btn = ctk.CTkButton(
            left_status,
            text="❓ مساعدة",
            width=80,
            height=30,
            font=self.fonts['caption'],
            fg_color=self.colors['info'],
            hover_color=self.get_hover_color('info'),
            corner_radius=15,
            command=self.show_help
        )
        help_btn.pack(side="left")

    def apply_visual_effects(self):
        """تطبيق التأثيرات البصرية المتقدمة"""
        # تأثير الظهور التدريجي
        self.window.attributes('-alpha', 0.0)
        self.fade_in()

    def fade_in(self):
        """تأثير الظهور التدريجي"""
        alpha = self.window.attributes('-alpha')
        if alpha < 1.0:
            alpha += 0.05
            self.window.attributes('-alpha', alpha)
            self.window.after(20, self.fade_in)

    def on_closing(self):
        """معالج إغلاق النافذة"""
        if messagebox.askyesno("تأكيد الإغلاق", "هل تريد إغلاق لوحة التحكم؟"):
            self.fade_out()

    def fade_out(self):
        """تأثير الاختفاء التدريجي"""
        alpha = self.window.attributes('-alpha')
        if alpha > 0.0:
            alpha -= 0.1
            self.window.attributes('-alpha', alpha)
            self.window.after(20, self.fade_out)
        else:
            self.if window and hasattr(window, "destroy"):
    if window and hasattr(window, "destroy"):
    window.destroy()

    def refresh_data(self):
        """تحديث البيانات"""
        self.show_toast("🔄 جاري تحديث البيانات...", "info")
        # تحديث البيانات هنا
        self.window.after(1000, lambda: self.show_toast("✅ تم تحديث البيانات بنجاح", "success"))

    def show_quick_settings(self):
        """عرض الإعدادات السريعة"""
        self.show_toast("⚙️ فتح الإعدادات السريعة...", "info")

    def quick_save(self):
        """الحفظ السريع"""
        self.show_toast("💾 جاري الحفظ...", "info")
        # حفظ الإعدادات هنا
        self.window.after(500, lambda: self.show_toast("✅ تم الحفظ بنجاح", "success"))

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
🎛️ لوحة التحكم الشاملة - دليل الاستخدام

📋 التبويبات المتاحة:
• الإعدادات العامة: معلومات الشركة والإعدادات الأساسية
• النسخ الاحتياطي: إدارة النسخ والاستعادة
• المستخدمون: إدارة المستخدمين والصلاحيات
• التحكم بالبيانات: ضبط المصنع وإعادة التعيين
• استيراد وتصدير: إدارة البيانات من Excel
• إعدادات النظام: تكوين النظام المتقدم
• الأمان والحماية: إعدادات الأمان والمراقبة

💡 نصائح:
• استخدم الحفظ السريع لحفظ التغييرات
• تحقق من شريط الحالة للمعلومات المهمة
• استخدم زر التحديث لتحديث البيانات
        """

        messagebox.showinfo("مساعدة لوحة التحكم", help_text)

    def create_general_settings_tab(self, tab, config):
        """إنشاء تبويب الإعدادات العامة المتقدم"""
        # إنشاء حاوي قابل للتمرير
        scrollable_frame = ctk.CTkScrollableFrame(
            tab,
            fg_color="transparent",
            corner_radius=0
        )
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        self.create_tab_header(scrollable_frame, config)

        # قسم معلومات الشركة
        company_section = self.create_settings_section(
            scrollable_frame,
            "🏢 معلومات الشركة",
            "البيانات الأساسية للمؤسسة"
        )

        # حقول معلومات الشركة
        self.create_input_field(
            company_section,
            "اسم المؤسسة:",
            "company_name",
            self.settings.get('company_name', 'شركة ست الكل للمحاسبة'),
            required=True
        )

        self.create_input_field(
            company_section,
            "رقم السجل التجاري:",
            "commercial_register",
            self.settings.get('commercial_register', ''),
            placeholder="1010123456"
        )

        self.create_input_field(
            company_section,
            "رقم الهاتف:",
            "phone",
            self.settings.get('phone', ''),
            placeholder="+966-XX-XXX-XXXX"
        )

        self.create_input_field(
            company_section,
            "البريد الإلكتروني:",
            "email",
            self.settings.get('email', ''),
            placeholder="<EMAIL>"
        )

        self.create_textarea_field(
            company_section,
            "العنوان:",
            "address",
            self.settings.get('address', ''),
            height=80
        )

        self.create_file_field(
            company_section,
            "شعار الشركة:",
            "company_logo",
            self.settings.get('company_logo', ''),
            file_types=[("صور", "*.png *.jpg *.jpeg *.gif *.bmp")]
        )

        # قسم الإعدادات المالية
        financial_section = self.create_settings_section(
            scrollable_frame,
            "💰 الإعدادات المالية",
            "إعدادات العملة والضرائب"
        )

        self.create_dropdown_field(
            financial_section,
            "اللغة الافتراضية:",
            "default_language",
            ["العربية", "English", "Français"],
            self.settings.get('default_language', 'العربية')
        )

        self.create_dropdown_field(
            financial_section,
            "العملة الرئيسية:",
            "main_currency",
            ["ريال سعودي", "درهم إماراتي", "دينار كويتي", "دولار أمريكي", "يورو"],
            self.settings.get('main_currency', 'ريال سعودي')
        )

        self.create_input_field(
            financial_section,
            "رمز العملة:",
            "currency_symbol",
            self.settings.get('currency_symbol', 'ر.س'),
            width=100
        )

        self.create_number_field(
            financial_section,
            "نسبة الضريبة (%):",
            "tax_rate",
            self.settings.get('tax_rate', 15.0),
            min_value=0,
            max_value=50,
            step=0.5
        )

        self.create_number_field(
            financial_section,
            "نسبة الخصم الافتراضية (%):",
            "default_discount",
            self.settings.get('default_discount', 0.0),
            min_value=0,
            max_value=100,
            step=1.0
        )

        # قسم إعدادات الفواتير
        invoice_section = self.create_settings_section(
            scrollable_frame,
            "🧾 إعدادات الفواتير",
            "تكوين الفواتير وأنماط الطباعة"
        )

        self.create_dropdown_field(
            invoice_section,
            "نمط الفاتورة:",
            "invoice_template",
            ["كلاسيكي", "حديث", "مبسط", "مفصل"],
            self.settings.get('invoice_template', 'حديث')
        )

        self.create_dropdown_field(
            invoice_section,
            "حجم الورق:",
            "paper_size",
            ["A4", "A5", "Letter"],
            self.settings.get('paper_size', 'A4')
        )

        self.create_checkbox_field(
            invoice_section,
            "إظهار الشعار في الفواتير",
            "show_logo_in_invoice",
            self.settings.get('show_logo_in_invoice', True)
        )

        self.create_checkbox_field(
            invoice_section,
            "طباعة تلقائية للفواتير",
            "auto_print_invoice",
            self.settings.get('auto_print_invoice', False)
        )

        # أزرار الحفظ
        self.create_action_buttons(scrollable_frame)

    def create_tab_header(self, parent, config):
        """إنشاء رأس التبويب"""
        header_frame = self.create_elevated_frame(
            parent,
            elevation=1,
            height=80,
            corner_radius=self.dimensions['border_radius']
        )
        header_frame.pack(fill="x", pady=(0, 20))
        header_frame.pack_propagate(False)

        # الأيقونة والعنوان
        content_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        content_frame.pack(expand=True, fill="both", padx=20, pady=15)

        # العنوان
        title_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        title_frame.pack(side="right", fill="y")

        title_label = ctk.CTkLabel(
            title_frame,
            text=f"{config['icon']} {config['title']}",
            font=self.fonts['subtitle'],
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="e", pady=(5, 0))

        desc_label = ctk.CTkLabel(
            title_frame,
            text=config['description'],
            font=self.fonts['caption'],
            text_color=self.colors['text_secondary']
        )
        desc_label.pack(anchor="e")

    def create_settings_section(self, parent, title, description):
        """إنشاء قسم إعدادات"""
        # إطار القسم
        section_frame = self.create_elevated_frame(
            parent,
            elevation=1,
            corner_radius=self.dimensions['border_radius']
        )
        section_frame.pack(fill="x", pady=(0, 15))

        # رأس القسم
        header_frame = ctk.CTkFrame(
            section_frame,
            fg_color=self.colors['primary'],
            corner_radius=self.dimensions['border_radius'],
            height=50
        )
        header_frame.pack(fill="x", padx=2, pady=2)
        header_frame.pack_propagate(False)

        # العنوان والوصف
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="right", fill="both", expand=True, padx=15, pady=10)

        title_label = ctk.CTkLabel(
            title_frame,
            text=title,
            font=self.fonts['heading'],
            text_color=self.colors['text_on_primary']
        )
        title_label.pack(anchor="e")

        if description:
            desc_label = ctk.CTkLabel(
                title_frame,
                text=description,
                font=self.fonts['caption'],
                text_color=self.colors['text_on_primary']
            )
            desc_label.pack(anchor="e")

        # محتوى القسم
        content_frame = ctk.CTkFrame(section_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=15)

        return content_frame

    def create_input_field(self, parent, label, key, default_value="", placeholder="", required=False, width=300):
        """إنشاء حقل إدخال نصي"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=8)

        # التسمية
        label_frame = ctk.CTkFrame(field_frame, fg_color="transparent", width=200)
        label_frame.pack(side="right", fill="y", padx=(0, 15))
        label_frame.pack_propagate(False)

        label_text = f"{label} {'*' if required else ''}"
        label_widget = ctk.CTkLabel(
            label_frame,
            text=label_text,
            font=self.fonts['body'],
            text_color=self.colors['error'] if required else self.colors['text_primary']
        )
        label_widget.pack(anchor="e", pady=5)

        # حقل الإدخال
        var = ctk.StringVar(value=default_value)
        entry = ctk.CTkEntry(
            field_frame,
            textvariable=var,
            placeholder_text=placeholder,
            font=self.fonts['body'],
            width=width,
            height=35,
            corner_radius=8,
            fg_color=self.colors['surface'],
            border_color=self.colors['border'],
            text_color=self.colors['text_primary']
        )
        entry.pack(side="left", padx=(0, 10))

        self.settings_vars[key] = var
        return entry

    def create_textarea_field(self, parent, label, key, default_value="", height=100):
        """إنشاء حقل نص متعدد الأسطر"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=8)

        # التسمية
        label_frame = ctk.CTkFrame(field_frame, fg_color="transparent", width=200)
        label_frame.pack(side="right", fill="y", padx=(0, 15))
        label_frame.pack_propagate(False)

        label_widget = ctk.CTkLabel(
            label_frame,
            text=label,
            font=self.fonts['body'],
            text_color=self.colors['text_primary']
        )
        label_widget.pack(anchor="e", pady=5)

        # حقل النص
        textbox = ctk.CTkTextbox(
            field_frame,
            font=self.fonts['body'],
            width=300,
            height=height,
            corner_radius=8,
            fg_color=self.colors['surface'],
            border_color=self.colors['border'],
            text_color=self.colors['text_primary']
        )
        textbox.pack(side="left", padx=(0, 10))
        textbox.insert("1.0", default_value)

        self.settings_vars[key] = textbox
        return textbox

    def create_dropdown_field(self, parent, label, key, options, default_value=""):
        """إنشاء قائمة منسدلة"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=8)

        # التسمية
        label_frame = ctk.CTkFrame(field_frame, fg_color="transparent", width=200)
        label_frame.pack(side="right", fill="y", padx=(0, 15))
        label_frame.pack_propagate(False)

        label_widget = ctk.CTkLabel(
            label_frame,
            text=label,
            font=self.fonts['body'],
            text_color=self.colors['text_primary']
        )
        label_widget.pack(anchor="e", pady=5)

        # القائمة المنسدلة
        var = ctk.StringVar(value=default_value)
        dropdown = ctk.CTkComboBox(
            field_frame,
            variable=var,
            values=options,
            font=self.fonts['body'],
            width=300,
            height=35,
            corner_radius=8,
            fg_color=self.colors['surface'],
            border_color=self.colors['border'],
            text_color=self.colors['text_primary'],
            dropdown_fg_color=self.colors['surface'],
            dropdown_text_color=self.colors['text_primary']
        )
        dropdown.pack(side="left", padx=(0, 10))

        self.settings_vars[key] = var
        return dropdown

    def create_number_field(self, parent, label, key, default_value=0, min_value=0, max_value=100, step=1):
        """إنشاء حقل رقمي"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=8)

        # التسمية
        label_frame = ctk.CTkFrame(field_frame, fg_color="transparent", width=200)
        label_frame.pack(side="right", fill="y", padx=(0, 15))
        label_frame.pack_propagate(False)

        label_widget = ctk.CTkLabel(
            label_frame,
            text=label,
            font=self.fonts['body'],
            text_color=self.colors['text_primary']
        )
        label_widget.pack(anchor="e", pady=5)

        # حقل الرقم
        var = ctk.DoubleVar(value=default_value)

        # إطار الرقم مع الأزرار
        number_frame = ctk.CTkFrame(field_frame, fg_color="transparent")
        number_frame.pack(side="left", padx=(0, 10))

        # زر التقليل
        decrease_btn = ctk.CTkButton(
            number_frame,
            text="−",
            width=30,
            height=35,
            font=self.fonts['button'],
            fg_color=self.colors['secondary'],
            hover_color=self.get_hover_color('secondary'),
            corner_radius=8,
            command=lambda: self.change_number_value(var, -step, min_value, max_value)
        )
        decrease_btn.pack(side="left")

        # حقل الإدخال
        entry = ctk.CTkEntry(
            number_frame,
            textvariable=var,
            font=self.fonts['body'],
            width=100,
            height=35,
            corner_radius=8,
            fg_color=self.colors['surface'],
            border_color=self.colors['border'],
            text_color=self.colors['text_primary'],
            justify="center"
        )
        entry.pack(side="left", padx=2)

        # زر الزيادة
        increase_btn = ctk.CTkButton(
            number_frame,
            text="+",
            width=30,
            height=35,
            font=self.fonts['button'],
            fg_color=self.colors['secondary'],
            hover_color=self.get_hover_color('secondary'),
            corner_radius=8,
            command=lambda: self.change_number_value(var, step, min_value, max_value)
        )
        increase_btn.pack(side="left")

        self.settings_vars[key] = var
        return entry

    def change_number_value(self, var, change, min_val, max_val):
        """تغيير قيمة الحقل الرقمي"""
        try:
            current = var.get()
            new_value = current + change
            new_value = max(min_val, min(max_val, new_value))
            var.set(new_value)
        except:
            var.set(min_val)

    def create_checkbox_field(self, parent, label, key, default_value=False):
        """إنشاء مربع اختيار"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=8)

        # مربع الاختيار
        var = ctk.BooleanVar(value=default_value)
        checkbox = ctk.CTkCheckBox(
            field_frame,
            text=label,
            variable=var,
            font=self.fonts['body'],
            text_color=self.colors['text_primary'],
            fg_color=self.colors['primary'],
            hover_color=self.get_hover_color('primary'),
            checkmark_color=self.colors['text_on_primary']
        )
        checkbox.pack(anchor="e", padx=10)

        self.settings_vars[key] = var
        return checkbox

    def create_file_field(self, parent, label, key, default_value="", file_types=None):
        """إنشاء حقل اختيار ملف"""
        if file_types is None:
            file_types = [("جميع الملفات", "*.*")]

        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=8)

        # التسمية
        label_frame = ctk.CTkFrame(field_frame, fg_color="transparent", width=200)
        label_frame.pack(side="right", fill="y", padx=(0, 15))
        label_frame.pack_propagate(False)

        label_widget = ctk.CTkLabel(
            label_frame,
            text=label,
            font=self.fonts['body'],
            text_color=self.colors['text_primary']
        )
        label_widget.pack(anchor="e", pady=5)

        # إطار الملف
        file_frame = ctk.CTkFrame(field_frame, fg_color="transparent")
        file_frame.pack(side="left", padx=(0, 10))

        # حقل المسار
        var = ctk.StringVar(value=default_value)
        path_entry = ctk.CTkEntry(
            file_frame,
            textvariable=var,
            font=self.fonts['body'],
            width=250,
            height=35,
            corner_radius=8,
            fg_color=self.colors['surface'],
            border_color=self.colors['border'],
            text_color=self.colors['text_primary']
        )
        path_entry.pack(side="left", padx=(0, 5))

        # زر التصفح
        browse_btn = ctk.CTkButton(
            file_frame,
            text="📁 تصفح",
            width=80,
            height=35,
            font=self.fonts['body'],
            fg_color=self.colors['info'],
            hover_color=self.get_hover_color('info'),
            corner_radius=8,
            command=lambda: self.browse_file(var, file_types)
        )
        browse_btn.pack(side="left")

        self.settings_vars[key] = var
        return path_entry

    def browse_file(self, var, file_types):
        """تصفح واختيار ملف"""
        filename = filedialog.askopenfilename(
            title="اختيار ملف",
            filetypes=file_types
        )
        if filename:
            var.set(filename)

    def create_action_buttons(self, parent):
        """إنشاء أزرار الإجراءات"""
        buttons_frame = self.create_elevated_frame(
            parent,
            elevation=1,
            height=70,
            corner_radius=self.dimensions['border_radius']
        )
        buttons_frame.pack(fill="x", pady=20)
        buttons_frame.pack_propagate(False)

        # إطار الأزرار
        controls_frame = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        controls_frame.pack(expand=True, fill="both", padx=20, pady=15)

        # زر الحفظ
        save_btn = ctk.CTkButton(
            controls_frame,
            text="💾 حفظ الإعدادات",
            width=150,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['success'],
            hover_color=self.get_hover_color('success'),
            corner_radius=self.dimensions['border_radius'],
            command=self.save_all_settings
        )
        save_btn.pack(side="left", padx=(0, 10))

        # زر الإلغاء
        cancel_btn = ctk.CTkButton(
            controls_frame,
            text="❌ إلغاء",
            width=100,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['error'],
            hover_color=self.get_hover_color('error'),
            corner_radius=self.dimensions['border_radius'],
            command=self.cancel_changes
        )
        cancel_btn.pack(side="left", padx=(0, 10))

        # زر الاستعادة
        reset_btn = ctk.CTkButton(
            controls_frame,
            text="🔄 استعادة الافتراضي",
            width=150,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['warning'],
            hover_color=self.get_hover_color('warning'),
            corner_radius=self.dimensions['border_radius'],
            command=self.reset_to_defaults
        )
        reset_btn.pack(side="right")

        # زر المعاينة
        preview_btn = ctk.CTkButton(
            controls_frame,
            text="👁️ معاينة",
            width=100,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['info'],
            hover_color=self.get_hover_color('info'),
            corner_radius=self.dimensions['border_radius'],
            command=self.preview_settings
        )
        preview_btn.pack(side="right", padx=(0, 10))

    def save_all_settings(self):
        """حفظ جميع الإعدادات"""
        try:
            self.show_toast("💾 جاري حفظ الإعدادات...", "info")

            # جمع القيم من الواجهة
            new_settings = {}
            for key, var in self.settings_vars.items():
                try:
                    if isinstance(var, ctk.CTkTextbox):
                        new_settings[key] = var.get("1.0", "end-1c").strip()
                    elif isinstance(var, (ctk.StringVar, ctk.BooleanVar, ctk.DoubleVar, ctk.IntVar)):
                        new_settings[key] = var.get()
                    else:
                        new_settings[key] = str(var)
                except Exception as e:
                    print(f"خطأ في قراءة القيمة {key}: {e}")
                    continue

            # التحقق من صحة البيانات
            validation_errors = self.validate_settings(new_settings)
            if validation_errors:
                error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(validation_errors)
                messagebox.showerror("خطأ في البيانات", error_message)
                self.show_toast("❌ فشل في الحفظ - يرجى تصحيح الأخطاء", "error")
                return

            # حفظ في الملف
            settings_file = PROJECT_ROOT / "config" / "admin_settings.json"
            settings_file.parent.mkdir(parents=True, exist_ok=True)

            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(new_settings, f, ensure_ascii=False, indent=2)

            # تحديث الإعدادات المحلية
            self.settings.update(new_settings)

            self.show_toast("✅ تم حفظ الإعدادات بنجاح", "success")

            # إشعار بإعادة التشغيل إذا لزم الأمر
            if self.requires_restart(new_settings):
                messagebox.showinfo(
                    "إعادة تشغيل مطلوبة",
                    "تم حفظ الإعدادات بنجاح.\nبعض الإعدادات تتطلب إعادة تشغيل البرنامج لتطبيقها."
                )

        except Exception as e:
            error_msg = f"حدث خطأ في حفظ الإعدادات: {str(e)}"
            messagebox.showerror("خطأ", error_msg)
            self.show_toast("❌ فشل في حفظ الإعدادات", "error")

    def validate_settings(self, settings):
        """التحقق من صحة الإعدادات"""
        errors = []

        # التحقق من الحقول المطلوبة
        required_fields = {
            'company_name': 'اسم المؤسسة',
            'main_currency': 'العملة الرئيسية',
            'currency_symbol': 'رمز العملة'
        }

        for field, label in required_fields.items():
            if not settings.get(field, '').strip():
                errors.append(f"• {label} مطلوب")

        # التحقق من البريد الإلكتروني
        email = settings.get('email', '').strip()
        if email and '@' not in email:
            errors.append("• البريد الإلكتروني غير صحيح")

        # التحقق من نسبة الضريبة
        try:
            tax_rate = float(settings.get('tax_rate', 0))
            if tax_rate < 0 or tax_rate > 50:
                errors.append("• نسبة الضريبة يجب أن تكون بين 0 و 50")
        except ValueError:
            errors.append("• نسبة الضريبة يجب أن تكون رقماً")

        # التحقق من نسبة الخصم
        try:
            discount = float(settings.get('default_discount', 0))
            if discount < 0 or discount > 100:
                errors.append("• نسبة الخصم يجب أن تكون بين 0 و 100")
        except ValueError:
            errors.append("• نسبة الخصم يجب أن تكون رقماً")

        return errors

    def requires_restart(self, settings):
        """تحديد ما إذا كانت الإعدادات تتطلب إعادة تشغيل"""
        restart_required_fields = ['default_language', 'main_currency']

        for field in restart_required_fields:
            if settings.get(field) != self.settings.get(field):
                return True
        return False

    def cancel_changes(self):
        """إلغاء التغييرات"""
        if messagebox.askyesno("تأكيد الإلغاء", "هل تريد إلغاء جميع التغييرات؟"):
            self.load_settings_to_ui()
            self.show_toast("🔄 تم إلغاء التغييرات", "info")

    def reset_to_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        if messagebox.askyesno(
            "تأكيد الاستعادة",
            "هل تريد استعادة جميع الإعدادات إلى القيم الافتراضية؟\nسيتم فقدان جميع الإعدادات الحالية."
        ):
            try:
                # حذف ملف الإعدادات
                settings_file = PROJECT_ROOT / "config" / "admin_settings.json"
                if settings_file.exists():
                    settings_file.unlink()

                # إعادة تحميل الإعدادات الافتراضية
                self.settings = self.load_settings()
                self.load_settings_to_ui()

                self.show_toast("✅ تم استعادة الإعدادات الافتراضية", "success")
                messagebox.showinfo("تم", "تم استعادة الإعدادات الافتراضية بنجاح.")

            except Exception as e:
                error_msg = f"حدث خطأ في استعادة الإعدادات: {str(e)}"
                messagebox.showerror("خطأ", error_msg)
                self.show_toast("❌ فشل في استعادة الإعدادات", "error")

    def preview_settings(self):
        """معاينة الإعدادات"""
        preview_text = "📋 معاينة الإعدادات الحالية:\n\n"

        sections = {
            "معلومات الشركة": [
                ('company_name', 'اسم المؤسسة'),
                ('commercial_register', 'رقم السجل التجاري'),
                ('phone', 'رقم الهاتف'),
                ('email', 'البريد الإلكتروني')
            ],
            "الإعدادات المالية": [
                ('main_currency', 'العملة الرئيسية'),
                ('currency_symbol', 'رمز العملة'),
                ('tax_rate', 'نسبة الضريبة'),
                ('default_discount', 'نسبة الخصم الافتراضية')
            ]
        }

        for section_name, fields in sections.items():
            preview_text += f"🔹 {section_name}:\n"
            for field_key, field_label in fields:
                value = self.settings.get(field_key, 'غير محدد')
                if field_key in ['tax_rate', 'default_discount']:
                    value = f"{value}%"
                preview_text += f"   • {field_label}: {value}\n"
            preview_text += "\n"

        messagebox.showinfo("معاينة الإعدادات", preview_text)

    def load_settings_to_ui(self):
        """تحميل الإعدادات إلى الواجهة"""
        for key, var in self.settings_vars.items():
            try:
                value = self.settings.get(key, '')
                if isinstance(var, ctk.CTkTextbox):
                    var.delete("1.0", "end")
                    var.insert("1.0", str(value))
                elif isinstance(var, (ctk.StringVar, ctk.BooleanVar, ctk.DoubleVar, ctk.IntVar)):
                    var.set(value)
            except Exception as e:
                print(f"خطأ في تحميل القيمة {key}: {e}")

    def create_backup_tab(self, tab, config):
        """إنشاء تبويب النسخ الاحتياطي المتقدم"""
        # إنشاء حاوي قابل للتمرير
        scrollable_frame = ctk.CTkScrollableFrame(
            tab,
            fg_color="transparent",
            corner_radius=0
        )
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        self.create_tab_header(scrollable_frame, config)

        # قسم النسخ الاحتياطي السريع
        quick_backup_section = self.create_settings_section(
            scrollable_frame,
            "⚡ النسخ الاحتياطي السريع",
            "إنشاء نسخة احتياطية فورية"
        )

        # أزرار النسخ السريع
        quick_buttons_frame = ctk.CTkFrame(quick_backup_section, fg_color="transparent")
        quick_buttons_frame.pack(fill="x", pady=10)

        # زر النسخ الكامل
        full_backup_btn = ctk.CTkButton(
            quick_buttons_frame,
            text="💾 نسخة احتياطية كاملة",
            width=200,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['primary'],
            hover_color=self.get_hover_color('primary'),
            corner_radius=self.dimensions['border_radius'],
            command=self.create_full_backup
        )
        full_backup_btn.pack(side="right", padx=(0, 10))

        # زر النسخ السريع
        quick_backup_btn = ctk.CTkButton(
            quick_buttons_frame,
            text="⚡ نسخة سريعة",
            width=150,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['success'],
            hover_color=self.get_hover_color('success'),
            corner_radius=self.dimensions['border_radius'],
            command=self.create_quick_backup
        )
        quick_backup_btn.pack(side="right", padx=(0, 10))

        # زر الاستعادة
        restore_btn = ctk.CTkButton(
            quick_buttons_frame,
            text="📥 استعادة نسخة",
            width=150,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['warning'],
            hover_color=self.get_hover_color('warning'),
            corner_radius=self.dimensions['border_radius'],
            command=self.restore_backup
        )
        restore_btn.pack(side="left")

        # قسم إعدادات النسخ الاحتياطي
        backup_settings_section = self.create_settings_section(
            scrollable_frame,
            "⚙️ إعدادات النسخ الاحتياطي",
            "تكوين النسخ التلقائي والمجلدات"
        )

        # مجلد النسخ الاحتياطي
        self.create_file_field(
            backup_settings_section,
            "مجلد النسخ الاحتياطي:",
            "backup_directory",
            self.settings.get('backup_directory', str(PROJECT_ROOT / "backups")),
            file_types=[("مجلدات", "")]
        )

        # عدد النسخ المحفوظة
        self.create_number_field(
            backup_settings_section,
            "عدد النسخ المحفوظة:",
            "max_backups",
            self.settings.get('max_backups', 30),
            min_value=5,
            max_value=100,
            step=5
        )

        # تفعيل النسخ التلقائي
        self.create_checkbox_field(
            backup_settings_section,
            "تفعيل النسخ التلقائي",
            "auto_backup_enabled",
            self.settings.get('auto_backup_enabled', True)
        )

        # فترة النسخ التلقائي
        self.create_dropdown_field(
            backup_settings_section,
            "فترة النسخ التلقائي:",
            "backup_interval",
            ["يومياً", "أسبوعياً", "شهرياً", "عند الإغلاق"],
            self.settings.get('backup_interval', 'يومياً')
        )

        # وقت النسخ التلقائي
        self.create_dropdown_field(
            backup_settings_section,
            "وقت النسخ التلقائي:",
            "backup_time",
            ["00:00", "02:00", "04:00", "06:00", "08:00", "10:00", "12:00",
             "14:00", "16:00", "18:00", "20:00", "22:00"],
            self.settings.get('backup_time', '02:00')
        )

        # ضغط النسخ الاحتياطية
        self.create_checkbox_field(
            backup_settings_section,
            "ضغط النسخ الاحتياطية (توفير مساحة)",
            "compress_backups",
            self.settings.get('compress_backups', True)
        )

        # تشفير النسخ الاحتياطية
        self.create_checkbox_field(
            backup_settings_section,
            "تشفير النسخ الاحتياطية (حماية إضافية)",
            "encrypt_backups",
            self.settings.get('encrypt_backups', False)
        )

        # قسم سجل النسخ الاحتياطية
        backup_log_section = self.create_settings_section(
            scrollable_frame,
            "📋 سجل النسخ الاحتياطية",
            "عرض النسخ السابقة وإدارتها"
        )

        # إنشاء جدول النسخ الاحتياطية
        self.create_backup_log_table(backup_log_section)

        # أزرار الحفظ
        self.create_action_buttons(scrollable_frame)

    def create_backup_log_table(self, parent):
        """إنشاء جدول سجل النسخ الاحتياطية"""
        # إطار الجدول
        table_frame = self.create_elevated_frame(
            parent,
            elevation=1,
            corner_radius=self.dimensions['border_radius']
        )
        table_frame.pack(fill="both", expand=True, pady=10)

        # رأس الجدول
        header_frame = ctk.CTkFrame(
            table_frame,
            fg_color=self.colors['primary'],
            corner_radius=self.dimensions['border_radius'],
            height=40
        )
        header_frame.pack(fill="x", padx=5, pady=5)
        header_frame.pack_propagate(False)

        # عناوين الأعمدة
        headers = ["التاريخ والوقت", "النوع", "الحجم", "الحالة", "الإجراءات"]
        header_weights = [3, 2, 2, 2, 3]

        for i, (header, weight) in enumerate(zip(headers, header_weights)):
            header_label = ctk.CTkLabel(
                header_frame,
                text=header,
                font=self.fonts['body'],
                text_color=self.colors['text_on_primary']
            )
            header_label.grid(row=0, column=i, sticky="ew", padx=5, pady=8)
            header_frame.grid_columnconfigure(i, weight=weight)

        # محتوى الجدول (قابل للتمرير)
        content_frame = ctk.CTkScrollableFrame(
            table_frame,
            fg_color="transparent",
            height=200
        )
        content_frame.pack(fill="both", expand=True, padx=5, pady=(0, 5))

        # بيانات وهمية للنسخ الاحتياطية
        backup_data = self.get_backup_history()

        for i, backup in enumerate(backup_data):
            row_frame = ctk.CTkFrame(
                content_frame,
                fg_color=self.colors['surface_variant'] if i % 2 == 0 else self.colors['surface'],
                corner_radius=5
            )
            row_frame.pack(fill="x", pady=2)

            # التاريخ والوقت
            date_label = ctk.CTkLabel(
                row_frame,
                text=backup['date'],
                font=self.fonts['caption'],
                text_color=self.colors['text_primary']
            )
            date_label.grid(row=0, column=0, sticky="ew", padx=5, pady=5)

            # النوع
            type_label = ctk.CTkLabel(
                row_frame,
                text=backup['type'],
                font=self.fonts['caption'],
                text_color=self.colors['text_primary']
            )
            type_label.grid(row=0, column=1, sticky="ew", padx=5, pady=5)

            # الحجم
            size_label = ctk.CTkLabel(
                row_frame,
                text=backup['size'],
                font=self.fonts['caption'],
                text_color=self.colors['text_primary']
            )
            size_label.grid(row=0, column=2, sticky="ew", padx=5, pady=5)

            # الحالة
            status_color = self.colors['success'] if backup['status'] == 'مكتمل' else self.colors['error']
            status_label = ctk.CTkLabel(
                row_frame,
                text=backup['status'],
                font=self.fonts['caption'],
                text_color=status_color
            )
            status_label.grid(row=0, column=3, sticky="ew", padx=5, pady=5)

            # الإجراءات
            actions_frame = ctk.CTkFrame(row_frame, fg_color="transparent")
            actions_frame.grid(row=0, column=4, sticky="ew", padx=5, pady=2)

            # زر الاستعادة
            restore_btn = ctk.CTkButton(
                actions_frame,
                text="📥",
                width=30,
                height=25,
                font=self.fonts['caption'],
                fg_color=self.colors['info'],
                hover_color=self.get_hover_color('info'),
                corner_radius=5,
                command=lambda b=backup: self.restore_specific_backup(b)
            )
            restore_btn.pack(side="left", padx=2)

            # زر الحذف
            delete_btn = ctk.CTkButton(
                actions_frame,
                text="🗑️",
                width=30,
                height=25,
                font=self.fonts['caption'],
                fg_color=self.colors['error'],
                hover_color=self.get_hover_color('error'),
                corner_radius=5,
                command=lambda b=backup: self.delete_backup(b)
            )
            delete_btn.pack(side="left", padx=2)

            # تكوين الأعمدة
            for j in range(5):
                row_frame.grid_columnconfigure(j, weight=header_weights[j])

    def get_backup_history(self):
        """الحصول على سجل النسخ الاحتياطية"""
        # بيانات وهمية - في التطبيق الحقيقي ستأتي من قاعدة البيانات
        return [
            {
                'id': 1,
                'date': '2024-01-15 02:00:00',
                'type': 'كاملة',
                'size': '125 MB',
                'status': 'مكتمل',
                'file_path': '/backups/backup_20240115_020000.zip'
            },
            {
                'id': 2,
                'date': '2024-01-14 02:00:00',
                'type': 'سريعة',
                'size': '45 MB',
                'status': 'مكتمل',
                'file_path': '/backups/backup_20240114_020000.zip'
            },
            {
                'id': 3,
                'date': '2024-01-13 02:00:00',
                'type': 'كاملة',
                'size': '130 MB',
                'status': 'فشل',
                'file_path': None
            }
        ]

    def create_full_backup(self):
        """إنشاء نسخة احتياطية كاملة"""
        if messagebox.askyesno("تأكيد النسخ", "هل تريد إنشاء نسخة احتياطية كاملة؟\nقد يستغرق هذا بعض الوقت."):
            self.show_toast("💾 جاري إنشاء النسخة الاحتياطية الكاملة...", "info")

            # محاكاة عملية النسخ
            self.window.after(3000, lambda: self.show_toast("✅ تم إنشاء النسخة الاحتياطية بنجاح", "success"))

    def create_quick_backup(self):
        """إنشاء نسخة احتياطية سريعة"""
        self.show_toast("⚡ جاري إنشاء النسخة السريعة...", "info")

        # محاكاة عملية النسخ السريع
        self.window.after(1500, lambda: self.show_toast("✅ تم إنشاء النسخة السريعة بنجاح", "success"))

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        backup_file = filedialog.askopenfilename(
            title="اختيار ملف النسخة الاحتياطية",
            filetypes=[("ملفات النسخ الاحتياطي", "*.zip *.bak"), ("جميع الملفات", "*.*")]
        )

        if backup_file:
            if messagebox.askyesno(
                "تأكيد الاستعادة",
                "هل تريد استعادة هذه النسخة الاحتياطية؟\n\n"
                "تحذير: سيتم استبدال البيانات الحالية!"
            ):
                self.show_toast("📥 جاري استعادة النسخة الاحتياطية...", "info")

                # محاكاة عملية الاستعادة
                self.window.after(2000, lambda: self.show_toast("✅ تم استعادة النسخة الاحتياطية بنجاح", "success"))

    def restore_specific_backup(self, backup):
        """استعادة نسخة احتياطية محددة"""
        if messagebox.askyesno(
            "تأكيد الاستعادة",
            f"هل تريد استعادة النسخة الاحتياطية من {backup['date']}؟\n\n"
            "تحذير: سيتم استبدال البيانات الحالية!"
        ):
            self.show_toast(f"📥 جاري استعادة النسخة من {backup['date']}...", "info")

            # محاكاة عملية الاستعادة
            self.window.after(2000, lambda: self.show_toast("✅ تم استعادة النسخة الاحتياطية بنجاح", "success"))

    def delete_backup(self, backup):
        """حذف نسخة احتياطية"""
        if messagebox.askyesno(
            "تأكيد الحذف",
            f"هل تريد حذف النسخة الاحتياطية من {backup['date']}؟\n\n"
            "لا يمكن التراجع عن هذا الإجراء!"
        ):
            self.show_toast(f"🗑️ جاري حذف النسخة من {backup['date']}...", "info")

            # محاكاة عملية الحذف
            self.window.after(1000, lambda: self.show_toast("✅ تم حذف النسخة الاحتياطية", "success"))

    def create_users_tab(self, tab, config):
        """إنشاء تبويب المستخدمين والصلاحيات"""
        # إنشاء حاوي قابل للتمرير
        scrollable_frame = ctk.CTkScrollableFrame(
            tab,
            fg_color="transparent",
            corner_radius=0
        )
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        self.create_tab_header(scrollable_frame, config)

        # قسم إدارة المستخدمين
        users_section = self.create_settings_section(
            scrollable_frame,
            "👥 إدارة المستخدمين",
            "إضافة وتعديل وحذف المستخدمين"
        )

        # أزرار إدارة المستخدمين
        users_buttons_frame = ctk.CTkFrame(users_section, fg_color="transparent")
        users_buttons_frame.pack(fill="x", pady=10)

        # زر إضافة مستخدم
        add_user_btn = ctk.CTkButton(
            users_buttons_frame,
            text="➕ إضافة مستخدم",
            width=150,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['success'],
            hover_color=self.get_hover_color('success'),
            corner_radius=self.dimensions['border_radius'],
            command=self.add_new_user
        )
        add_user_btn.pack(side="right", padx=(0, 10))

        # زر تعديل مستخدم
        edit_user_btn = ctk.CTkButton(
            users_buttons_frame,
            text="✏️ تعديل مستخدم",
            width=150,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['info'],
            hover_color=self.get_hover_color('info'),
            corner_radius=self.dimensions['border_radius'],
            command=self.edit_user
        )
        edit_user_btn.pack(side="right", padx=(0, 10))

        # زر حذف مستخدم
        delete_user_btn = ctk.CTkButton(
            users_buttons_frame,
            text="🗑️ حذف مستخدم",
            width=150,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['error'],
            hover_color=self.get_hover_color('error'),
            corner_radius=self.dimensions['border_radius'],
            command=self.delete_user
        )
        delete_user_btn.pack(side="left")

        # جدول المستخدمين
        self.create_users_table(users_section)

        # قسم الأدوار والصلاحيات
        roles_section = self.create_settings_section(
            scrollable_frame,
            "🔐 الأدوار والصلاحيات",
            "تحديد صلاحيات كل دور"
        )

        # إنشاء إدارة الأدوار
        self.create_roles_management(roles_section)

        # قسم إعدادات الأمان
        security_section = self.create_settings_section(
            scrollable_frame,
            "🛡️ إعدادات الأمان",
            "إعدادات كلمات المرور والجلسات"
        )

        # الحد الأدنى لطول كلمة المرور
        self.create_number_field(
            security_section,
            "الحد الأدنى لطول كلمة المرور:",
            "min_password_length",
            self.settings.get('min_password_length', 6),
            min_value=4,
            max_value=20,
            step=1
        )

        # عدد محاولات تسجيل الدخول
        self.create_number_field(
            security_section,
            "عدد محاولات تسجيل الدخول المسموحة:",
            "max_login_attempts",
            self.settings.get('max_login_attempts', 3),
            min_value=3,
            max_value=10,
            step=1
        )

        # مدة انتهاء الجلسة
        self.create_number_field(
            security_section,
            "مدة انتهاء الجلسة (دقائق):",
            "session_timeout",
            self.settings.get('session_timeout', 60),
            min_value=15,
            max_value=480,
            step=15
        )

        # تفعيل المصادقة الثنائية
        self.create_checkbox_field(
            security_section,
            "تفعيل المصادقة الثنائية (2FA)",
            "enable_2fa",
            self.settings.get('enable_2fa', False)
        )

        # تسجيل محاولات تسجيل الدخول
        self.create_checkbox_field(
            security_section,
            "تسجيل محاولات تسجيل الدخول",
            "log_login_attempts",
            self.settings.get('log_login_attempts', True)
        )

        # إجبار تغيير كلمة المرور
        self.create_checkbox_field(
            security_section,
            "إجبار المستخدمين على تغيير كلمة المرور دورياً",
            "force_password_change",
            self.settings.get('force_password_change', False)
        )

        # أزرار الحفظ
        self.create_action_buttons(scrollable_frame)

    def create_users_table(self, parent):
        """إنشاء جدول المستخدمين"""
        # إطار الجدول
        table_frame = self.create_elevated_frame(
            parent,
            elevation=1,
            corner_radius=self.dimensions['border_radius']
        )
        table_frame.pack(fill="both", expand=True, pady=10)

        # رأس الجدول
        header_frame = ctk.CTkFrame(
            table_frame,
            fg_color=self.colors['primary'],
            corner_radius=self.dimensions['border_radius'],
            height=40
        )
        header_frame.pack(fill="x", padx=5, pady=5)
        header_frame.pack_propagate(False)

        # عناوين الأعمدة
        headers = ["اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", "الدور", "الحالة", "آخر دخول"]

        for i, header in enumerate(headers):
            header_label = ctk.CTkLabel(
                header_frame,
                text=header,
                font=self.fonts['body'],
                text_color=self.colors['text_on_primary']
            )
            header_label.grid(row=0, column=i, sticky="ew", padx=5, pady=8)
            header_frame.grid_columnconfigure(i, weight=1)

        # محتوى الجدول
        content_frame = ctk.CTkScrollableFrame(
            table_frame,
            fg_color="transparent",
            height=200
        )
        content_frame.pack(fill="both", expand=True, padx=5, pady=(0, 5))

        # بيانات المستخدمين
        users_data = self.get_users_data()

        for i, user in enumerate(users_data):
            row_frame = ctk.CTkFrame(
                content_frame,
                fg_color=self.colors['surface_variant'] if i % 2 == 0 else self.colors['surface'],
                corner_radius=5
            )
            row_frame.pack(fill="x", pady=2)

            # البيانات
            data = [user['username'], user['full_name'], user['email'],
                   user['role'], user['status'], user['last_login']]

            for j, value in enumerate(data):
                # تحديد لون النص حسب الحالة
                if j == 4:  # عمود الحالة
                    text_color = self.colors['success'] if value == 'نشط' else self.colors['error']
                else:
                    text_color = self.colors['text_primary']

                label = ctk.CTkLabel(
                    row_frame,
                    text=value,
                    font=self.fonts['caption'],
                    text_color=text_color
                )
                label.grid(row=0, column=j, sticky="ew", padx=5, pady=5)
                row_frame.grid_columnconfigure(j, weight=1)

    def create_roles_management(self, parent):
        """إنشاء إدارة الأدوار والصلاحيات"""
        # إطار الأدوار
        roles_frame = self.create_elevated_frame(
            parent,
            elevation=1,
            corner_radius=self.dimensions['border_radius']
        )
        roles_frame.pack(fill="x", pady=10)

        # الأدوار المتاحة
        roles_data = {
            "مدير": {
                "permissions": ["عرض", "إضافة", "تعديل", "حذف", "إدارة المستخدمين", "النسخ الاحتياطي", "التقارير"],
                "color": self.colors['error']
            },
            "محاسب": {
                "permissions": ["عرض", "إضافة", "تعديل", "التقارير"],
                "color": self.colors['primary']
            },
            "مستخدم": {
                "permissions": ["عرض"],
                "color": self.colors['info']
            }
        }

        for role_name, role_info in roles_data.items():
            # إطار الدور
            role_frame = ctk.CTkFrame(roles_frame, fg_color="transparent")
            role_frame.pack(fill="x", padx=15, pady=10)

            # عنوان الدور
            role_header = ctk.CTkFrame(
                role_frame,
                fg_color=role_info['color'],
                corner_radius=8,
                height=40
            )
            role_header.pack(fill="x", pady=(0, 5))
            role_header.pack_propagate(False)

            role_title = ctk.CTkLabel(
                role_header,
                text=f"🔐 {role_name}",
                font=self.fonts['heading'],
                text_color=self.colors['text_on_primary']
            )
            role_title.pack(expand=True, pady=10)

            # الصلاحيات
            permissions_frame = ctk.CTkFrame(role_frame, fg_color=self.colors['surface_variant'])
            permissions_frame.pack(fill="x", padx=10, pady=5)

            permissions_text = " • ".join(role_info['permissions'])
            permissions_label = ctk.CTkLabel(
                permissions_frame,
                text=permissions_text,
                font=self.fonts['caption'],
                text_color=self.colors['text_secondary'],
                wraplength=600
            )
            permissions_label.pack(padx=15, pady=10)

    def get_users_data(self):
        """الحصول على بيانات المستخدمين"""
        # بيانات وهمية - في التطبيق الحقيقي ستأتي من قاعدة البيانات
        return [
            {
                'id': 1,
                'username': 'admin',
                'full_name': 'المدير العام',
                'email': '<EMAIL>',
                'role': 'مدير',
                'status': 'نشط',
                'last_login': '2024-01-15 10:30'
            },
            {
                'id': 2,
                'username': 'accountant1',
                'full_name': 'أحمد محمد',
                'email': '<EMAIL>',
                'role': 'محاسب',
                'status': 'نشط',
                'last_login': '2024-01-15 09:15'
            },
            {
                'id': 3,
                'username': 'user1',
                'full_name': 'فاطمة علي',
                'email': '<EMAIL>',
                'role': 'مستخدم',
                'status': 'غير نشط',
                'last_login': '2024-01-10 14:20'
            }
        ]

    def add_new_user(self):
        """إضافة مستخدم جديد"""
        self.show_user_dialog("إضافة مستخدم جديد")

    def edit_user(self):
        """تعديل مستخدم"""
        self.show_user_dialog("تعديل المستخدم", edit_mode=True)

    def delete_user(self):
        """حذف مستخدم"""
        if messagebox.askyesno(
            "تأكيد الحذف",
            "هل تريد حذف المستخدم المحدد؟\n\nلا يمكن التراجع عن هذا الإجراء!"
        ):
            self.show_toast("🗑️ تم حذف المستخدم بنجاح", "success")

    def show_user_dialog(self, title, edit_mode=False):
        """عرض نافذة إضافة/تعديل المستخدم"""
        dialog = ctk.CTkToplevel(self.window)
        dialog.title(title)
        dialog.geometry("500x600")
        dialog.configure(fg_color=self.colors['background'])

        # جعل النافذة في المقدمة
        dialog.transient(self.window)
        dialog.grab_set()

        # المحتوى
        content_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # العنوان
        title_label = ctk.CTkLabel(
            content_frame,
            text=title,
            font=self.fonts['subtitle'],
            text_color=self.colors['text_primary']
        )
        title_label.pack(pady=(0, 20))

        # الحقول
        fields_frame = ctk.CTkFrame(content_frame, fg_color=self.colors['surface'])
        fields_frame.pack(fill="both", expand=True, pady=(0, 20))

        # متغيرات الحقول
        username_var = ctk.StringVar()
        fullname_var = ctk.StringVar()
        email_var = ctk.StringVar()
        role_var = ctk.StringVar(value="مستخدم")
        password_var = ctk.StringVar()

        # إنشاء الحقول
        self.create_dialog_field(fields_frame, "اسم المستخدم:", username_var)
        self.create_dialog_field(fields_frame, "الاسم الكامل:", fullname_var)
        self.create_dialog_field(fields_frame, "البريد الإلكتروني:", email_var)

        # الدور
        role_frame = ctk.CTkFrame(fields_frame, fg_color="transparent")
        role_frame.pack(fill="x", padx=20, pady=10)

        role_label = ctk.CTkLabel(role_frame, text="الدور:", font=self.fonts['body'])
        role_label.pack(anchor="e", pady=5)

        role_combo = ctk.CTkComboBox(
            role_frame,
            variable=role_var,
            values=["مدير", "محاسب", "مستخدم"],
            font=self.fonts['body']
        )
        role_combo.pack(fill="x", pady=5)

        # كلمة المرور
        if not edit_mode:
            self.create_dialog_field(fields_frame, "كلمة المرور:", password_var, show="*")

        # الأزرار
        buttons_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        buttons_frame.pack(fill="x")

        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            font=self.fonts['button'],
            fg_color=self.colors['success'],
            hover_color=self.get_hover_color('success'),
            command=lambda: self.save_user_data(dialog, {
                'username': username_var.get(),
                'fullname': fullname_var.get(),
                'email': email_var.get(),
                'role': role_var.get(),
                'password': password_var.get()
            })
        )
        save_btn.pack(side="left", padx=(0, 10))

        # زر الإلغاء
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            font=self.fonts['button'],
            fg_color=self.colors['error'],
            hover_color=self.get_hover_color('error'),
            command=dialog.destroy
        )
        cancel_btn.pack(side="left")

    def create_dialog_field(self, parent, label_text, variable, show=None):
        """إنشاء حقل في النافذة المنبثقة"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", padx=20, pady=10)

        label = ctk.CTkLabel(field_frame, text=label_text, font=self.fonts['body'])
        label.pack(anchor="e", pady=5)

        entry = ctk.CTkEntry(
            field_frame,
            textvariable=variable,
            font=self.fonts['body'],
            show=show
        )
        entry.pack(fill="x", pady=5)

        return entry

    def save_user_data(self, dialog, user_data):
        """حفظ بيانات المستخدم"""
        # التحقق من صحة البيانات
        if not all([user_data['username'], user_data['fullname'], user_data['email']]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        # حفظ البيانات (محاكاة)
        self.show_toast("✅ تم حفظ بيانات المستخدم بنجاح", "success")
        if dialog and hasattr(dialog, "destroy"):

            dialog.destroy()

    def show_toast(self, message, toast_type="info", duration=3000):
        """عرض إشعار Toast"""
        # ألوان الإشعارات
        toast_colors = {
            'info': self.colors['info'],
            'success': self.colors['success'],
            'warning': self.colors['warning'],
            'error': self.colors['error']
        }

        # أيقونات الإشعارات
        toast_icons = {
            'info': 'ℹ️',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌'
        }

        # إنشاء نافذة الإشعار
        toast = ctk.CTkToplevel(self.window)
        toast.title("")
        toast.geometry("400x80")
        toast.configure(fg_color=toast_colors.get(toast_type, self.colors['info']))

        # إزالة شريط العنوان
        toast.overrideredirect(True)

        # موضع الإشعار (أعلى يمين الشاشة)
        screen_width = toast.winfo_screenwidth()
        toast.geometry(f"400x80+{screen_width-420}+50")

        # جعل النافذة في المقدمة
        toast.attributes('-topmost', True)

        # المحتوى
        content_frame = ctk.CTkFrame(toast, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # الأيقونة والرسالة
        message_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        message_frame.pack(expand=True, fill="both")

        # الأيقونة
        icon_label = ctk.CTkLabel(
            message_frame,
            text=toast_icons.get(toast_type, 'ℹ️'),
            font=("Arial", 20),
            text_color=self.colors['text_on_primary']
        )
        icon_label.pack(side="right", padx=(0, 10))

        # الرسالة
        message_label = ctk.CTkLabel(
            message_frame,
            text=message,
            font=self.fonts['body'],
            text_color=self.colors['text_on_primary'],
            wraplength=300
        )
        message_label.pack(side="right", expand=True, fill="both")

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            message_frame,
            text="✕",
            width=30,
            height=30,
            font=self.fonts['caption'],
            fg_color="transparent",
            hover_color=self.colors['error'],
            text_color=self.colors['text_on_primary'],
            command=toast.destroy
        )
        close_btn.pack(side="left", padx=(10, 0))

        # إغلاق تلقائي
        toast.after(duration, toast.destroy)

        # تأثير الظهور
        toast.attributes('-alpha', 0.0)
        self.fade_in_toast(toast)

    def fade_in_toast(self, toast):
        """تأثير ظهور الإشعار"""
        try:
            alpha = toast.attributes('-alpha')
            if alpha < 1.0:
                alpha += 0.1
                toast.attributes('-alpha', alpha)
                toast.after(50, lambda: self.fade_in_toast(toast))
        except:
            pass  # النافذة مغلقة

    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            settings_file = PROJECT_ROOT / "config" / "admin_settings.json"
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {str(e)}")

        # إعدادات افتراضية شاملة
        return {
            # معلومات الشركة
            'company_name': 'شركة ست الكل للمحاسبة',
            'commercial_register': '',
            'phone': '',
            'email': '',
            'address': '',
            'company_logo': '',

            # الإعدادات المالية
            'default_language': 'العربية',
            'main_currency': 'ريال سعودي',
            'currency_symbol': 'ر.س',
            'tax_rate': 15.0,
            'default_discount': 0.0,

            # إعدادات الفواتير
            'invoice_template': 'حديث',
            'paper_size': 'A4',
            'show_logo_in_invoice': True,
            'auto_print_invoice': False,

            # إعدادات النسخ الاحتياطي
            'backup_directory': str(PROJECT_ROOT / "backups"),
            'max_backups': 30,
            'auto_backup_enabled': True,
            'backup_interval': 'يومياً',
            'backup_time': '02:00',
            'compress_backups': True,
            'encrypt_backups': False,

            # إعدادات الأمان
            'min_password_length': 6,
            'max_login_attempts': 3,
            'session_timeout': 60,
            'enable_2fa': False,
            'log_login_attempts': True,
            'force_password_change': False,

            # إعدادات النظام
            'database_type': 'SQLite',
            'database_path': str(PROJECT_ROOT / "data" / "accounting.db"),
            'enable_logging': True,
            'log_level': 'INFO',
            'max_log_size': 10,
            'backup_logs': True,

            # إعدادات الشبكة
            'enable_network': False,
            'server_address': 'localhost',
            'server_port': 8080,
            'connection_timeout': 30,
            'enable_ssl': False,

            # إعدادات التقارير
            'default_report_format': 'PDF',
            'report_paper_size': 'A4',
            'report_orientation': 'عمودي',
            'include_logo_in_reports': True,
            'watermark_reports': False,

            # إعدادات الواجهة
            'theme': 'فاتح',
            'font_size': 14,
            'enable_animations': True,
            'show_tooltips': True,
            'auto_save': True,
            'auto_save_interval': 5
        }

    def create_data_control_tab(self, tab, config):
        """إنشاء تبويب التحكم بالبيانات"""
        # إنشاء حاوي قابل للتمرير
        scrollable_frame = ctk.CTkScrollableFrame(
            tab,
            fg_color="transparent",
            corner_radius=0
        )
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        self.create_tab_header(scrollable_frame, config)

        # قسم ضبط المصنع
        factory_reset_section = self.create_settings_section(
            scrollable_frame,
            "🏭 ضبط المصنع",
            "إعادة تعيين البرنامج إلى حالته الأصلية"
        )

        # تحذير ضبط المصنع
        warning_frame = ctk.CTkFrame(
            factory_reset_section,
            fg_color=self.colors['error'],
            corner_radius=self.dimensions['border_radius']
        )
        warning_frame.pack(fill="x", pady=10)

        warning_label = ctk.CTkLabel(
            warning_frame,
            text="⚠️ تحذير: ضبط المصنع سيحذف جميع البيانات نهائياً!",
            font=self.fonts['heading'],
            text_color=self.colors['text_on_primary']
        )
        warning_label.pack(pady=15)

        # أزرار ضبط المصنع
        factory_buttons_frame = ctk.CTkFrame(factory_reset_section, fg_color="transparent")
        factory_buttons_frame.pack(fill="x", pady=10)

        # زر ضبط المصنع الكامل
        full_reset_btn = ctk.CTkButton(
            factory_buttons_frame,
            text="🏭 ضبط المصنع الكامل",
            width=200,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['error'],
            hover_color=self.get_hover_color('error'),
            corner_radius=self.dimensions['border_radius'],
            command=self.full_factory_reset
        )
        full_reset_btn.pack(side="right", padx=(0, 10))

        # زر إعادة تعيين الإعدادات فقط
        settings_reset_btn = ctk.CTkButton(
            factory_buttons_frame,
            text="⚙️ إعادة تعيين الإعدادات",
            width=200,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['warning'],
            hover_color=self.get_hover_color('warning'),
            corner_radius=self.dimensions['border_radius'],
            command=self.reset_settings_only
        )
        settings_reset_btn.pack(side="left")

        # قسم تنظيف البيانات
        cleanup_section = self.create_settings_section(
            scrollable_frame,
            "🧹 تنظيف البيانات",
            "تنظيف وتحسين قاعدة البيانات"
        )

        # أزرار التنظيف
        cleanup_buttons_frame = ctk.CTkFrame(cleanup_section, fg_color="transparent")
        cleanup_buttons_frame.pack(fill="x", pady=10)

        # زر تنظيف البيانات المؤقتة
        temp_cleanup_btn = ctk.CTkButton(
            cleanup_buttons_frame,
            text="🗑️ تنظيف البيانات المؤقتة",
            width=200,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['info'],
            hover_color=self.get_hover_color('info'),
            corner_radius=self.dimensions['border_radius'],
            command=self.cleanup_temp_data
        )
        temp_cleanup_btn.pack(side="right", padx=(0, 10))

        # زر تحسين قاعدة البيانات
        optimize_db_btn = ctk.CTkButton(
            cleanup_buttons_frame,
            text="⚡ تحسين قاعدة البيانات",
            width=200,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['success'],
            hover_color=self.get_hover_color('success'),
            corner_radius=self.dimensions['border_radius'],
            command=self.optimize_database
        )
        optimize_db_btn.pack(side="right", padx=(0, 10))

        # زر إعادة بناء الفهارس
        rebuild_indexes_btn = ctk.CTkButton(
            cleanup_buttons_frame,
            text="🔧 إعادة بناء الفهارس",
            width=200,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['secondary'],
            hover_color=self.get_hover_color('secondary'),
            corner_radius=self.dimensions['border_radius'],
            command=self.rebuild_indexes
        )
        rebuild_indexes_btn.pack(side="left")

        # قسم إحصائيات قاعدة البيانات
        stats_section = self.create_settings_section(
            scrollable_frame,
            "📊 إحصائيات قاعدة البيانات",
            "معلومات حول حجم واستخدام البيانات"
        )

        # إنشاء إحصائيات قاعدة البيانات
        self.create_database_stats(stats_section)

        # أزرار الحفظ
        self.create_action_buttons(scrollable_frame)

    def create_database_stats(self, parent):
        """إنشاء إحصائيات قاعدة البيانات"""
        stats_frame = self.create_elevated_frame(
            parent,
            elevation=1,
            corner_radius=self.dimensions['border_radius']
        )
        stats_frame.pack(fill="x", pady=10)

        # بيانات الإحصائيات (وهمية)
        stats_data = {
            "حجم قاعدة البيانات": "45.2 MB",
            "عدد الجداول": "25",
            "عدد السجلات": "12,450",
            "آخر تحسين": "2024-01-10 15:30",
            "مساحة فارغة": "2.1 MB",
            "عدد الفهارس": "18"
        }

        # إنشاء شبكة الإحصائيات
        for i, (key, value) in enumerate(stats_data.items()):
            row = i // 2
            col = i % 2

            stat_frame = ctk.CTkFrame(
                stats_frame,
                fg_color=self.colors['surface_variant'],
                corner_radius=8
            )
            stat_frame.grid(row=row, column=col, sticky="ew", padx=10, pady=5)

            # المفتاح
            key_label = ctk.CTkLabel(
                stat_frame,
                text=key,
                font=self.fonts['caption'],
                text_color=self.colors['text_secondary']
            )
            key_label.pack(pady=(10, 0))

            # القيمة
            value_label = ctk.CTkLabel(
                stat_frame,
                text=value,
                font=self.fonts['heading'],
                text_color=self.colors['text_primary']
            )
            value_label.pack(pady=(0, 10))

            stats_frame.grid_columnconfigure(col, weight=1)

    def full_factory_reset(self):
        """ضبط المصنع الكامل"""
        if messagebox.askyesno(
            "تأكيد ضبط المصنع",
            "هل تريد إجراء ضبط مصنع كامل؟\n\n"
            "سيتم حذف:\n"
            "• جميع البيانات المالية\n"
            "• جميع الإعدادات\n"
            "• جميع المستخدمين\n"
            "• جميع النسخ الاحتياطية\n\n"
            "هذا الإجراء لا يمكن التراجع عنه!"
        ):
            if messagebox.askyesno(
                "تأكيد نهائي",
                "هذا هو التأكيد الأخير!\n\n"
                "هل أنت متأكد من رغبتك في حذف جميع البيانات؟"
            ):
                self.show_toast("🏭 جاري إجراء ضبط المصنع الكامل...", "warning")
                # محاكاة عملية ضبط المصنع
                self.window.after(3000, lambda: self.show_toast("✅ تم ضبط المصنع بنجاح", "success"))

    def reset_settings_only(self):
        """إعادة تعيين الإعدادات فقط"""
        if messagebox.askyesno(
            "تأكيد إعادة التعيين",
            "هل تريد إعادة تعيين الإعدادات إلى القيم الافتراضية؟\n\n"
            "سيتم الاحتفاظ بجميع البيانات المالية."
        ):
            self.show_toast("⚙️ جاري إعادة تعيين الإعدادات...", "info")
            # محاكاة عملية إعادة التعيين
            self.window.after(2000, lambda: self.show_toast("✅ تم إعادة تعيين الإعدادات بنجاح", "success"))

    def cleanup_temp_data(self):
        """تنظيف البيانات المؤقتة"""
        self.show_toast("🗑️ جاري تنظيف البيانات المؤقتة...", "info")
        # محاكاة عملية التنظيف
        self.window.after(1500, lambda: self.show_toast("✅ تم تنظيف البيانات المؤقتة (2.3 MB محررة)", "success"))

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        self.show_toast("⚡ جاري تحسين قاعدة البيانات...", "info")
        # محاكاة عملية التحسين
        self.window.after(2500, lambda: self.show_toast("✅ تم تحسين قاعدة البيانات بنجاح", "success"))

    def rebuild_indexes(self):
        """إعادة بناء الفهارس"""
        self.show_toast("🔧 جاري إعادة بناء الفهارس...", "info")
        # محاكاة عملية إعادة البناء
        self.window.after(2000, lambda: self.show_toast("✅ تم إعادة بناء الفهارس بنجاح", "success"))

    def create_import_export_tab(self, tab, config):
        """إنشاء تبويب استيراد وتصدير"""
        # إنشاء حاوي قابل للتمرير
        scrollable_frame = ctk.CTkScrollableFrame(
            tab,
            fg_color="transparent",
            corner_radius=0
        )
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        self.create_tab_header(scrollable_frame, config)

        # قسم الاستيراد
        import_section = self.create_settings_section(
            scrollable_frame,
            "📥 استيراد البيانات",
            "استيراد البيانات من ملفات Excel و CSV"
        )

        # أزرار الاستيراد
        import_buttons_frame = ctk.CTkFrame(import_section, fg_color="transparent")
        import_buttons_frame.pack(fill="x", pady=10)

        # زر استيراد العملاء
        import_customers_btn = ctk.CTkButton(
            import_buttons_frame,
            text="👥 استيراد العملاء",
            width=180,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['primary'],
            hover_color=self.get_hover_color('primary'),
            corner_radius=self.dimensions['border_radius'],
            command=lambda: self.import_data('customers')
        )
        import_customers_btn.pack(side="right", padx=(0, 10))

        # زر استيراد المنتجات
        import_products_btn = ctk.CTkButton(
            import_buttons_frame,
            text="📦 استيراد المنتجات",
            width=180,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['success'],
            hover_color=self.get_hover_color('success'),
            corner_radius=self.dimensions['border_radius'],
            command=lambda: self.import_data('products')
        )
        import_products_btn.pack(side="right", padx=(0, 10))

        # زر استيراد الموردين
        import_suppliers_btn = ctk.CTkButton(
            import_buttons_frame,
            text="🏪 استيراد الموردين",
            width=180,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['info'],
            hover_color=self.get_hover_color('info'),
            corner_radius=self.dimensions['border_radius'],
            command=lambda: self.import_data('suppliers')
        )
        import_suppliers_btn.pack(side="left")

        # قسم التصدير
        export_section = self.create_settings_section(
            scrollable_frame,
            "📤 تصدير البيانات",
            "تصدير التقارير والبيانات إلى ملفات مختلفة"
        )

        # أزرار التصدير
        export_buttons_frame = ctk.CTkFrame(export_section, fg_color="transparent")
        export_buttons_frame.pack(fill="x", pady=10)

        # زر تصدير التقارير المالية
        export_financial_btn = ctk.CTkButton(
            export_buttons_frame,
            text="💰 تصدير التقارير المالية",
            width=200,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['warning'],
            hover_color=self.get_hover_color('warning'),
            corner_radius=self.dimensions['border_radius'],
            command=lambda: self.export_data('financial_reports')
        )
        export_financial_btn.pack(side="right", padx=(0, 10))

        # زر تصدير قائمة العملاء
        export_customers_btn = ctk.CTkButton(
            export_buttons_frame,
            text="👥 تصدير قائمة العملاء",
            width=200,
            height=50,
            font=self.fonts['button'],
            fg_color=self.colors['secondary'],
            hover_color=self.get_hover_color('secondary'),
            corner_radius=self.dimensions['border_radius'],
            command=lambda: self.export_data('customers')
        )
        export_customers_btn.pack(side="left")

        # قسم إعدادات الاستيراد والتصدير
        settings_section = self.create_settings_section(
            scrollable_frame,
            "⚙️ إعدادات الاستيراد والتصدير",
            "تكوين خيارات الاستيراد والتصدير"
        )

        # تنسيق التصدير الافتراضي
        self.create_dropdown_field(
            settings_section,
            "تنسيق التصدير الافتراضي:",
            "default_export_format",
            ["Excel (.xlsx)", "CSV (.csv)", "PDF (.pdf)"],
            self.settings.get('default_export_format', 'Excel (.xlsx)')
        )

        # ترميز الملفات
        self.create_dropdown_field(
            settings_section,
            "ترميز الملفات:",
            "file_encoding",
            ["UTF-8", "Windows-1256", "ISO-8859-6"],
            self.settings.get('file_encoding', 'UTF-8')
        )

        # تضمين العناوين
        self.create_checkbox_field(
            settings_section,
            "تضمين عناوين الأعمدة عند التصدير",
            "include_headers",
            self.settings.get('include_headers', True)
        )

        # التحقق من البيانات عند الاستيراد
        self.create_checkbox_field(
            settings_section,
            "التحقق من صحة البيانات عند الاستيراد",
            "validate_import_data",
            self.settings.get('validate_import_data', True)
        )

        # أزرار الحفظ
        self.create_action_buttons(scrollable_frame)

    def import_data(self, data_type):
        """استيراد البيانات"""
        file_types = [
            ("ملفات Excel", "*.xlsx *.xls"),
            ("ملفات CSV", "*.csv"),
            ("جميع الملفات", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title=f"اختيار ملف لاستيراد {data_type}",
            filetypes=file_types
        )

        if filename:
            self.show_toast(f"📥 جاري استيراد {data_type}...", "info")
            # محاكاة عملية الاستيراد
            self.window.after(2000, lambda: self.show_toast(f"✅ تم استيراد {data_type} بنجاح (150 سجل)", "success"))

    def export_data(self, data_type):
        """تصدير البيانات"""
        filename = filedialog.asksaveasfilename(
            title=f"حفظ تصدير {data_type}",
            defaultextension=".xlsx",
            filetypes=[
                ("ملفات Excel", "*.xlsx"),
                ("ملفات CSV", "*.csv"),
                ("ملفات PDF", "*.pdf")
            ]
        )

        if filename:
            self.show_toast(f"📤 جاري تصدير {data_type}...", "info")
            # محاكاة عملية التصدير
            self.window.after(1500, lambda: self.show_toast(f"✅ تم تصدير {data_type} بنجاح", "success"))

    def create_system_settings_tab(self, tab, config):
        """إنشاء تبويب إعدادات النظام"""
        # إنشاء حاوي قابل للتمرير
        scrollable_frame = ctk.CTkScrollableFrame(
            tab,
            fg_color="transparent",
            corner_radius=0
        )
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        self.create_tab_header(scrollable_frame, config)

        # قسم قاعدة البيانات
        database_section = self.create_settings_section(
            scrollable_frame,
            "🗄️ إعدادات قاعدة البيانات",
            "تكوين الاتصال بقاعدة البيانات"
        )

        # نوع قاعدة البيانات
        self.create_dropdown_field(
            database_section,
            "نوع قاعدة البيانات:",
            "database_type",
            ["SQLite", "PostgreSQL", "MySQL", "SQL Server"],
            self.settings.get('database_type', 'SQLite')
        )

        # مسار قاعدة البيانات
        self.create_file_field(
            database_section,
            "مسار قاعدة البيانات:",
            "database_path",
            self.settings.get('database_path', ''),
            file_types=[("ملفات قاعدة البيانات", "*.db *.sqlite"), ("جميع الملفات", "*.*")]
        )

        # قسم السجلات
        logging_section = self.create_settings_section(
            scrollable_frame,
            "📝 إعدادات السجلات",
            "تكوين تسجيل العمليات والأخطاء"
        )

        # تفعيل التسجيل
        self.create_checkbox_field(
            logging_section,
            "تفعيل تسجيل العمليات",
            "enable_logging",
            self.settings.get('enable_logging', True)
        )

        # مستوى التسجيل
        self.create_dropdown_field(
            logging_section,
            "مستوى التسجيل:",
            "log_level",
            ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
            self.settings.get('log_level', 'INFO')
        )

        # حجم ملف السجل الأقصى
        self.create_number_field(
            logging_section,
            "حجم ملف السجل الأقصى (MB):",
            "max_log_size",
            self.settings.get('max_log_size', 10),
            min_value=1,
            max_value=100,
            step=1
        )

        # نسخ احتياطي للسجلات
        self.create_checkbox_field(
            logging_section,
            "إنشاء نسخ احتياطية للسجلات",
            "backup_logs",
            self.settings.get('backup_logs', True)
        )

        # قسم الأداء
        performance_section = self.create_settings_section(
            scrollable_frame,
            "⚡ إعدادات الأداء",
            "تحسين أداء البرنامج"
        )

        # تفعيل التخزين المؤقت
        self.create_checkbox_field(
            performance_section,
            "تفعيل التخزين المؤقت",
            "enable_caching",
            self.settings.get('enable_caching', True)
        )

        # حجم التخزين المؤقت
        self.create_number_field(
            performance_section,
            "حجم التخزين المؤقت (MB):",
            "cache_size",
            self.settings.get('cache_size', 50),
            min_value=10,
            max_value=500,
            step=10
        )

        # عدد الخيوط
        self.create_number_field(
            performance_section,
            "عدد خيوط المعالجة:",
            "thread_count",
            self.settings.get('thread_count', 4),
            min_value=1,
            max_value=16,
            step=1
        )

        # قسم الشبكة
        network_section = self.create_settings_section(
            scrollable_frame,
            "🌐 إعدادات الشبكة",
            "تكوين الاتصال بالشبكة"
        )

        # تفعيل الشبكة
        self.create_checkbox_field(
            network_section,
            "تفعيل الاتصال بالشبكة",
            "enable_network",
            self.settings.get('enable_network', False)
        )

        # عنوان الخادم
        self.create_input_field(
            network_section,
            "عنوان الخادم:",
            "server_address",
            self.settings.get('server_address', 'localhost')
        )

        # منفذ الاتصال
        self.create_number_field(
            network_section,
            "منفذ الاتصال:",
            "server_port",
            self.settings.get('server_port', 8080),
            min_value=1000,
            max_value=65535,
            step=1
        )

        # مهلة الاتصال
        self.create_number_field(
            network_section,
            "مهلة الاتصال (ثواني):",
            "connection_timeout",
            self.settings.get('connection_timeout', 30),
            min_value=5,
            max_value=300,
            step=5
        )

        # تفعيل SSL
        self.create_checkbox_field(
            network_section,
            "تفعيل التشفير SSL",
            "enable_ssl",
            self.settings.get('enable_ssl', False)
        )

        # أزرار الحفظ
        self.create_action_buttons(scrollable_frame)

    def create_security_tab(self, tab, config):
        """إنشاء تبويب الأمان والحماية"""
        # إنشاء حاوي قابل للتمرير
        scrollable_frame = ctk.CTkScrollableFrame(
            tab,
            fg_color="transparent",
            corner_radius=0
        )
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        self.create_tab_header(scrollable_frame, config)

        # قسم مراقبة الأمان
        monitoring_section = self.create_settings_section(
            scrollable_frame,
            "👁️ مراقبة الأمان",
            "مراقبة النشاط والتهديدات الأمنية"
        )

        # أزرار المراقبة
        monitoring_buttons_frame = ctk.CTkFrame(monitoring_section, fg_color="transparent")
        monitoring_buttons_frame.pack(fill="x", pady=10)

        # زر عرض سجل الأمان
        security_log_btn = ctk.CTkButton(
            monitoring_buttons_frame,
            text="📋 سجل الأمان",
            width=150,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['info'],
            hover_color=self.get_hover_color('info'),
            corner_radius=self.dimensions['border_radius'],
            command=self.show_security_log
        )
        security_log_btn.pack(side="right", padx=(0, 10))

        # زر فحص الأمان
        security_scan_btn = ctk.CTkButton(
            monitoring_buttons_frame,
            text="🔍 فحص أمني",
            width=150,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['warning'],
            hover_color=self.get_hover_color('warning'),
            corner_radius=self.dimensions['border_radius'],
            command=self.run_security_scan
        )
        security_scan_btn.pack(side="right", padx=(0, 10))

        # زر تقرير الأمان
        security_report_btn = ctk.CTkButton(
            monitoring_buttons_frame,
            text="📊 تقرير الأمان",
            width=150,
            height=40,
            font=self.fonts['button'],
            fg_color=self.colors['secondary'],
            hover_color=self.get_hover_color('secondary'),
            corner_radius=self.dimensions['border_radius'],
            command=self.generate_security_report
        )
        security_report_btn.pack(side="left")

        # قسم التشفير
        encryption_section = self.create_settings_section(
            scrollable_frame,
            "🔐 إعدادات التشفير",
            "تكوين التشفير وحماية البيانات"
        )

        # تفعيل تشفير قاعدة البيانات
        self.create_checkbox_field(
            encryption_section,
            "تشفير قاعدة البيانات",
            "encrypt_database",
            self.settings.get('encrypt_database', False)
        )

        # تشفير النسخ الاحتياطية
        self.create_checkbox_field(
            encryption_section,
            "تشفير النسخ الاحتياطية",
            "encrypt_backups",
            self.settings.get('encrypt_backups', False)
        )

        # تشفير الملفات المؤقتة
        self.create_checkbox_field(
            encryption_section,
            "تشفير الملفات المؤقتة",
            "encrypt_temp_files",
            self.settings.get('encrypt_temp_files', False)
        )

        # نوع التشفير
        self.create_dropdown_field(
            encryption_section,
            "نوع التشفير:",
            "encryption_type",
            ["AES-256", "AES-128", "3DES"],
            self.settings.get('encryption_type', 'AES-256')
        )

        # قسم التحكم في الوصول
        access_control_section = self.create_settings_section(
            scrollable_frame,
            "🚪 التحكم في الوصول",
            "إدارة صلاحيات الوصول والمصادقة"
        )

        # تفعيل المصادقة الثنائية
        self.create_checkbox_field(
            access_control_section,
            "المصادقة الثنائية (2FA)",
            "enable_2fa",
            self.settings.get('enable_2fa', False)
        )

        # تسجيل محاولات الدخول الفاشلة
        self.create_checkbox_field(
            access_control_section,
            "تسجيل محاولات الدخول الفاشلة",
            "log_failed_logins",
            self.settings.get('log_failed_logins', True)
        )

        # حظر IP بعد محاولات فاشلة
        self.create_checkbox_field(
            access_control_section,
            "حظر عنوان IP بعد محاولات فاشلة",
            "block_ip_after_failures",
            self.settings.get('block_ip_after_failures', True)
        )

        # عدد المحاولات قبل الحظر
        self.create_number_field(
            access_control_section,
            "عدد المحاولات قبل الحظر:",
            "max_failed_attempts",
            self.settings.get('max_failed_attempts', 5),
            min_value=3,
            max_value=20,
            step=1
        )

        # مدة الحظر (دقائق)
        self.create_number_field(
            access_control_section,
            "مدة الحظر (دقائق):",
            "block_duration",
            self.settings.get('block_duration', 30),
            min_value=5,
            max_value=1440,
            step=5
        )

        # قسم التدقيق والمراجعة
        audit_section = self.create_settings_section(
            scrollable_frame,
            "📝 التدقيق والمراجعة",
            "تسجيل ومراجعة العمليات"
        )

        # تفعيل تدقيق العمليات
        self.create_checkbox_field(
            audit_section,
            "تدقيق جميع العمليات",
            "enable_audit_trail",
            self.settings.get('enable_audit_trail', True)
        )

        # تسجيل تغييرات البيانات
        self.create_checkbox_field(
            audit_section,
            "تسجيل تغييرات البيانات",
            "log_data_changes",
            self.settings.get('log_data_changes', True)
        )

        # تسجيل الوصول للتقارير
        self.create_checkbox_field(
            audit_section,
            "تسجيل الوصول للتقارير",
            "log_report_access",
            self.settings.get('log_report_access', True)
        )

        # مدة الاحتفاظ بسجلات التدقيق
        self.create_number_field(
            audit_section,
            "مدة الاحتفاظ بسجلات التدقيق (أيام):",
            "audit_retention_days",
            self.settings.get('audit_retention_days', 365),
            min_value=30,
            max_value=3650,
            step=30
        )

        # أزرار الحفظ
        self.create_action_buttons(scrollable_frame)

    def show_security_log(self):
        """عرض سجل الأمان"""
        # إنشاء نافذة سجل الأمان
        log_window = ctk.CTkToplevel(self.window)
        log_window.title("📋 سجل الأمان")
        log_window.geometry("800x600")
        log_window.configure(fg_color=self.colors['background'])

        # جعل النافذة في المقدمة
        log_window.transient(self.window)
        log_window.grab_set()

        # المحتوى
        content_frame = ctk.CTkFrame(log_window, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # العنوان
        title_label = ctk.CTkLabel(
            content_frame,
            text="📋 سجل الأمان",
            font=self.fonts['subtitle'],
            text_color=self.colors['text_primary']
        )
        title_label.pack(pady=(0, 20))

        # جدول السجل
        log_frame = ctk.CTkScrollableFrame(content_frame)
        log_frame.pack(fill="both", expand=True)

        # بيانات وهمية للسجل
        log_entries = [
            {"time": "2024-01-15 10:30:15", "user": "admin", "action": "تسجيل دخول ناجح", "ip": "*************"},
            {"time": "2024-01-15 10:25:30", "user": "user1", "action": "محاولة دخول فاشلة", "ip": "*************"},
            {"time": "2024-01-15 10:20:45", "user": "admin", "action": "تعديل إعدادات", "ip": "*************"},
            {"time": "2024-01-15 10:15:20", "user": "accountant1", "action": "عرض تقرير مالي", "ip": "*************"}
        ]

        for entry in log_entries:
            entry_frame = ctk.CTkFrame(log_frame, fg_color=self.colors['surface_variant'])
            entry_frame.pack(fill="x", pady=2, padx=5)

            entry_text = f"{entry['time']} | {entry['user']} | {entry['action']} | {entry['ip']}"
            entry_label = ctk.CTkLabel(
                entry_frame,
                text=entry_text,
                font=self.fonts['caption'],
                text_color=self.colors['text_primary']
            )
            entry_label.pack(pady=5, padx=10, anchor="w")

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            content_frame,
            text="إغلاق",
            font=self.fonts['button'],
            fg_color=self.colors['error'],
            hover_color=self.get_hover_color('error'),
            command=log_window.destroy
        )
        close_btn.pack(pady=20)

    def run_security_scan(self):
        """تشغيل فحص أمني"""
        self.show_toast("🔍 جاري تشغيل الفحص الأمني...", "info")

        # محاكاة الفحص الأمني
        def show_scan_results():
            results = """
🔍 نتائج الفحص الأمني:

✅ كلمات المرور: قوية
✅ التشفير: مفعل
⚠️ تحديثات الأمان: متوفرة (2)
✅ صلاحيات المستخدمين: صحيحة
⚠️ سجلات الأمان: تحتاج تنظيف
✅ النسخ الاحتياطية: محدثة

التوصيات:
• تطبيق تحديثات الأمان المتوفرة
• تنظيف سجلات الأمان القديمة
            """
            messagebox.showinfo("نتائج الفحص الأمني", results)
            self.show_toast("✅ تم الفحص الأمني بنجاح", "success")

        self.window.after(3000, show_scan_results)

    def generate_security_report(self):
        """إنشاء تقرير الأمان"""
        filename = filedialog.asksaveasfilename(
            title="حفظ تقرير الأمان",
            defaultextension=".pdf",
            filetypes=[
                ("ملفات PDF", "*.pdf"),
                ("ملفات Word", "*.docx"),
                ("ملفات Excel", "*.xlsx")
            ]
        )

        if filename:
            self.show_toast("📊 جاري إنشاء تقرير الأمان...", "info")
            # محاكاة إنشاء التقرير
            self.window.after(2000, lambda: self.show_toast("✅ تم إنشاء تقرير الأمان بنجاح", "success"))


def main():
    """تشغيل لوحة التحكم الشاملة للاختبار"""
    # إنشاء النافذة الرئيسية (مخفية)
    root = ctk.CTk()
    root.withdraw()

    # تطبيق الثيم
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")

    try:
        # إنشاء لوحة التحكم
        admin_panel = ComprehensiveAdminPanel()

        # تشغيل الحلقة الرئيسية
        root.mainloop()

    except Exception as e:
        print(f"خطأ في تشغيل لوحة التحكم: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل لوحة التحكم:\n{str(e)}")


if __name__ == "__main__":
    main()

















































