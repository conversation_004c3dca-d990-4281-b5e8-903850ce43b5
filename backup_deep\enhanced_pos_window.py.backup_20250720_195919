# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة نقطة البيع المحسنة والاحترافية
Enhanced Professional POS System
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime

try:
    from themes.modern_theme import MODERN_COLORS, FONTS
    from ui.window_utils import configure_window_fullscreen
    from database.hybrid_database_manager import HybridDatabaseManager
    from PIL import Image
except ImportError:
    # ألوان افتراضية في حالة عدم وجود الثيم
    MODERN_COLORS = {
        'primary': '#1f538d',
        'background': '#f0f0f0',
        'surface': '#ffffff',
        'text_primary': '#000000',
        'success': '#28a745',
        'error': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8'
    }
    FONTS = {'arabic': 'Arial'}

class EnhancedPOSWindow:
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.cart_items = []
        self.total_amount = 0.0
        self.selected_customer = None
        self.current_quantity = 1
        self.calculator_display = "0"
        self.calculator_memory = 0
        self.calculator_operation = None
        self.calculator_waiting_for_operand = False

        # تهيئة قاعدة البيانات
        try:
            self.db_manager = HybridDatabaseManager()
            self.sales_manager = self.db_manager.sales_manager
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            self.db_manager = None
            self.sales_manager = None

        # بيانات المنتجات التجريبية
        self.products = self.load_sample_products()

        self.create_window()

    def load_image_with_background_removal(self, image_path, size=(100, 100)):
        """تحميل صورة مع إزالة الخلفية البيضاء"""
        try:
            print(f"تحميل صورة المنتج: {image_path} بحجم {size}")

            # فتح الصورة
            image = Image.open(image_path)
            print(f"حجم الصورة الأصلي: {image.size}")

            # تحويل إلى RGBA إذا لم تكن كذلك
            if image.mode != 'RGBA':
                image = image.convert('RGBA')

            # الحصول على بيانات البكسل
            data = image.getdata()

            # إنشاء قائمة جديدة للبكسل مع إزالة الخلفية البيضاء
            new_data = []
            for item in data:
                # إذا كان البكسل أبيض أو قريب من الأبيض، اجعله شفاف
                if item[0] > 240 and item[1] > 240 and item[2] > 240:
                    new_data.append((255, 255, 255, 0))  # شفاف
                else:
                    new_data.append(item)

            # تطبيق البيانات الجديدة
            image.putdata(new_data)

            # تغيير حجم الصورة
            image = image.resize(size, Image.Resampling.LANCZOS)
            print(f"حجم الصورة بعد التغيير: {image.size}")

            print("تم إنشاء CTkImage مع إزالة الخلفية بنجاح")
            return ctk.CTkImage(light_image=image, size=size)

        except Exception as e:
            print(f"خطأ في تحميل الصورة {image_path}: {e}")
            return None

    def load_sample_products(self):
        """تحميل منتجات تجريبية"""
        return [
            {"id": 1, "name": "مكيف هواء سبليت", "price": 2100.0, "category": "أجهزة كهربائية", "image": "assets/icons/1.png"},
            {"id": 2, "name": "فرن كهربائي 45 لتر", "price": 850.0, "category": "أجهزة مطبخ", "image": "assets/icons/2.png"},
            {"id": 3, "name": "مكنسة كهربائية", "price": 450.0, "category": "أجهزة تنظيف", "image": "assets/icons/3.png"},
            {"id": 4, "name": "غلاية كهربائية", "price": 120.0, "category": "أجهزة مطبخ", "image": "assets/icons/4.png"},
            {"id": 5, "name": "مروحة سقف", "price": 320.0, "category": "أجهزة كهربائية", "image": "assets/icons/5.png"},
            {"id": 6, "name": "ميكروويف", "price": 680.0, "category": "أجهزة مطبخ", "image": "assets/icons/6.png"},
            {"id": 7, "name": "غسالة أطباق", "price": 1850.0, "category": "أجهزة مطبخ", "image": "assets/icons/7.png"},
            {"id": 8, "name": "ثلاجة صغيرة", "price": 950.0, "category": "أجهزة كهربائية", "image": "assets/icons/8.png"},
            {"id": 9, "name": "تلفزيون ذكي", "price": 1200.0, "category": "أجهزة كهربائية", "image": "assets/icons/9.png"},
            {"id": 10, "name": "هاتف ذكي", "price": 800.0, "category": "إلكترونيات", "image": "assets/icons/10.png"},
            {"id": 11, "name": "لابتوب", "price": 2500.0, "category": "إلكترونيات", "image": "assets/icons/11.png"},
            {"id": 12, "name": "طابعة", "price": 350.0, "category": "مكتبية", "image": "assets/icons/12.png"}
        ]

    def create_window(self):
        """إنشاء النافذة الرئيسية"""
        try:
            self.window = ctk.CTkToplevel(self.parent)

            # إعداد النافذة لتملأ الشاشة
            configure_window_fullscreen(self.window, "نقطة البيع المحسنة - Aliphia POS")

            # جعل النافذة في المقدمة
            self.window.transient(self.parent)
            self.window.grab_set()

            # إنشاء المحتوى
            self.create_main_layout()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء النافذة: {str(e)}")

    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي"""
        # الشريط العلوي
        self.create_header()

        # المحتوى الرئيسي
        main_container = ctk.CTkFrame(self.window, fg_color="#f8f9fa")
        main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # تقسيم الشاشة إلى ثلاثة أقسام
        # القسم الأيسر - المنتجات (50%)
        products_section = ctk.CTkFrame(main_container, fg_color="#ffffff")
        products_section.pack(side="left", fill="both", expand=True, padx=(0, 5))

        # القسم الأوسط - السلة والفاتورة (30%)
        cart_section = ctk.CTkFrame(main_container, fg_color="#ffffff", width=400)
        cart_section.pack(side="left", fill="y", padx=5)
        cart_section.pack_propagate(False)

        # القسم الأيمن - الحاسبة والدفع (20%)
        calculator_section = ctk.CTkFrame(main_container, fg_color="#ffffff", width=300)
        calculator_section.pack(side="right", fill="y", padx=(5, 0))
        calculator_section.pack_propagate(False)

        # إنشاء محتوى كل قسم
        self.create_products_section(products_section)
        self.create_cart_section(cart_section)
        self.create_calculator_section(calculator_section)

    def create_header(self):
        """إنشاء الشريط العلوي"""
        header = ctk.CTkFrame(self.window, height=80, fg_color="#2E8B57")
        header.pack(fill="x", padx=10, pady=(10, 0))
        header.pack_propagate(False)

        # شعار وعنوان
        title_frame = ctk.CTkFrame(header, fg_color="transparent")
        title_frame.pack(side="right", padx=20, pady=15)

        ctk.CTkLabel(
            title_frame,
            text="🏪 Aliphia POS",
            font=("Arial", 24, "bold"),
            text_color="white"
        ).pack()

        ctk.CTkLabel(
            title_frame,
            text="نقطة البيع الاحترافية",
            font=("Arial", 12),
            text_color="#E8F5E8"
        ).pack()

        # معلومات التاريخ والمستخدم
        info_frame = ctk.CTkFrame(header, fg_color="transparent")
        info_frame.pack(side="left", padx=20, pady=15)

        ctk.CTkLabel(
            info_frame,
            text=f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}",
            font=("Arial", 12),
            text_color="white"
        ).pack(anchor="w")

        ctk.CTkLabel(
            info_frame,
            text=f"الوقت: {datetime.now().strftime('%H:%M')}",
            font=("Arial", 12),
            text_color="#E8F5E8"
        ).pack(anchor="w")

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            header,
            text="❌ إغلاق",
            width=100,
            height=40,
            fg_color="#dc3545",
            hover_color="#c82333",
            command=self.close_window,
            font=("Arial", 12, "bold")
        )
        close_btn.pack(side="left", padx=(0, 20), pady=20)

    def create_products_section(self, parent):
        """إنشاء قسم المنتجات"""
        # عنوان القسم
        header = ctk.CTkFrame(parent, height=60, fg_color="#4682B4")
        header.pack(fill="x", padx=10, pady=(10, 0))
        header.pack_propagate(False)

        ctk.CTkLabel(
            header,
            text="🛍️ المنتجات المتاحة",
            font=("Arial", 18, "bold"),
            text_color="white"
        ).pack(pady=15)

        # شريط البحث
        search_frame = ctk.CTkFrame(parent, height=50, fg_color="#f8f9fa")
        search_frame.pack(fill="x", padx=10, pady=5)
        search_frame.pack_propagate(False)

        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="🔍 البحث عن منتج...",
            font=("Arial", 12),
            width=300
        )
        self.search_entry.pack(side="right", padx=10, pady=10)

        # إطار المنتجات مع التمرير
        products_frame = ctk.CTkScrollableFrame(parent, fg_color="#ffffff")
        products_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عرض المنتجات في شبكة
        self.display_products_grid(products_frame)

    def display_products_grid(self, parent):
        """عرض المنتجات في شبكة"""
        # تنظيم المنتجات في صفوف وأعمدة
        cols = 4
        for i, product in enumerate(self.products):
            row = i // cols
            col = i % cols

            # إطار المنتج
            product_frame = ctk.CTkFrame(parent, width=180, height=220, fg_color="#f8f9fa")
            product_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
            product_frame.grid_propagate(False)

            # صورة المنتج
            image_frame = ctk.CTkFrame(product_frame, height=120, fg_color="#e9ecef")
            image_frame.pack(fill="x", padx=5, pady=(5, 0))
            image_frame.pack_propagate(False)

            # تحميل وعرض صورة المنتج
            ctk_image = self.load_image_with_background_removal(product["image"], (100, 100))

            if ctk_image:
                # عرض الصورة
                image_label = ctk.CTkLabel(
                    image_frame,
                    image=ctk_image,
                    text=""
                )
                image_label.pack(expand=True, pady=10)
            else:
                # في حالة فشل تحميل الصورة، عرض رمز تعبيري
                ctk.CTkLabel(
                    image_frame,
                    text="📦",
                    font=("Arial", 40),
                    text_color="#6c757d"
                ).pack(expand=True)

            # اسم المنتج
            ctk.CTkLabel(
                product_frame,
                text=product["name"],
                font=("Arial", 12, "bold"),
                wraplength=160
            ).pack(pady=(5, 0))

            # السعر
            price_label = ctk.CTkLabel(
                product_frame,
                text=f"SAR {product['price']:.2f}",
                font=("Arial", 14, "bold"),
                text_color="#2E8B57"
            )
            price_label.pack()

            # زر الإضافة
            add_btn = ctk.CTkButton(
                product_frame,
                text="➕ إضافة",
                width=120,
                height=30,
                fg_color="#28a745",
                hover_color="#218838",
                command=lambda p=product: self.add_to_cart(p),
                font=("Arial", 10, "bold")
            )
            add_btn.pack(pady=5)

    def create_cart_section(self, parent):
        """إنشاء قسم السلة والفاتورة"""
        # عنوان القسم
        header = ctk.CTkFrame(parent, height=60, fg_color="#FF6B35")
        header.pack(fill="x", padx=10, pady=(10, 0))
        header.pack_propagate(False)

        ctk.CTkLabel(
            header,
            text="🛒 سلة المشتريات",
            font=("Arial", 18, "bold"),
            text_color="white"
        ).pack(pady=15)

        # معلومات العميل
        customer_frame = ctk.CTkFrame(parent, height=80, fg_color="#f8f9fa")
        customer_frame.pack(fill="x", padx=10, pady=5)
        customer_frame.pack_propagate(False)

        ctk.CTkLabel(
            customer_frame,
            text="👤 العميل:",
            font=("Arial", 12, "bold")
        ).pack(anchor="w", padx=10, pady=(10, 0))

        self.customer_label = ctk.CTkLabel(
            customer_frame,
            text="عميل نقدي",
            font=("Arial", 12),
            text_color="#6c757d"
        )
        self.customer_label.pack(anchor="w", padx=10)

        customer_btn = ctk.CTkButton(
            customer_frame,
            text="اختيار عميل",
            width=100,
            height=30,
            fg_color="#17a2b8",
            command=self.select_customer,
            font=("Arial", 10)
        )
        customer_btn.pack(side="left", padx=10, pady=5)

        # قائمة السلة
        cart_frame = ctk.CTkScrollableFrame(parent, fg_color="#ffffff")
        cart_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عناوين الجدول
        headers_frame = ctk.CTkFrame(cart_frame, height=40, fg_color="#e9ecef")
        headers_frame.pack(fill="x", pady=(0, 5))
        headers_frame.pack_propagate(False)

        headers = ["الصنف", "الكمية", "السعر", "الإجمالي", "حذف"]
        widths = [120, 60, 80, 80, 60]

        for i, (header, width) in enumerate(zip(headers, widths)):
            ctk.CTkLabel(
                headers_frame,
                text=header,
                font=("Arial", 11, "bold"),
                width=width
            ).pack(side="right" if i == 0 else "left", padx=5, pady=10)

        # إطار عناصر السلة
        self.cart_items_frame = ctk.CTkFrame(cart_frame, fg_color="transparent")
        self.cart_items_frame.pack(fill="x")

        # إجمالي الفاتورة
        total_frame = ctk.CTkFrame(parent, height=100, fg_color="#2E8B57")
        total_frame.pack(fill="x", padx=10, pady=10)
        total_frame.pack_propagate(False)

        ctk.CTkLabel(
            total_frame,
            text="💰 إجمالي الفاتورة",
            font=("Arial", 14, "bold"),
            text_color="white"
        ).pack(pady=(10, 0))

        self.total_label = ctk.CTkLabel(
            total_frame,
            text="SAR 0.00",
            font=("Arial", 20, "bold"),
            text_color="#E8F5E8"
        )
        self.total_label.pack(pady=(0, 10))

        # أزرار العمليات
        actions_frame = ctk.CTkFrame(parent, height=60, fg_color="#f8f9fa")
        actions_frame.pack(fill="x", padx=10, pady=(0, 10))
        actions_frame.pack_propagate(False)

        # زر الدفع
        pay_btn = ctk.CTkButton(
            actions_frame,
            text="💳 دفع",
            width=120,
            height=40,
            fg_color="#28a745",
            hover_color="#218838",
            command=self.process_payment,
            font=("Arial", 12, "bold")
        )
        pay_btn.pack(side="right", padx=10, pady=10)

        # زر مسح السلة
        clear_btn = ctk.CTkButton(
            actions_frame,
            text="🗑️ مسح",
            width=80,
            height=40,
            fg_color="#dc3545",
            hover_color="#c82333",
            command=self.clear_cart,
            font=("Arial", 12, "bold")
        )
        clear_btn.pack(side="right", padx=5, pady=10)

    def create_calculator_section(self, parent):
        """إنشاء قسم الحاسبة المحسنة"""
        # عنوان القسم
        header = ctk.CTkFrame(parent, height=60, fg_color="#9C27B0")
        header.pack(fill="x", padx=10, pady=(10, 0))
        header.pack_propagate(False)

        ctk.CTkLabel(
            header,
            text="🧮 الحاسبة",
            font=("Arial", 18, "bold"),
            text_color="white"
        ).pack(pady=15)

        # شاشة الحاسبة
        display_frame = ctk.CTkFrame(parent, height=80, fg_color="#1a1a1a")
        display_frame.pack(fill="x", padx=10, pady=10)
        display_frame.pack_propagate(False)

        self.calculator_display_label = ctk.CTkLabel(
            display_frame,
            text="0",
            font=("Arial", 24, "bold"),
            text_color="#00FF00",
            anchor="e"
        )
        self.calculator_display_label.pack(fill="both", expand=True, padx=10, pady=10)

        # أزرار الحاسبة
        buttons_frame = ctk.CTkFrame(parent, fg_color="transparent")
        buttons_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # تخطيط الأزرار
        calculator_buttons = [
            ["C", "±", "%", "÷"],
            ["7", "8", "9", "×"],
            ["4", "5", "6", "-"],
            ["1", "2", "3", "+"],
            ["+/-", "0", ".", "="]
        ]

        # ألوان الأزرار
        button_colors = {
            "C": "#FF6B35",      # برتقالي للمسح
            "±": "#4682B4",      # أزرق للعمليات
            "%": "#4682B4",
            "÷": "#4682B4",
            "×": "#4682B4",
            "-": "#4682B4",
            "+": "#4682B4",
            "=": "#2E8B57",      # أخضر للنتيجة
            "+/-": "#6c757d",    # رمادي للوظائف الإضافية
            ".": "#6c757d"
        }

        for row_idx, row in enumerate(calculator_buttons):
            row_frame = ctk.CTkFrame(buttons_frame, fg_color="transparent")
            row_frame.pack(fill="x", pady=2)

            for col_idx, button_text in enumerate(row):
                # تحديد لون الزر
                if button_text.isdigit():
                    btn_color = "#495057"  # رمادي داكن للأرقام
                    hover_color = "#6c757d"
                else:
                    btn_color = button_colors.get(button_text, "#6c757d")
                    hover_color = self.get_hover_color(btn_color)

                # تحديد عرض الزر (زر الصفر أعرض)
                btn_width = 130 if button_text == "0" else 60

                btn = ctk.CTkButton(
                    row_frame,
                    text=button_text,
                    width=btn_width,
                    height=50,
                    fg_color=btn_color,
                    hover_color=hover_color,
                    command=lambda t=button_text: self.calculator_button_click(t),
                    font=("Arial", 16, "bold"),
                    corner_radius=8
                )

                if button_text == "0":
                    btn.pack(side="left", padx=2, fill="x", expand=True)
                else:
                    btn.pack(side="left", padx=2)

        # قسم الدفع السريع
        payment_frame = ctk.CTkFrame(parent, fg_color="#f8f9fa")
        payment_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            payment_frame,
            text="💸 دفع سريع",
            font=("Arial", 14, "bold")
        ).pack(pady=(10, 5))

        # أزرار الدفع السريع
        quick_amounts = [50, 100, 200, 500]
        quick_frame = ctk.CTkFrame(payment_frame, fg_color="transparent")
        quick_frame.pack(fill="x", padx=10, pady=(0, 10))

        for amount in quick_amounts:
            btn = ctk.CTkButton(
                quick_frame,
                text=f"{amount}",
                width=60,
                height=35,
                fg_color="#17a2b8",
                hover_color="#138496",
                command=lambda a=amount: self.quick_payment(a),
                font=("Arial", 11, "bold")
            )
            btn.pack(side="left", padx=2, fill="x", expand=True)

    def get_hover_color(self, color):
        """الحصول على لون التمرير"""
        hover_colors = {
            "#FF6B35": "#E55A2B",
            "#4682B4": "#3A6B94",
            "#2E8B57": "#267A4A",
            "#6c757d": "#5A6268",
            "#495057": "#3D4449"
        }
        return hover_colors.get(color, "#5A6268")

    def calculator_button_click(self, button_text):
        """معالجة نقر أزرار الحاسبة"""
        try:
            if button_text == "C":
                self.calculator_display = "0"
                self.calculator_memory = 0
                self.calculator_operation = None
                self.calculator_waiting_for_operand = False

            elif button_text == "±":
                if self.calculator_display != "0":
                    if self.calculator_display.startswith("-"):
                        self.calculator_display = self.calculator_display[1:]
                    else:
                        self.calculator_display = "-" + self.calculator_display

            elif button_text == "%":
                value = float(self.calculator_display)
                self.calculator_display = str(value / 100)

            elif button_text in ["÷", "×", "-", "+"]:
                if not self.calculator_waiting_for_operand:
                    if self.calculator_operation:
                        self.calculate_result()
                    self.calculator_memory = float(self.calculator_display)

                self.calculator_operation = button_text
                self.calculator_waiting_for_operand = True

            elif button_text == "=":
                if self.calculator_operation and not self.calculator_waiting_for_operand:
                    self.calculate_result()
                    self.calculator_operation = None

            elif button_text == ".":
                if "." not in self.calculator_display:
                    if self.calculator_waiting_for_operand:
                        self.calculator_display = "0."
                        self.calculator_waiting_for_operand = False
                    else:
                        self.calculator_display += "."

            elif button_text == "+/-":
                self.calculator_button_click("±")

            else:  # أرقام
                if self.calculator_waiting_for_operand or self.calculator_display == "0":
                    self.calculator_display = button_text
                    self.calculator_waiting_for_operand = False
                else:
                    self.calculator_display += button_text

            # تحديث العرض
            self.update_calculator_display()

        except Exception as e:
            self.calculator_display = "خطأ"
            self.update_calculator_display()

    def calculate_result(self):
        """حساب النتيجة"""
        try:
            current_value = float(self.calculator_display)

            if self.calculator_operation == "+":
                result = self.calculator_memory + current_value
            elif self.calculator_operation == "-":
                result = self.calculator_memory - current_value
            elif self.calculator_operation == "×":
                result = self.calculator_memory * current_value
            elif self.calculator_operation == "÷":
                if current_value != 0:
                    result = self.calculator_memory / current_value
                else:
                    self.calculator_display = "خطأ"
                    return
            else:
                return

            # تنسيق النتيجة
            if result == int(result):
                self.calculator_display = str(int(result))
            else:
                self.calculator_display = f"{result:.8f}".rstrip('0').rstrip('.')

        except:
            self.calculator_display = "خطأ"

    def update_calculator_display(self):
        """تحديث شاشة الحاسبة"""
        if hasattr(self, 'calculator_display_label'):
            # تحديد لون النص حسب المحتوى
            if self.calculator_display == "خطأ":
                text_color = "#FF0000"
            elif self.calculator_display.startswith("-"):
                text_color = "#FF6B35"
            else:
                text_color = "#00FF00"

            self.calculator_display_label.configure(
                text=self.calculator_display,
                text_color=text_color
            )

    def quick_payment(self, amount):
        """دفع سريع"""
        self.calculator_display = str(amount)
        self.update_calculator_display()

    def add_to_cart(self, product):
        """إضافة منتج إلى السلة"""
        try:
            # البحث عن المنتج في السلة
            existing_item = None
            for item in self.cart_items:
                if item["id"] == product["id"]:
                    existing_item = item
                    break

            if existing_item:
                # زيادة الكمية
                existing_item["quantity"] += self.current_quantity
                existing_item["total"] = existing_item["quantity"] * existing_item["price"]
            else:
                # إضافة منتج جديد
                cart_item = {
                    "id": product["id"],
                    "name": product["name"],
                    "price": product["price"],
                    "quantity": self.current_quantity,
                    "total": product["price"] * self.current_quantity
                }
                self.cart_items.append(cart_item)

            # تحديث العرض
            self.update_cart_display()
            self.update_total()

            # رسالة نجاح
            messagebox.showinfo("تم", f"تم إضافة {product['name']} إلى السلة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المنتج: {str(e)}")

    def update_cart_display(self):
        """تحديث عرض السلة"""
        try:
            # مسح العرض الحالي
            for widget in self.cart_items_frame.winfo_children():
                if widget and hasattr(widget, "destroy"):

                    widget.destroy()

            # عرض العناصر
            for i, item in enumerate(self.cart_items):
                item_frame = ctk.CTkFrame(self.cart_items_frame, height=50, fg_color="#f8f9fa" if i % 2 == 0 else "#ffffff")
                item_frame.pack(fill="x", pady=1)
                item_frame.pack_propagate(False)

                # اسم المنتج
                ctk.CTkLabel(
                    item_frame,
                    text=item["name"][:15] + "..." if len(item["name"]) > 15 else item["name"],
                    font=("Arial", 10),
                    width=120
                ).pack(side="right", padx=5, pady=10)

                # الكمية
                ctk.CTkLabel(
                    item_frame,
                    text=str(item["quantity"]),
                    font=("Arial", 10),
                    width=60
                ).pack(side="left", padx=5, pady=10)

                # السعر
                ctk.CTkLabel(
                    item_frame,
                    text=f"{item['price']:.2f}",
                    font=("Arial", 10),
                    width=80
                ).pack(side="left", padx=5, pady=10)

                # الإجمالي
                ctk.CTkLabel(
                    item_frame,
                    text=f"{item['total']:.2f}",
                    font=("Arial", 10, "bold"),
                    text_color="#2E8B57",
                    width=80
                ).pack(side="left", padx=5, pady=10)

                # زر الحذف
                delete_btn = ctk.CTkButton(
                    item_frame,
                    text="🗑️",
                    width=30,
                    height=30,
                    fg_color="#dc3545",
                    hover_color="#c82333",
                    command=lambda idx=i: self.remove_from_cart(idx),
                    font=("Arial", 12)
                )
                delete_btn.pack(side="left", padx=5, pady=10)

        except Exception as e:
            print(f"خطأ في تحديث السلة: {e}")

    def update_total(self):
        """تحديث إجمالي الفاتورة"""
        try:
            self.total_amount = sum(item["total"] for item in self.cart_items)
            if hasattr(self, 'total_label'):
                self.total_label.configure(text=f"SAR {self.total_amount:.2f}")
        except Exception as e:
            print(f"خطأ في تحديث الإجمالي: {e}")

    def remove_from_cart(self, index):
        """حذف منتج من السلة"""
        try:
            if 0 <= index < len(self.cart_items):
                removed_item = self.cart_items.pop(index)
                self.update_cart_display()
                self.update_total()
                messagebox.showinfo("تم", f"تم حذف {removed_item['name']} من السلة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حذف المنتج: {str(e)}")

    def clear_cart(self):
        """مسح السلة"""
        if self.cart_items:
            if messagebox.askyesno("تأكيد", "هل تريد مسح جميع المنتجات من السلة؟"):
                self.cart_items.clear()
                self.update_cart_display()
                self.update_total()
                messagebox.showinfo("تم", "تم مسح السلة")
        else:
            messagebox.showinfo("تنبيه", "السلة فارغة بالفعل")

    def select_customer(self):
        """اختيار عميل"""
        # نافذة اختيار العميل
        customer_window = ctk.CTkToplevel(self.window)
        customer_window.title("اختيار عميل")
        customer_window.geometry("400x300")
        customer_window.transient(self.window)
        customer_window.grab_set()

        # قائمة العملاء التجريبية
        customers = [
            "عميل نقدي",
            "أحمد محمد",
            "فاطمة علي",
            "محمد أحمد",
            "سارة محمود"
        ]

        ctk.CTkLabel(
            customer_window,
            text="اختر العميل:",
            font=("Arial", 14, "bold")
        ).pack(pady=20)

        customer_var = tk.StringVar(value=customers[0])

        for customer in customers:
            ctk.CTkRadioButton(
                customer_window,
                text=customer,
                variable=customer_var,
                value=customer,
                font=("Arial", 12)
            ).pack(pady=5, anchor="w", padx=50)

        def confirm_customer():
            self.selected_customer = customer_var.get()
            self.customer_label.configure(text=self.selected_customer)
            if customer_window and hasattr(customer_window, "destroy"):

                customer_window.destroy()

        ctk.CTkButton(
            customer_window,
            text="تأكيد",
            command=confirm_customer,
            fg_color="#2E8B57"
        ).pack(pady=20)

    def process_payment(self):
        """معالجة عملية الدفع"""
        if not self.cart_items:
            messagebox.showwarning("تحذير", "السلة فارغة!")
            return

        # نافذة الدفع
        payment_window = ctk.CTkToplevel(self.window)
        payment_window.title("إتمام عملية الدفع")
        payment_window.geometry("500x400")
        payment_window.transient(self.window)
        payment_window.grab_set()

        # معلومات الفاتورة
        info_frame = ctk.CTkFrame(payment_window, fg_color="#f8f9fa")
        info_frame.pack(fill="x", padx=20, pady=20)

        ctk.CTkLabel(
            info_frame,
            text=f"إجمالي الفاتورة: SAR {self.total_amount:.2f}",
            font=("Arial", 16, "bold"),
            text_color="#2E8B57"
        ).pack(pady=10)

        ctk.CTkLabel(
            info_frame,
            text=f"العميل: {self.selected_customer or 'عميل نقدي'}",
            font=("Arial", 12)
        ).pack(pady=5)

        # طريقة الدفع
        payment_frame = ctk.CTkFrame(payment_window)
        payment_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(
            payment_frame,
            text="طريقة الدفع:",
            font=("Arial", 12, "bold")
        ).pack(pady=(10, 5))

        payment_method = ctk.CTkOptionMenu(
            payment_frame,
            values=["نقدي", "بطاقة ائتمان", "تحويل بنكي", "آجل"],
            font=("Arial", 12)
        )
        payment_method.pack(pady=5)
        payment_method.set("نقدي")

        # المبلغ المدفوع
        amount_frame = ctk.CTkFrame(payment_window)
        amount_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(
            amount_frame,
            text="المبلغ المدفوع:",
            font=("Arial", 12, "bold")
        ).pack(pady=(10, 5))

        paid_entry = ctk.CTkEntry(
            amount_frame,
            placeholder_text="أدخل المبلغ المدفوع",
            font=("Arial", 12)
        )
        paid_entry.pack(pady=5, fill="x", padx=10)
        paid_entry.insert(0, str(self.total_amount))

        # الباقي
        change_label = ctk.CTkLabel(
            amount_frame,
            text="الباقي: SAR 0.00",
            font=("Arial", 12),
            text_color="#dc3545"
        )
        change_label.pack(pady=5)

        def calculate_change():
            try:
                paid = float(paid_entry.get() or 0)
                change = paid - self.total_amount
                change_label.configure(
                    text=f"الباقي: SAR {change:.2f}",
                    text_color="#28a745" if change >= 0 else "#dc3545"
                )
            except:
                change_label.configure(text="الباقي: خطأ في المبلغ")

        paid_entry.bind("<KeyRelease>", lambda e: calculate_change())

        def complete_payment():
            try:
                paid = float(paid_entry.get() or 0)
                if paid < self.total_amount:
                    messagebox.showerror("خطأ", "المبلغ المدفوع أقل من إجمالي الفاتورة!")
                    return

                # حفظ الفاتورة
                invoice_data = {
                    'customer': self.selected_customer or 'عميل نقدي',
                    'items': self.cart_items,
                    'total': self.total_amount,
                    'paid': paid,
                    'change': paid - self.total_amount,
                    'payment_method': payment_method.get(),
                    'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }

                # رقم الفاتورة
                invoice_no = f"INV{datetime.now().strftime('%Y%m%d%H%M%S')}"

                # رسالة نجاح
                messagebox.showinfo(
                    "نجح",
                    f"تم إتمام عملية البيع بنجاح!\n"
                    f"رقم الفاتورة: {invoice_no}\n"
                    f"الإجمالي: SAR {self.total_amount:.2f}\n"
                    f"المدفوع: SAR {paid:.2f}\n"
                    f"الباقي: SAR {paid - self.total_amount:.2f}"
                )

                # مسح السلة
                self.cart_items.clear()
                self.selected_customer = None
                self.update_cart_display()
                self.update_total()
                self.customer_label.configure(text="عميل نقدي")

                if payment_window and hasattr(payment_window, "destroy"):


                    payment_window.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح!")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إتمام العملية: {str(e)}")

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(payment_window, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=20)

        ctk.CTkButton(
            buttons_frame,
            text="💳 إتمام الدفع",
            command=complete_payment,
            fg_color="#28a745",
            hover_color="#218838",
            font=("Arial", 12, "bold")
        ).pack(side="right", padx=10)

        ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=payment_window.destroy,
            fg_color="#6c757d",
            font=("Arial", 12)
        ).pack(side="right")

    def close_window(self):
        """إغلاق النافذة"""
        if self.cart_items:
            if messagebox.askyesno("تأكيد", "هناك منتجات في السلة. هل تريد الإغلاق؟"):
                if hasattr(self, \'window\') and self.window:

                    self.window.destroy()
        else:
            if hasattr(self, \'window\') and self.window:

                self.window.destroy()
