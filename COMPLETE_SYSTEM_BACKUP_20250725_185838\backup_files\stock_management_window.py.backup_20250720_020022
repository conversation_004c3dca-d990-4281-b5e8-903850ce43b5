# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة إدارة المخزون والحركات
Stock Management Window
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, date

try:
    from themes.modern_theme import MODERN_COLORS, FONTS
except ImportError:
    pass
except ImportError:
    MODERN_COLORS = {
        'primary': '#1f538d',
        'secondary': '#2c3e50',
        'success': '#27ae60',
        'danger': '#e74c3c',
        'error': '#e74c3c',
        'warning': '#f39c12',
        'info': '#3498db',
        'background': '#ecf0f1',
        'surface': '#ffffff',
        'text_primary': '#2c3e50',
        'text_secondary': '#7f8c8d',
        'border': '#bdc3c7'
    }
    FONTS = {'arabic': 'Arial', 'english': 'Arial'}

class StockManagementWindow:
    """نافذة إدارة المخزون والحركات"""
    
    def __init__(self, parent, db_manager=None):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.current_movement_id = None
        self.stock_movements_data = []
        self.stock_balance_data = []
        self.items_data = []
        self.warehouses_data = []
        
        # إعداد النافذة
        self.create_window()
        self.load_initial_data()
        
    def create_window(self):
        """إنشاء النافذة الرئيسية"""
        self.window = ctk.CTkToplevel(self.parent)
        
        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "📦 إدارة المخزون والحركات - برنامج ست الكل للمحاسبة")

        # ضبط النافذة لملء الشاشة
        self.window  # ملء الشاشة في Windows

        # للأنظمة الأخرى كبديل
        try:
            self.window  # Linux
        except:
            pass

        # كبديل احتياطي - استخدام أبعاد الشاشة الكاملة
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        self.window.geometry(f"{screen_width}x{screen_height}+0+0")

        self.window.configure(fg_color=MODERN_COLORS['background'])

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إنشاء المحتوى
        self.create_header()
        self.create_main_layout()
        
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = ctk.CTkFrame(self.window, height=100, fg_color=MODERN_COLORS['primary'])
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        header_frame.pack_propagate(False)
        
        # الأيقونة والعنوان
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="right", padx=20, pady=15)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="📦 إدارة المخزون والحركات",
            font=(FONTS['arabic'], 24, "bold"),
            text_color="white"
        )
        title_label.pack()
        
        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="تتبع حركات المخزون والأرصدة اللحظية",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        subtitle_label.pack()
        
        # معلومات سريعة
        info_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        info_frame.pack(side="left", padx=20, pady=15)
        
        self.total_movements_label = ctk.CTkLabel(
            info_frame,
            text="إجمالي الحركات: 0",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.total_movements_label.pack()
        
        self.total_items_label = ctk.CTkLabel(
            info_frame,
            text="إجمالي الأصناف: 0",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.total_items_label.pack()
        
        self.low_stock_label = ctk.CTkLabel(
            info_frame,
            text="أصناف تحت الحد الأدنى: 0",
            font=(FONTS['arabic'], 12),
            text_color="white"
        )
        self.low_stock_label.pack()
    
    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي مع الأزرار على اليمين"""
        # إطار رئيسي يحتوي على المحتوى والأزرار
        main_container = ctk.CTkFrame(self.window, fg_color="transparent")
        main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # إطار الأزرار على اليمين
        self.buttons_frame = ctk.CTkFrame(main_container, width=200, fg_color=MODERN_COLORS['surface'])
        self.buttons_frame.pack(side="right", fill="y", padx=(0, 10))
        self.buttons_frame.pack_propagate(False)

        # إطار المحتوى الرئيسي على اليسار
        content_frame = ctk.CTkFrame(main_container, fg_color="transparent")
        content_frame.pack(side="left", fill="both", expand=True)

        # إطار التبويبات داخل المحتوى
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill="both", expand=True, padx=(10, 0), pady=(0, 10))

        # تبويب حركات المخزون
        self.movements_frame = ctk.CTkFrame(self.notebook, fg_color=MODERN_COLORS['surface'])
        self.notebook.add(self.movements_frame, text="🔄 حركات المخزون")

        # تبويب الأرصدة الحالية
        self.balance_frame = ctk.CTkFrame(self.notebook, fg_color=MODERN_COLORS['surface'])
        self.notebook.add(self.balance_frame, text="📊 الأرصدة الحالية")

        # تبويب إضافة حركة جديدة
        self.new_movement_frame = ctk.CTkFrame(self.notebook, fg_color=MODERN_COLORS['surface'])
        self.notebook.add(self.new_movement_frame, text="➕ إضافة حركة")

        # تبويب التقارير
        self.reports_frame = ctk.CTkFrame(self.notebook, fg_color=MODERN_COLORS['surface'])
        self.notebook.add(self.reports_frame, text="📈 التقارير")

        # إنشاء الأزرار الجانبية
        self.create_side_buttons()

        # إنشاء محتوى كل تبويب
        self.create_movements_tab()
        self.create_balance_tab()
        self.create_new_movement_tab()
        self.create_reports_tab()

    def create_side_buttons(self):
        """إنشاء الأزرار الجانبية على اليمين"""
        # عنوان الأزرار
        title_label = ctk.CTkLabel(
            self.buttons_frame,
            text="🔧 أدوات المخزون",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        title_label.pack(pady=(20, 15))

        # قائمة الأزرار
        buttons_data = [
            ("➕ إضافة حركة جديدة", self.switch_to_new_movement, MODERN_COLORS['success']),
            ("🔍 بحث في الحركات", self.focus_search, MODERN_COLORS['info']),
            ("📊 عرض الأرصدة", self.switch_to_balance, MODERN_COLORS['warning']),
            ("📈 التقارير", self.switch_to_reports, MODERN_COLORS['primary']),
            ("🔄 تحديث البيانات", self.refresh_all_data, MODERN_COLORS['secondary']),
            ("📤 تصدير إلى Excel", self.export_to_excel, MODERN_COLORS['success']),
            ("📋 تصدير إلى CSV", self.export_to_csv, MODERN_COLORS['info']),
            ("🖨️ طباعة التقرير", self.print_report, MODERN_COLORS['warning']),
            ("⚠️ تنبيهات المخزون", self.show_stock_alerts, MODERN_COLORS['danger']),
            ("📦 إدارة الأصناف", self.manage_items, MODERN_COLORS['primary']),
            ("🏪 إدارة المخازن", self.manage_warehouses, MODERN_COLORS['secondary']),
            ("⚙️ الإعدادات", self.show_settings, MODERN_COLORS['info']),
            ("❌ إغلاق النافذة", self.close_window, MODERN_COLORS['danger'])
        ]

        # إنشاء الأزرار
        for text, command, color in buttons_data:
            btn = ctk.CTkButton(
                self.buttons_frame,
                text=text,
                command=command,
                fg_color=color,
                hover_color=self.get_hover_color(color),
                width=180,
                height=40,
                font=(FONTS['arabic'], 12, "bold"),
                corner_radius=8
            )
            btn.pack(pady=5, padx=10, fill="x")

        # مساحة فارغة في الأسفل
        spacer = ctk.CTkFrame(self.buttons_frame, height=20, fg_color="transparent")
        spacer.pack(fill="x", pady=10)

    def get_hover_color(self, color):
        """الحصول على لون التمرير للزر"""
        hover_colors = {
            MODERN_COLORS['success']: "#1B5E20",
            MODERN_COLORS['info']: "#138496",
            MODERN_COLORS['warning']: "#e0a800",
            MODERN_COLORS['primary']: "#1B5E20",
            MODERN_COLORS['secondary']: "#5a6268",
            MODERN_COLORS['danger']: "#c82333"
        }
        return hover_colors.get(color, "#1B5E20")

    def create_movements_tab(self):
        """إنشاء تبويب حركات المخزون"""
        # شريط الفلاتر
        filters_frame = ctk.CTkFrame(self.movements_frame, height=80, fg_color=MODERN_COLORS['primary'])
        filters_frame.pack(fill="x", padx=10, pady=10)
        filters_frame.pack_propagate(False)
        
        # فلتر التاريخ
        date_frame = ctk.CTkFrame(filters_frame, fg_color="transparent")
        date_frame.pack(side="right", padx=20, pady=15)
        
        ctk.CTkLabel(date_frame, text="من:", font=(FONTS['arabic'], 12), text_color="white").pack(side="right", padx=5)
        self.start_date_entry = ctk.CTkEntry(date_frame, width=100, placeholder_text="YYYY-MM-DD")
        self.start_date_entry.pack(side="right", padx=5)
        
        ctk.CTkLabel(date_frame, text="إلى:", font=(FONTS['arabic'], 12), text_color="white").pack(side="right", padx=5)
        self.end_date_entry = ctk.CTkEntry(date_frame, width=100, placeholder_text="YYYY-MM-DD")
        self.end_date_entry.pack(side="right", padx=5)
        
        # فلتر نوع الحركة
        type_frame = ctk.CTkFrame(filters_frame, fg_color="transparent")
        type_frame.pack(side="right", padx=20, pady=15)
        
        ctk.CTkLabel(type_frame, text="نوع الحركة:", font=(FONTS['arabic'], 12), text_color="white").pack(side="right", padx=5)
        self.movement_type_filter = ctk.CTkComboBox(
            type_frame,
            values=["جميع الحركات", "إدخال", "إخراج", "تحويل", "تسوية"],
            width=120
        )
        self.movement_type_filter.pack(side="right", padx=5)
        
        # أزرار التحكم
        controls_frame = ctk.CTkFrame(filters_frame, fg_color="transparent")
        controls_frame.pack(side="left", padx=20, pady=15)
        
        search_btn = ctk.CTkButton(
            controls_frame,
            text="🔍 بحث",
            command=self.search_movements,
            fg_color=MODERN_COLORS['info'],
            width=80,
            height=30
        )
        search_btn.pack(side="left", padx=5)
        
        refresh_btn = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.refresh_movements,
            fg_color=MODERN_COLORS['success'],
            width=80,
            height=30
        )
        refresh_btn.pack(side="left", padx=5)
        
        # جدول حركات المخزون
        table_frame = ctk.CTkFrame(self.movements_frame, fg_color="white")
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء Treeview
        columns = ("id", "date", "item", "warehouse", "type", "quantity", "cost_price", "reference_no", "notes")
        
        self.movements_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=20
        )
        
        # تعريف رؤوس الأعمدة
        headers = {
            "id": "رقم الحركة",
            "date": "التاريخ",
            "item": "الصنف",
            "warehouse": "المخزن",
            "type": "نوع الحركة",
            "quantity": "الكمية",
            "cost_price": "السعر",
            "reference_no": "رقم المرجع",
            "notes": "ملاحظات"
        }
        
        for col, header in headers.items():
            self.movements_tree.heading(col, text=header, anchor="center")
        
        # تحديد عرض الأعمدة
        column_widths = {
            "id": 80,
            "date": 100,
            "item": 150,
            "warehouse": 120,
            "type": 100,
            "quantity": 80,
            "cost_price": 100,
            "reference_no": 120,
            "notes": 150
        }
        
        for col, width in column_widths.items():
            self.movements_tree.column(col, width=width, anchor="center")
        
        # شريط التمرير
        scrollbar_movements = ttk.Scrollbar(table_frame, orient="vertical", command=self.movements_tree.yview)
        self.movements_tree.configure(yscrollcommand=scrollbar_movements.set)
        
        # تخطيط الجدول
        self.movements_tree.pack(side="left", fill="both", expand=True)
        scrollbar_movements.pack(side="right", fill="y")
        
        # ربط الأحداث
        self.movements_tree.bind("<ButtonRelease-1>", self.on_movement_select)
        self.movements_tree.bind("<Double-1>", self.on_movement_double_click)
    
    def create_balance_tab(self):
        """إنشاء تبويب الأرصدة الحالية"""
        # شريط البحث والفلاتر
        search_frame = ctk.CTkFrame(self.balance_frame, height=60, fg_color=MODERN_COLORS['primary'])
        search_frame.pack(fill="x", padx=10, pady=10)
        search_frame.pack_propagate(False)
        
        # حقل البحث
        search_controls = ctk.CTkFrame(search_frame, fg_color="transparent")
        search_controls.pack(side="right", padx=20, pady=10)
        
        ctk.CTkLabel(search_controls, text="البحث:", font=(FONTS['arabic'], 12), text_color="white").pack(side="right", padx=5)
        self.balance_search_entry = ctk.CTkEntry(search_controls, width=200, placeholder_text="اسم الصنف أو الكود")
        self.balance_search_entry.pack(side="right", padx=5)
        self.balance_search_entry.bind("<KeyRelease>", self.on_balance_search)
        
        # فلتر المخزن
        warehouse_filter_frame = ctk.CTkFrame(search_frame, fg_color="transparent")
        warehouse_filter_frame.pack(side="right", padx=20, pady=10)
        
        ctk.CTkLabel(warehouse_filter_frame, text="المخزن:", font=(FONTS['arabic'], 12), text_color="white").pack(side="right", padx=5)
        self.warehouse_filter = ctk.CTkComboBox(
            warehouse_filter_frame,
            values=["جميع المخازن"],
            width=150,
            command=self.filter_by_warehouse
        )
        self.warehouse_filter.pack(side="right", padx=5)
        
        # أزرار التحكم
        balance_controls = ctk.CTkFrame(search_frame, fg_color="transparent")
        balance_controls.pack(side="left", padx=20, pady=10)
        
        export_btn = ctk.CTkButton(
            balance_controls,
            text="📤 تصدير",
            command=self.export_balance_report,
            fg_color=MODERN_COLORS['warning'],
            width=80,
            height=30
        )
        export_btn.pack(side="left", padx=5)
        
        # جدول الأرصدة
        balance_table_frame = ctk.CTkFrame(self.balance_frame, fg_color="white")
        balance_table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء Treeview للأرصدة
        balance_columns = ("item_id", "item_code", "item_name", "warehouse", "quantity", "unit", "cost_value", "status")
        
        self.balance_tree = ttk.Treeview(
            balance_table_frame,
            columns=balance_columns,
            show="headings",
            height=20
        )
        
        # تعريف رؤوس الأعمدة
        balance_headers = {
            "item_id": "رقم الصنف",
            "item_code": "كود الصنف",
            "item_name": "اسم الصنف",
            "warehouse": "المخزن",
            "quantity": "الكمية",
            "unit": "الوحدة",
            "cost_value": "قيمة المخزون",
            "status": "الحالة"
        }
        
        for col, header in balance_headers.items():
            self.balance_tree.heading(col, text=header, anchor="center")
        
        # تحديد عرض الأعمدة
        balance_widths = {
            "item_id": 80,
            "item_code": 100,
            "item_name": 200,
            "warehouse": 120,
            "quantity": 80,
            "unit": 80,
            "cost_value": 120,
            "status": 100
        }
        
        for col, width in balance_widths.items():
            self.balance_tree.column(col, width=width, anchor="center")
        
        # شريط التمرير للأرصدة
        scrollbar_balance = ttk.Scrollbar(balance_table_frame, orient="vertical", command=self.balance_tree.yview)
        self.balance_tree.configure(yscrollcommand=scrollbar_balance.set)
        
        # تخطيط جدول الأرصدة
        self.balance_tree.pack(side="left", fill="both", expand=True)
        scrollbar_balance.pack(side="right", fill="y")
        
        # ربط الأحداث
        self.balance_tree.bind("<Double-1>", self.on_balance_double_click)

    def create_new_movement_tab(self):
        """إنشاء تبويب إضافة حركة جديدة"""
        # عنوان التبويب
        title_label = ctk.CTkLabel(
            self.new_movement_frame,
            text="➕ إضافة حركة مخزون جديدة",
            font=(FONTS['arabic'], 20, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        title_label.pack(pady=20)

        # إطار النموذج
        form_container = ctk.CTkFrame(self.new_movement_frame, fg_color=MODERN_COLORS['surface'])
        form_container.pack(fill="both", expand=True, padx=50, pady=20)

        # إطار قابل للتمرير
        scroll_frame = ctk.CTkScrollableFrame(form_container, height=500)
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # معلومات الحركة الأساسية
        basic_info_label = ctk.CTkLabel(
            scroll_frame,
            text="📋 معلومات الحركة الأساسية",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        basic_info_label.pack(pady=(10, 15), anchor="e")

        # نوع الحركة
        self.create_movement_field(scroll_frame, "نوع الحركة:", "movement_type", field_type="combo",
                                values=["إدخال", "إخراج", "تحويل", "تسوية"], required=True)

        # الصنف
        self.create_movement_field(scroll_frame, "الصنف:", "item", field_type="combo",
                                values=self.get_items_list(), required=True)

        # المخزن
        self.create_movement_field(scroll_frame, "المخزن:", "warehouse", field_type="combo",
                                values=self.get_warehouses_list(), required=True)

        # الكمية
        self.create_movement_field(scroll_frame, "الكمية:", "quantity", field_type="number", required=True)

        # سعر التكلفة
        self.create_movement_field(scroll_frame, "سعر التكلفة:", "cost_price", field_type="number")

        # تاريخ الحركة
        self.create_movement_field(scroll_frame, "تاريخ الحركة:", "movement_date", field_type="date")

        # رقم المرجع
        self.create_movement_field(scroll_frame, "رقم المرجع:", "reference_no")

        # ملاحظات
        self.create_movement_field(scroll_frame, "ملاحظات:", "notes", field_type="textarea")

        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)

        save_movement_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ الحركة",
            command=self.save_movement,
            fg_color=MODERN_COLORS['success'],
            width=150,
            height=40,
            font=(FONTS['arabic'], 14, "bold")
        )
        save_movement_btn.pack(side="right", padx=10)

        clear_form_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح النموذج",
            command=self.clear_movement_form,
            fg_color=MODERN_COLORS['warning'],
            width=120,
            height=40,
            font=(FONTS['arabic'], 14, "bold")
        )
        clear_form_btn.pack(side="right", padx=10)

    def create_movement_field(self, parent, label_text, field_name, field_type="text", values=None, required=False):
        """إنشاء حقل في نموذج الحركة"""
        # إطار الحقل
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=8)

        # التسمية
        label_text_with_required = f"{label_text} *" if required else label_text
        label = ctk.CTkLabel(
            field_frame,
            text=label_text_with_required,
            font=(FONTS['arabic'], 12, "bold" if required else "normal"),
            text_color=MODERN_COLORS['error'] if required else MODERN_COLORS['text_primary']
        )
        label.pack(anchor="e", pady=(0, 5))

        # الحقل حسب النوع
        if field_type == "combo":
            field = ctk.CTkComboBox(
                field_frame,
                values=values or [],
                font=(FONTS['arabic'], 12),
                height=35
            )
        elif field_type == "number":
            field = ctk.CTkEntry(
                field_frame,
                placeholder_text="0.00",
                font=(FONTS['english'], 12),
                height=35
            )
        elif field_type == "date":
            field = ctk.CTkEntry(
                field_frame,
                placeholder_text="YYYY-MM-DD",
                font=(FONTS['english'], 12),
                height=35
            )
            # تعيين التاريخ الحالي
            field.insert(0, date.today().strftime("%Y-%m-%d"))
        elif field_type == "textarea":
            field = ctk.CTkTextbox(
                field_frame,
                height=80,
                font=(FONTS['arabic'], 12)
            )
        else:  # text:
            field = ctk.CTkEntry(
                field_frame,
                placeholder_text=f"أدخل {label_text}",
                font=(FONTS['arabic'], 12),
                height=35
            )

        field.pack(fill="x", pady=(0, 5))

        # حفظ مرجع الحقل
        setattr(self, f"movement_{field_name}_field", field)

        return field

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        # عنوان التبويب
        reports_title = ctk.CTkLabel(
            self.reports_frame,
            text="📈 تقارير المخزون",
            font=(FONTS['arabic'], 20, "bold"),
            text_color=MODERN_COLORS['primary']
        )
        reports_title.pack(pady=20)

        # إطار التقارير
        reports_container = ctk.CTkFrame(self.reports_frame, fg_color=MODERN_COLORS['surface'])
        reports_container.pack(fill="both", expand=True, padx=50, pady=20)

        # شبكة التقارير
        reports_grid = ctk.CTkFrame(reports_container, fg_color="transparent")
        reports_grid.pack(fill="both", expand=True, padx=20, pady=20)

        # تقرير الأرصدة الحالية
        balance_report_btn = ctk.CTkButton(
            reports_grid,
            text="📊 تقرير الأرصدة الحالية\nعرض جميع أرصدة المخزون",
            command=self.generate_balance_report,
            fg_color=MODERN_COLORS['info'],
            width=300,
            height=80,
            font=(FONTS['arabic'], 14, "bold")
        )
        balance_report_btn.grid(row=0, column=0, padx=20, pady=20)

        # تقرير حركات المخزون
        movements_report_btn = ctk.CTkButton(
            reports_grid,
            text="🔄 تقرير حركات المخزون\nسجل شامل لجميع الحركات",
            command=self.generate_movements_report,
            fg_color=MODERN_COLORS['success'],
            width=300,
            height=80,
            font=(FONTS['arabic'], 14, "bold")
        )
        movements_report_btn.grid(row=0, column=1, padx=20, pady=20)

        # تقرير الأصناف تحت الحد الأدنى
        low_stock_report_btn = ctk.CTkButton(
            reports_grid,
            text="⚠️ تقرير الأصناف تحت الحد الأدنى\nالأصناف التي تحتاج إعادة طلب",
            command=self.generate_low_stock_report,
            fg_color=MODERN_COLORS['warning'],
            width=300,
            height=80,
            font=(FONTS['arabic'], 14, "bold")
        )
        low_stock_report_btn.grid(row=1, column=0, padx=20, pady=20)

        # تقرير قيمة المخزون
        value_report_btn = ctk.CTkButton(
            reports_grid,
            text="💰 تقرير قيمة المخزون\nالقيمة الإجمالية للمخزون",
            command=self.generate_value_report,
            fg_color=MODERN_COLORS['info'],
            width=300,
            height=80,
            font=(FONTS['arabic'], 14, "bold")
        )
        value_report_btn.grid(row=1, column=1, padx=20, pady=20)

        # تقرير حركات صنف معين
        item_movements_btn = ctk.CTkButton(
            reports_grid,
            text="📦 تقرير حركات صنف معين\nتتبع حركات صنف محدد",
            command=self.generate_item_movements_report,
            fg_color=MODERN_COLORS['primary'],
            width=300,
            height=80,
            font=(FONTS['arabic'], 14, "bold")
        )
        item_movements_btn.grid(row=2, column=0, padx=20, pady=20)

        # تقرير مخزون مخزن معين
        warehouse_stock_btn = ctk.CTkButton(
            reports_grid,
            text="🏪 تقرير مخزون مخزن معين\nأرصدة مخزن محدد",
            command=self.generate_warehouse_stock_report,
            fg_color=MODERN_COLORS['error'],
            width=300,
            height=80,
            font=(FONTS['arabic'], 14, "bold")
        )
        warehouse_stock_btn.grid(row=2, column=1, padx=20, pady=20)

        # إعداد الشبكة
        reports_grid.grid_columnconfigure(0, weight=1)
        reports_grid.grid_columnconfigure(1, weight=1)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.load_items_data()
        self.load_warehouses_data()
        self.load_stock_movements()
        self.load_stock_balance()
        self.update_statistics()

    def load_items_data(self):
        """تحميل بيانات الأصناف"""
        try:
            if self.db_manager:
                query = "SELECT id, item_code, name_ar FROM items WHERE is_active = 1"
                self.items_data = self.db_manager.fetch_all(query)
            else:
                # بيانات وهمية
                self.items_data = [
                    (1, "ITM001", "لابتوب ديل"),
                    (2, "ITM002", "قميص قطني"),
                    (3, "ITM003", "قهوة عربية"),
                    (4, "ITM004", "دفتر ملاحظات"),
                    (5, "ITM005", "زيت محرك 5W30")
                ]
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الأصناف: {e}")

    def load_warehouses_data(self):
        """تحميل بيانات المخازن"""
        try:
            if self.db_manager:
                query = "SELECT id, warehouse_code, name FROM warehouses WHERE is_active = 1"
                self.warehouses_data = self.db_manager.fetch_all(query)
            else:
                # بيانات وهمية
                self.warehouses_data = [
                    (1, "WH001", "المخزن الرئيسي"),
                    (2, "WH002", "مخزن الفرع الأول"),
                    (3, "WH003", "مخزن الفرع الثاني"),
                    (4, "WH004", "مخزن المواد الخام"),
                    (5, "WH005", "مخزن المنتجات الجاهزة")
                ]

            # تحديث فلتر المخازن
            warehouse_names = ["جميع المخازن"] + [f"{wh[2]} ({wh[1]})" for wh in self.warehouses_data]
            self.warehouse_filter.configure(values=warehouse_names)

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات المخازن: {e}")

    def load_stock_movements(self):
        """تحميل حركات المخزون"""
        try:
            if self.db_manager:
                query = """
                SELECT sm.id, sm.date, i.name_ar, w.name, sm.movement_type,
                        sm.quantity, sm.cost_price, sm.reference_no, sm.notes
                FROM stock_movements sm
                JOIN items i ON sm.item_id = i.id
                JOIN warehouses w ON sm.warehouse_id = w.id
                ORDER BY sm.date DESC, sm.id DESC
                LIMIT 1000
                """
                self.stock_movements_data = self.db_manager.fetch_all(query)
            else:
                # بيانات وهمية
                self.stock_movements_data = self.get_sample_movements_data()

            self.populate_movements_table()

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل حركات المخزون: {e}")

    def load_stock_balance(self):
        """تحميل أرصدة المخزون"""
        try:
            if self.db_manager:
                query = """
                SELECT sb.item_id, i.item_code, i.name_ar, w.name, sb.quantity,
                        u.symbol, (sb.quantity * i.cost_price) as cost_value,
                        CASE
                            WHEN sb.quantity <= i.min_stock THEN 'تحت الحد الأدنى'
                            WHEN sb.quantity >= i.max_stock THEN 'فوق الحد الأقصى'
                            ELSE 'طبيعي'
                        END as status
                FROM stock_balance sb
                JOIN items i ON sb.item_id = i.id
                JOIN warehouses w ON sb.warehouse_id = w.id
                LEFT JOIN units u ON i.unit_id = u.id
                WHERE sb.quantity > 0
                ORDER BY i.name_ar
                """
                self.stock_balance_data = self.db_manager.fetch_all(query)
            else:
                # بيانات وهمية
                self.stock_balance_data = self.get_sample_balance_data()

            self.populate_balance_table()

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل أرصدة المخزون: {e}")

    def get_sample_movements_data(self):
        """بيانات وهمية لحركات المخزون"""
        return [
            (1, "2024-01-20", "لابتوب ديل", "المخزن الرئيسي", "إدخال", 10, 2500.00, "PO-001", "شراء جديد"),
            (2, "2024-01-20", "قميص قطني", "مخزن الفرع الأول", "إدخال", 50, 50.00, "PO-002", "تجديد المخزون"),
            (3, "2024-01-21", "لابتوب ديل", "المخزن الرئيسي", "إخراج", 2, 2500.00, "SO-001", "بيع للعميل"),
            (4, "2024-01-21", "قهوة عربية", "المخزن الرئيسي", "إدخال", 100, 80.00, "PO-003", "شراء بالجملة"),
            (5, "2024-01-22", "قميص قطني", "مخزن الفرع الأول", "إخراج", 5, 50.00, "SO-002", "مبيعات الفرع"),
            (6, "2024-01-22", "زيت محرك 5W30", "مخزن المواد الخام", "إدخال", 200, 22.50, "PO-004", "شحنة جديدة"),
            (7, "2024-01-23", "دفتر ملاحظات", "المخزن الرئيسي", "تسوية", 5, 15.00, "ADJ-001", "تسوية جرد"),
            (8, "2024-01-23", "لابتوب ديل", "المخزن الرئيسي", "تحويل", 3, 2500.00, "TR-001", "تحويل للفرع"),
        ]

    def get_sample_balance_data(self):
        """بيانات وهمية لأرصدة المخزون"""
        return [
            (1, "ITM001", "لابتوب ديل", "المخزن الرئيسي", 8, "قطعة", 20000.00, "طبيعي"),
            (2, "ITM002", "قميص قطني", "مخزن الفرع الأول", 45, "قطعة", 2250.00, "طبيعي"),
            (3, "ITM003", "قهوة عربية", "المخزن الرئيسي", 100, "كجم", 8000.00, "طبيعي"),
            (4, "ITM004", "دفتر ملاحظات", "المخزن الرئيسي", 55, "قطعة", 825.00, "طبيعي"),
            (5, "ITM005", "زيت محرك 5W30", "مخزن المواد الخام", 200, "لتر", 4500.00, "طبيعي"),
            (1, "ITM001", "لابتوب ديل", "مخزن الفرع الأول", 3, "قطعة", 7500.00, "تحت الحد الأدنى"),
        ]

    def populate_movements_table(self):
        """ملء جدول حركات المخزون"""
        # مسح البيانات السابقة
        for item in self.movements_tree.get_children():
            self.movements_tree.delete(item)

        # إضافة البيانات الجديدة
        for movement in self.stock_movements_data:
            # تنسيق نوع الحركة
            movement_type_ar = {
                "in": "إدخال",
                "out": "إخراج",
                "transfer": "تحويل",
                "adjust": "تسوية"
            }.get(movement[4], movement[4])

            self.movements_tree.insert("", "end", values=(
                movement[0],  # id
                movement[1],  # date
                movement[2],  # item
                movement[3],  # warehouse
                movement_type_ar,  # type
                f"{movement[5]:.2f}",  # quantity
                f"{movement[6]:.2f}",  # cost_price
                movement[7],  # reference_no
                movement[8]   # notes
            ))

    def populate_balance_table(self):
        """ملء جدول أرصدة المخزون"""
        # مسح البيانات السابقة
        for item in self.balance_tree.get_children():
            self.balance_tree.delete(item)

        # إضافة البيانات الجديدة
        for balance in self.stock_balance_data:
            item_id = self.balance_tree.insert("", "end", values=(
                balance[0],  # item_id
                balance[1],  # item_code
                balance[2],  # item_name
                balance[3],  # warehouse
                f"{balance[4]:.2f}",  # quantity
                balance[5],  # unit
                f"{balance[6]:.2f}",  # cost_value
                balance[7]   # status
            ))

            # تلوين الصفوف حسب الحالة
            if balance[7] == "تحت الحد الأدنى":
                self.balance_tree.set(item_id, "status", balance[7])
            elif balance[7] == "فوق الحد الأقصى":
                self.balance_tree.set(item_id, "status", balance[7])

    def update_statistics(self):
        """تحديث الإحصائيات في الرأس"""
        total_movements = len(self.stock_movements_data)
        total_items = len(set(balance[0] for balance in self.stock_balance_data))
        low_stock_items = len([b for b in self.stock_balance_data if b[7] == "تحت الحد الأدنى"])

        self.total_movements_label.configure(text=f"إجمالي الحركات: {total_movements}")
        self.total_items_label.configure(text=f"إجمالي الأصناف: {total_items}")
        self.low_stock_label.configure(text=f"أصناف تحت الحد الأدنى: {low_stock_items}")

    def get_items_list(self):
        """الحصول على قائمة الأصناف للقائمة المنسدلة"""
        return [f"{item[2]} ({item[1]})" for item in self.items_data]

    def get_warehouses_list(self):
        """الحصول على قائمة المخازن للقائمة المنسدلة"""
        return [f"{wh[2]} ({wh[1]})" for wh in self.warehouses_data]

    def search_movements(self):
        """البحث في حركات المخزون"""
        try:
            start_date = self.start_date_entry.get()
            end_date = self.end_date_entry.get()
            movement_type = self.movement_type_filter.get()

            # تطبيق الفلاتر
            filtered_data = []
            for movement in self.stock_movements_data:
                # فلتر التاريخ
                if start_date and movement[1] < start_date:
                    continue
                if end_date and movement[1] > end_date:
                    continue

                # فلتر نوع الحركة
                if movement_type != "جميع الحركات":
                    type_mapping = {
                        "إدخال": "in",
                        "إخراج": "out",
                        "تحويل": "transfer",
                        "تسوية": "adjust"
                    }
                    if movement[4] != type_mapping.get(movement_type, movement_type):
                        continue

                filtered_data.append(movement)

            # عرض النتائج المفلترة
            self.display_filtered_movements(filtered_data)

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {e}")

    def display_filtered_movements(self, filtered_data):
        """عرض حركات المخزون المفلترة"""
        # مسح البيانات السابقة
        for item in self.movements_tree.get_children():
            self.movements_tree.delete(item)

        # إضافة البيانات المفلترة
        for movement in filtered_data:
            movement_type_ar = {
                "in": "إدخال",
                "out": "إخراج",
                "transfer": "تحويل",
                "adjust": "تسوية"
            }.get(movement[4], movement[4])

            self.movements_tree.insert("", "end", values=(
                movement[0], movement[1], movement[2], movement[3],
                movement_type_ar, f"{movement[5]:.2f}", f"{movement[6]:.2f}",
                movement[7], movement[8]
            ))

    def refresh_movements(self):
        """تحديث حركات المخزون"""
        self.load_stock_movements()
        self.start_date_entry.delete(0, "end")
        self.end_date_entry.delete(0, "end")
        self.movement_type_filter.set("جميع الحركات")
        messagebox.showinfo("تحديث", "تم تحديث حركات المخزون بنجاح")

    def on_balance_search(self, event):
        """البحث في أرصدة المخزون"""
        search_term = self.balance_search_entry.get().lower()

        if search_term:
            filtered_data = []
            for balance in self.stock_balance_data:
                if (search_term in balance[1].lower() or  # item_code:
                    search_term in balance[2].lower()):   # item_name
                    filtered_data.append(balance)

            self.display_filtered_balance(filtered_data)
        else:
            self.populate_balance_table()

    def display_filtered_balance(self, filtered_data):
        """عرض أرصدة المخزون المفلترة"""
        # مسح البيانات السابقة
        for item in self.balance_tree.get_children():
            self.balance_tree.delete(item)

        # إضافة البيانات المفلترة
        for balance in filtered_data:
            item_id = self.balance_tree.insert("", "end", values=(
                balance[0], balance[1], balance[2], balance[3],
                f"{balance[4]:.2f}", balance[5], f"{balance[6]:.2f}", balance[7]
            ))

            # تلوين حسب الحالة
            if balance[7] == "تحت الحد الأدنى":
                self.balance_tree.set(item_id, "status", balance[7])

    def filter_by_warehouse(self, selected_warehouse):
        """فلترة الأرصدة حسب المخزن"""
        if selected_warehouse == "جميع المخازن":
            self.populate_balance_table()
        else:
            # استخراج اسم المخزن
            warehouse_name = selected_warehouse.split(" (")[0]
            filtered_data = [b for b in self.stock_balance_data if b[3] == warehouse_name]
            self.display_filtered_balance(filtered_data)

    def save_movement(self):
        """حفظ حركة مخزون جديدة"""
        try:
            # التحقق من البيانات
            if not self.validate_movement_form():
                return

            # جمع البيانات
            movement_data = self.get_movement_form_data()

            if self.db_manager:
                # حفظ في قاعدة البيانات
                query = """
                INSERT INTO stock_movements
                (item_id, warehouse_id, movement_type, quantity, cost_price,
                reference_no, date, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                params = (
                    movement_data['item_id'],
                    movement_data['warehouse_id'],
                    movement_data['movement_type'],
                    movement_data['quantity'],
                    movement_data['cost_price'],
                    movement_data['reference_no'],
                    movement_data['date'],
                    movement_data['notes'],
                    datetime.now()
                )

                self.db_manager.execute_query(query, params)

                # تحديث رصيد المخزون
                self.update_stock_balance(movement_data)

                messagebox.showinfo("نجح", "تم حفظ حركة المخزون بنجاح")
            else:
                # محاكاة الحفظ
                messagebox.showinfo("نجح", "تم حفظ حركة المخزون بنجاح (وضع الاختبار)")

            # تحديث البيانات ومسح النموذج
            self.load_stock_movements()
            self.load_stock_balance()
            self.clear_movement_form()
            self.update_statistics()

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ حركة المخزون: {e}")

    def validate_movement_form(self):
        """التحقق من صحة بيانات نموذج الحركة"""
        errors = []

        if not self.movement_movement_type_field.get():
            errors.append("نوع الحركة مطلوب")

        if not self.movement_item_field.get():
            errors.append("الصنف مطلوب")

        if not self.movement_warehouse_field.get():
            errors.append("المخزن مطلوب")

        try:
            quantity = float(self.movement_quantity_field.get() or 0)
            if quantity <= 0:
                errors.append("الكمية يجب أن تكون أكبر من صفر")
        except ValueError:
            pass
        except ValueError:
            errors.append("الكمية يجب أن تكون رقماً صحيحاً")

        try:
            cost_price = float(self.movement_cost_price_field.get() or 0)
            if cost_price < 0:
                errors.append("سعر التكلفة يجب أن يكون أكبر من أو يساوي صفر")
        except ValueError:
            pass
        except ValueError:
            errors.append("سعر التكلفة يجب أن يكون رقماً صحيحاً")

        if errors:
            messagebox.showerror("خطأ في البيانات", "\n".join(errors))
            return False

        return True

    def get_movement_form_data(self):
        """جمع بيانات نموذج الحركة"""
        # تحويل أسماء الأصناف والمخازن إلى معرفات
        item_text = self.movement_item_field.get()
        warehouse_text = self.movement_warehouse_field.get()

        # استخراج المعرفات من النصوص
        item_id = None
        for item in self.items_data:
            if f"{item[2]} ({item[1]})" == item_text:
                item_id = item[0]
                break

        warehouse_id = None
        for warehouse in self.warehouses_data:
            if f"{warehouse[2]} ({warehouse[1]})" == warehouse_text:
                warehouse_id = warehouse[0]
                break

        # تحويل نوع الحركة
        movement_type_mapping = {
            "إدخال": "in",
            "إخراج": "out",
            "تحويل": "transfer",
            "تسوية": "adjust"
        }

        return {
            'item_id': item_id,
            'warehouse_id': warehouse_id,
            'movement_type': movement_type_mapping.get(self.movement_movement_type_field.get()),
            'quantity': float(self.movement_quantity_field.get()),
            'cost_price': float(self.movement_cost_price_field.get() or 0),
            'reference_no': self.movement_reference_no_field.get(),
            'date': self.movement_movement_date_field.get(),
            'notes': self.movement_notes_field.get("1.0", "end-1c")
        }

    def update_stock_balance(self, movement_data):
        """تحديث رصيد المخزون بناءً على الحركة"""
        try:
            if not self.db_manager:
                return

            item_id = movement_data['item_id']
            warehouse_id = movement_data['warehouse_id']
            quantity = movement_data['quantity']
            movement_type = movement_data['movement_type']

            # تحديد اتجاه الحركة
            if movement_type in ['in', 'adjust']:
                quantity_change = quantity
            elif movement_type == 'out':
                quantity_change = -quantity
            else:  # transfer:
                quantity_change = 0  # يحتاج معالجة خاصة

            # تحديث أو إدراج رصيد المخزون
            query_check = """
            SELECT quantity FROM stock_balance
            WHERE item_id = ? AND warehouse_id = ?
            """

            existing_balance = self.db_manager.fetch_one(query_check, (item_id, warehouse_id))

            if existing_balance:
                # تحديث الرصيد الموجود
                new_quantity = existing_balance[0] + quantity_change
                query_update = """
                UPDATE stock_balance
                SET quantity = ?, last_updated = ?
                WHERE item_id = ? AND warehouse_id = ?
                """
                self.db_manager.execute_query(query_update, (new_quantity, datetime.now(), item_id, warehouse_id))
            else:
                # إدراج رصيد جديد
                query_insert = """
                INSERT INTO stock_balance (item_id, warehouse_id, quantity, last_updated)
                VALUES (?, ?, ?, ?)
                """
                self.db_manager.execute_query(query_insert, (item_id, warehouse_id, quantity_change, datetime.now()))

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث رصيد المخزون: {e}")

    def clear_movement_form(self):
        """مسح نموذج الحركة"""
        self.movement_movement_type_field.set("")
        self.movement_item_field.set("")
        self.movement_warehouse_field.set("")
        self.movement_quantity_field.delete(0, "end")
        self.movement_cost_price_field.delete(0, "end")
        self.movement_reference_no_field.delete(0, "end")
        self.movement_movement_date_field.delete(0, "end")
        self.movement_movement_date_field.insert(0, date.today().strftime("%Y-%m-%d"))
        self.movement_notes_field.delete("1.0", "end")

    def on_movement_select(self, event):
        """عند تحديد حركة من الجدول"""
        selected_item = self.movements_tree.selection()
        if selected_item:
            item_values = self.movements_tree.item(selected_item[0])['values']
            self.current_movement_id = item_values[0]

    def on_movement_double_click(self, event):
        """عند النقر المزدوج على حركة"""
        self.show_movement_details()

    def on_balance_double_click(self, event):
        """عند النقر المزدوج على رصيد"""
        self.show_item_movements()

    def show_movement_details(self):
        """عرض تفاصيل الحركة"""
        if not self.current_movement_id:
            return

        # إنشاء نافذة التفاصيل
        details_window = ctk.CTkToplevel(self.window)
        details_window.title("تفاصيل حركة المخزون")
        details_window
        details_window.configure(fg_color=MODERN_COLORS['background'])

        # العثور على بيانات الحركة
        movement_data = None
        for movement in self.stock_movements_data:
            if movement[0] == self.current_movement_id:
                movement_data = movement
                break

        if movement_data:
            # عرض التفاصيل
            details_frame = ctk.CTkScrollableFrame(details_window)
            details_frame.pack(fill="both", expand=True, padx=20, pady=20)

            details = [
                ("رقم الحركة:", movement_data[0]),
                ("التاريخ:", movement_data[1]),
                ("الصنف:", movement_data[2]),
                ("المخزن:", movement_data[3]),
                ("نوع الحركة:", movement_data[4]),
                ("الكمية:", f"{movement_data[5]:.2f}"),
                ("سعر التكلفة:", f"{movement_data[6]:.2f} ريال"),
                ("رقم المرجع:", movement_data[7]),
                ("ملاحظات:", movement_data[8])
            ]

            for label, value in details:
                detail_frame = ctk.CTkFrame(details_frame, fg_color="transparent")
                detail_frame.pack(fill="x", padx=10, pady=5)

                ctk.CTkLabel(detail_frame, text=str(value), font=(FONTS['arabic'], 12)).pack(side="left", padx=10)
                ctk.CTkLabel(detail_frame, text=label, font=(FONTS['arabic'], 12, "bold")).pack(side="right", padx=10)

    def show_item_movements(self):
        """عرض حركات صنف معين"""
        selected_item = self.balance_tree.selection()
        if not selected_item:
            return

        item_values = self.balance_tree.item(selected_item[0])['values']
        item_id = item_values[0]
        item_name = item_values[2]

        # إنشاء نافذة حركات الصنف
        movements_window = ctk.CTkToplevel(self.window)
        movements_window.title(f"حركات الصنف: {item_name}")
        movements_window
        movements_window.configure(fg_color=MODERN_COLORS['background'])

        # جدول حركات الصنف
        movements_frame = ctk.CTkFrame(movements_window, fg_color="white")
        movements_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # إنشاء Treeview
        item_movements_tree = ttk.Treeview(
            movements_frame,
            columns=("date", "warehouse", "type", "quantity", "cost_price", "reference"),
            show="headings",
            height=20
        )

        # رؤوس الأعمدة
        headers = {
            "date": "التاريخ",
            "warehouse": "المخزن",
            "type": "نوع الحركة",
            "quantity": "الكمية",
            "cost_price": "السعر",
            "reference": "المرجع"
        }

        for col, header in headers.items():
            item_movements_tree.heading(col, text=header, anchor="center")
            item_movements_tree.column(col, width=120, anchor="center")

        # تصفية حركات الصنف
        item_movements = [m for m in self.stock_movements_data if m[2] == item_name]

        for movement in item_movements:
            item_movements_tree.insert("", "end", values=(
                movement[1], movement[3], movement[4],
                f"{movement[5]:.2f}", f"{movement[6]:.2f}", movement[7]
            ))

        item_movements_tree.pack(fill="both", expand=True)

    # وظائف التقارير
    def generate_balance_report(self):
        """إنشاء تقرير الأرصدة الحالية"""
        messagebox.showinfo("تقرير الأرصدة", "سيتم إنشاء تقرير الأرصدة الحالية")

    def generate_movements_report(self):
        """إنشاء تقرير حركات المخزون"""
        messagebox.showinfo("تقرير الحركات", "سيتم إنشاء تقرير حركات المخزون")

    def generate_low_stock_report(self):
        """إنشاء تقرير الأصناف تحت الحد الأدنى"""
        low_stock_items = [b for b in self.stock_balance_data if b[7] == "تحت الحد الأدنى"]

        if not low_stock_items:
            messagebox.showinfo("تقرير الحد الأدنى", "لا توجد أصناف تحت الحد الأدنى")
            return

        # إنشاء نافذة التقرير
        report_window = ctk.CTkToplevel(self.window)
        report_window.title("تقرير الأصناف تحت الحد الأدنى")
        report_window
        report_window.configure(fg_color=MODERN_COLORS['background'])

        # عنوان التقرير
        title_label = ctk.CTkLabel(
            report_window,
            text="⚠️ الأصناف تحت الحد الأدنى",
            font=(FONTS['arabic'], 20, "bold"),
            text_color=MODERN_COLORS['error']
        )
        title_label.pack(pady=20)

        # جدول الأصناف
        report_frame = ctk.CTkFrame(report_window, fg_color="white")
        report_frame.pack(fill="both", expand=True, padx=20, pady=20)

        report_tree = ttk.Treeview(
            report_frame,
            columns=("item_code", "item_name", "warehouse", "current_qty", "min_qty"),
            show="headings",
            height=15
        )

        # رؤوس الأعمدة
        report_headers = {
            "item_code": "كود الصنف",
            "item_name": "اسم الصنف",
            "warehouse": "المخزن",
            "current_qty": "الكمية الحالية",
            "min_qty": "الحد الأدنى"
        }

        for col, header in report_headers.items():
            report_tree.heading(col, text=header, anchor="center")
            report_tree.column(col, width=150, anchor="center")

        # إضافة البيانات
        for item in low_stock_items:
            report_tree.insert("", "end", values=(
                item[1], item[2], item[3], f"{item[4]:.2f}", "غير محدد"
            ))

        report_tree.pack(fill="both", expand=True)

    def generate_value_report(self):
        """إنشاء تقرير قيمة المخزون"""
        total_value = sum(balance[6] for balance in self.stock_balance_data)
        messagebox.showinfo("قيمة المخزون", f"إجمالي قيمة المخزون: {total_value:.2f} ريال")

    def generate_item_movements_report(self):
        """إنشاء تقرير حركات صنف معين"""
        messagebox.showinfo("تقرير حركات صنف", "يرجى تحديد صنف من جدول الأرصدة والنقر مزدوجاً عليه")

    def generate_warehouse_stock_report(self):
        """إنشاء تقرير مخزون مخزن معين"""
        messagebox.showinfo("تقرير مخزون مخزن", "استخدم فلتر المخزن في تبويب الأرصدة")

    def export_balance_report(self):
        """تصدير تقرير الأرصدة"""
        try:
            from tkinter import filedialog
            import csv

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ تقرير الأرصدة"
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة الرؤوس
                    writer.writerow(["كود الصنف", "اسم الصنف", "المخزن", "الكمية", "الوحدة", "قيمة المخزون", "الحالة"])

                    # كتابة البيانات
                    for balance in self.stock_balance_data:
                        writer.writerow([
                            balance[1], balance[2], balance[3], balance[4],
                            balance[5], balance[6], balance[7]
                        ])

                messagebox.showinfo("تصدير", f"تم تصدير التقرير إلى: {filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير التقرير: {e}")

    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()

    # دوال الأزرار الجانبية
    def switch_to_new_movement(self):
        """التبديل إلى تبويب إضافة حركة جديدة"""
        self.notebook.select(2)  # تبويب إضافة حركة (الفهرس 2)

    def focus_search(self):
        """التركيز على حقل البحث في التبويب الحالي"""
        current_tab = self.notebook.index(self.notebook.select())
        if current_tab == 0:  # تبويب حركات المخزون:
            self.notebook.select(0)
            # يمكن إضافة تركيز على حقل البحث هنا
        elif current_tab == 1:  # تبويب الأرصدة:
            self.notebook.select(1)
            if hasattr(self, 'balance_search_entry'):
                self.balance_search_entry.focus()

    def switch_to_balance(self):
        """التبديل إلى تبويب الأرصدة"""
        self.notebook.select(1)  # تبويب الأرصدة (الفهرس 1)

    def switch_to_reports(self):
        """التبديل إلى تبويب التقارير"""
        self.notebook.select(3)  # تبويب التقارير (الفهرس 3)

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        try:
            self.load_stock_movements()
            self.load_stock_balance()
            messagebox.showinfo("تحديث", "تم تحديث جميع البيانات بنجاح")
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث البيانات: {e}")

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            messagebox.showinfo("تصدير", "سيتم تصدير البيانات إلى Excel قريباً")
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التصدير: {e}")

    def export_to_csv(self):
        """تصدير البيانات إلى CSV"""
        try:
            messagebox.showinfo("تصدير", "سيتم تصدير البيانات إلى CSV قريباً")
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التصدير: {e}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            messagebox.showinfo("طباعة", "سيتم تفعيل الطباعة قريباً")
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الطباعة: {e}")

    def show_stock_alerts(self):
        """عرض تنبيهات المخزون"""
        try:
            messagebox.showinfo("تنبيهات", "سيتم عرض تنبيهات المخزون قريباً")
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض التنبيهات: {e}")

    def manage_items(self):
        """إدارة الأصناف"""
        try:
            messagebox.showinfo("إدارة الأصناف", "سيتم فتح نافذة إدارة الأصناف قريباً")
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح إدارة الأصناف: {e}")

    def manage_warehouses(self):
        """إدارة المخازن"""
        try:
            messagebox.showinfo("إدارة المخازن", "سيتم فتح نافذة إدارة المخازن قريباً")
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح إدارة المخازن: {e}")

    def show_settings(self):
        """عرض الإعدادات"""
        try:
            messagebox.showinfo("الإعدادات", "سيتم فتح نافذة الإعدادات قريباً")
        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح الإعدادات: {e}")
