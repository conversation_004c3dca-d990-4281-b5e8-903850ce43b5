# -*- coding: utf-8 -*-
"""
اختبار نافذة إدخال الأصناف
Test Item Entry Window
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    
    from windows.item_entry_window import ItemEntryWindow
    
    def test_item_entry_window():
        """اختبار نافذة إدخال الأصناف"""
        app = QApplication(sys.argv)
        
        # تطبيق الخط العربي
        font = QFont("Cairo", 10)
        if not font.exactMatch():
            font = QFont("Arial", 10)
        app.setFont(font)
        
        # إنشاء النافذة
        window = ItemEntryWindow()
        window.show()
        
        print("✅ تم إنشاء نافذة إدخال الأصناف بنجاح")
        print("📋 المكونات المتاحة:")
        print("   - نموذج إدخال البيانات الأساسية")
        print("   - قسم الأسعار والتكلفة")
        print("   - تحميل الصور")
        print("   - التحقق من صحة البيانات")
        print("   - توليد رموز الأصناف تلقائياً")
        print("   - حفظ واسترجاع البيانات")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
    
    if __name__ == "__main__":
        test_item_entry_window()

except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من تثبيت PyQt5:")
    print("pip install PyQt5")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
