/* نمط داكن لنافذة إدخال الأصناف */
/* Dark Theme for Item Entry Window */

/* النافذة الرئيسية */
QMainWindow {
    background-color: #2c3e50;
    color: #ecf0f1;
    font-family: "Cairo", "Segoe UI", Arial, sans-serif;
    font-size: 11px;
}

/* الإطارات */
QFrame {
    background-color: #34495e;
    border: 1px solid #4a5f7a;
    border-radius: 8px;
    padding: 10px;
}

QFrame#headerFrame {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                stop: 0 #1abc9c, stop: 1 #16a085);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 15px;
}

QFrame#buttonFrame {
    background-color: #2c3e50;
    border: none;
    padding: 15px;
}

/* المجموعات */
QGroupBox {
    font-weight: bold;
    font-size: 12px;
    color: #ecf0f1;
    border: 2px solid #5d6d7e;
    border-radius: 10px;
    margin-top: 10px;
    padding-top: 15px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 15px;
    padding: 0 8px 0 8px;
    background-color: #34495e;
    color: #1abc9c;
}

QGroupBox#basicGroup {
    border-color: #3498db;
}

QGroupBox#priceGroup {
    border-color: #27ae60;
}

QGroupBox#imageGroup {
    border-color: #e67e22;
}

/* حقول النص */
QLineEdit {
    background-color: #34495e;
    border: 2px solid #5d6d7e;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 11px;
    color: #ecf0f1;
}

QLineEdit:focus {
    border-color: #1abc9c;
    background-color: #3c5a78;
}

QLineEdit:hover {
    border-color: #7fb3d3;
}

QLineEdit[readOnly="true"] {
    background-color: #2c3e50;
    color: #95a5a6;
}

/* منطقة النص */
QTextEdit {
    background-color: #34495e;
    border: 2px solid #5d6d7e;
    border-radius: 6px;
    padding: 8px;
    font-size: 11px;
    color: #ecf0f1;
}

QTextEdit:focus {
    border-color: #1abc9c;
}

/* القوائم المنسدلة */
QComboBox {
    background-color: #34495e;
    border: 2px solid #5d6d7e;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 11px;
    color: #ecf0f1;
    min-width: 120px;
}

QComboBox:hover {
    border-color: #7fb3d3;
}

QComboBox:focus {
    border-color: #1abc9c;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #5d6d7e;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background-color: #2c3e50;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iI2VjZjBmMSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    width: 12px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: #34495e;
    border: 1px solid #5d6d7e;
    border-radius: 6px;
    selection-background-color: #1abc9c;
    selection-color: white;
    padding: 4px;
    color: #ecf0f1;
}

/* صناديق الأرقام */
QSpinBox, QDoubleSpinBox {
    background-color: #34495e;
    border: 2px solid #5d6d7e;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 11px;
    color: #ecf0f1;
    min-width: 100px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #1abc9c;
}

QSpinBox:hover, QDoubleSpinBox:hover {
    border-color: #7fb3d3;
}

QSpinBox#priceSpinBox, QDoubleSpinBox#priceSpinBox {
    border-color: #27ae60;
    font-weight: bold;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    subcontrol-origin: border;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #5d6d7e;
    border-top-right-radius: 6px;
    background-color: #2c3e50;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    subcontrol-origin: border;
    subcontrol-position: bottom right;
    width: 20px;
    border-left: 1px solid #5d6d7e;
    border-bottom-right-radius: 6px;
    background-color: #2c3e50;
}

/* الأزرار */
QPushButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #1abc9c, stop: 1 #16a085);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    font-size: 11px;
    padding: 12px 24px;
    min-width: 100px;
}

QPushButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #48c9b0, stop: 1 #1abc9c);
}

QPushButton:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #16a085, stop: 1 #138d75);
}

QPushButton#saveButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #27ae60, stop: 1 #229954);
}

QPushButton#saveButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #58d68d, stop: 1 #27ae60);
}

QPushButton#cancelButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #e74c3c, stop: 1 #c0392b);
}

QPushButton#cancelButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #ec7063, stop: 1 #e74c3c);
}

QPushButton#clearButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f39c12, stop: 1 #e67e22);
}

QPushButton#clearButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f7dc6f, stop: 1 #f39c12);
}

QPushButton#uploadButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #9b59b6, stop: 1 #8e44ad);
}

QPushButton#uploadButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #bb8fce, stop: 1 #9b59b6);
}

QPushButton#themeButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f39c12, stop: 1 #e67e22);
    border-radius: 20px;
    padding: 8px;
    min-width: 40px;
}

/* التسميات */
QLabel {
    color: #ecf0f1;
    font-size: 11px;
}

QLabel#titleLabel {
    font-size: 18px;
    font-weight: bold;
    color: white;
}

QLabel#subtitleLabel {
    font-size: 12px;
    color: #d5dbdb;
}

QLabel#profitLabel {
    font-size: 12px;
    font-weight: bold;
    color: #27ae60;
}

QLabel#errorLabel {
    color: #e74c3c;
    font-size: 10px;
    font-style: italic;
}

QLabel#imageLabel {
    border: 2px dashed #5d6d7e;
    border-radius: 8px;
    background-color: #2c3e50;
    color: #95a5a6;
    text-align: center;
    padding: 20px;
    font-size: 14px;
}

QLabel#imageLabel:hover {
    border-color: #7fb3d3;
    background-color: #34495e;
}

/* شريط الحالة */
QStatusBar {
    background-color: #1b2631;
    color: #ecf0f1;
    border-top: 1px solid #34495e;
    padding: 5px;
}

/* شريط التمرير */
QScrollBar:vertical {
    background-color: #2c3e50;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #5d6d7e;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #7fb3d3;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

/* منطقة التمرير */
QScrollArea {
    border: none;
    background-color: transparent;
}

/* تخطيط النموذج */
QFormLayout {
    spacing: 10px;
}

/* تأثيرات الحركة والظلال */
QWidget {
    selection-background-color: #1abc9c;
    selection-color: white;
}

/* تخصيص للعناصر المحددة */
QComboBox#taxCombo {
    border-color: #e67e22;
}

QLineEdit#itemCodeEdit {
    font-family: "Courier New", monospace;
    font-weight: bold;
}

/* تحسينات إضافية */
QWidget:focus {
    outline: none;
}

QGroupBox:focus {
    border-color: #1abc9c;
}
