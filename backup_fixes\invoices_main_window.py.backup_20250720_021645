# -*- coding: utf-8 -*-
"""
نافذة الفواتير الرئيسية - وحدة متكاملة لإدارة جميع أنواع الفواتير
"""

import os
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, date
import customtkinter as ctk

from themes.modern_theme import MODERN_COLORS, FONTS, DIMENSIONS
from ui.window_utils import configure_window_fullscreen


class InvoicesMainWindow:
    """نافذة الفواتير الرئيسية مع Dashboard متكامل"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
        self.current_user = getattr(parent, 'current_user', {'role': 'admin', 'username': 'admin'})
        
        # إحصائيات ديناميكية
        self.stats = {
            'today_invoices': 0,
            'today_sales': 0.0,
            'today_returns': 0.0,
            'active_user': self.current_user.get('username', 'غير محدد')
        }
        
        self.create_window()
        
    def create_window(self):
        """إنشاء النافذة الرئيسية"""
        self.window = ctk.CTkToplevel()
        self.window.title("🧾 وحدة الفواتير المتكاملة - برنامج ست الكل للمحاسبة")
        
        # تكوين النافذة
        configure_window_fullscreen(self.window)
        self.window.configure(fg_color=MODERN_COLORS['background'])
        
        # إعداد الخط العربي
        arabic_font = ("Cairo", 14)
        
        # إنشاء الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء الهيدر
        self.create_header(main_frame)
        
        # إنشاء المحتوى الرئيسي
        content_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, pady=(10, 0))
        
        # إنشاء الشريط الجانبي والمحتوى
        self.create_sidebar_and_content(content_frame)
        
        # تحديث الإحصائيات
        self.update_stats()
        
    def create_header(self, parent):
        """إنشاء هيدر النافذة مع العنوان والإحصائيات"""
        header_frame = ctk.CTkFrame(parent, height=120, fg_color=MODERN_COLORS['primary'])
        header_frame.pack(fill="x", pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="right", fill="y", padx=20, pady=10)
        
        main_title = ctk.CTkLabel(
            title_frame,
            text="🧾 وحدة الفواتير المتكاملة",
            font=("Cairo", 24, "bold"),
            text_color="white"
        )
        main_title.pack(anchor="e", pady=(5, 0))
        
        subtitle = ctk.CTkLabel(
            title_frame,
            text="إدارة شاملة لجميع أنواع الفواتير والمبيعات",
            font=("Cairo", 14),
            text_color="white"
        )
        subtitle.pack(anchor="e")
        
        # كروت الإحصائيات
        self.create_stats_cards(header_frame)
        
    def create_stats_cards(self, parent):
        """إنشاء كروت الإحصائيات الديناميكية"""
        stats_frame = ctk.CTkFrame(parent, fg_color="transparent")
        stats_frame.pack(side="left", fill="both", expand=True, padx=20, pady=10)
        
        # إطار الكروت
        cards_frame = ctk.CTkFrame(stats_frame, fg_color="transparent")
        cards_frame.pack(fill="both", expand=True)
        
        # كرت عدد الفواتير اليوم
        self.invoices_card = self.create_stat_card(
            cards_frame, "📊", "فواتير اليوم", str(self.stats['today_invoices']), 
            MODERN_COLORS['success'], 0, 0
        )
        
        # كرت قيمة المبيعات
        self.sales_card = self.create_stat_card(
            cards_frame, "💰", "المبيعات", f"{self.stats['today_sales']:.2f} ر.س", 
            MODERN_COLORS['info'], 0, 1
        )
        
        # كرت المرتجعات
        self.returns_card = self.create_stat_card(
            cards_frame, "↩️", "المرتجعات", f"{self.stats['today_returns']:.2f} ر.س", 
            MODERN_COLORS['warning'], 1, 0
        )
        
        # كرت المستخدم النشط
        self.user_card = self.create_stat_card(
            cards_frame, "👤", "المستخدم النشط", self.stats['active_user'], 
            MODERN_COLORS['secondary'], 1, 1
        )
        
    def create_stat_card(self, parent, icon, title, value, color, row, col):
        """إنشاء كرت إحصائية واحد"""
        card = ctk.CTkFrame(parent, fg_color="white", corner_radius=10)
        card.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
        parent.grid_columnconfigure(col, weight=1)
        
        # الأيقونة
        icon_label = ctk.CTkLabel(
            card, text=icon, font=("Arial", 20), 
            text_color=color, width=40
        )
        icon_label.pack(side="right", padx=10, pady=10)
        
        # النص
        text_frame = ctk.CTkFrame(card, fg_color="transparent")
        text_frame.pack(side="right", fill="both", expand=True, padx=10, pady=10)
        
        title_label = ctk.CTkLabel(
            text_frame, text=title, font=("Cairo", 12), 
            text_color=MODERN_COLORS['text_secondary']
        )
        title_label.pack(anchor="e")
        
        value_label = ctk.CTkLabel(
            text_frame, text=value, font=("Cairo", 16, "bold"), 
            text_color=MODERN_COLORS['text_primary']
        )
        value_label.pack(anchor="e")
        
        return card
        
    def create_sidebar_and_content(self, parent):
        """إنشاء الشريط الجانبي ومنطقة المحتوى"""
        # الشريط الجانبي
        sidebar = ctk.CTkFrame(parent, width=280, fg_color=MODERN_COLORS['surface'])
        sidebar.pack(side="right", fill="y", padx=(0, 10))
        sidebar.pack_propagate(False)
        
        # عنوان الشريط الجانبي
        sidebar_title = ctk.CTkLabel(
            sidebar, text="📋 أنواع الفواتير", 
            font=("Cairo", 18, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        sidebar_title.pack(pady=20)
        
        # أزرار أنواع الفواتير
        self.create_invoice_type_buttons(sidebar)
        
        # منطقة المحتوى الرئيسي
        self.content_area = ctk.CTkFrame(parent, fg_color=MODERN_COLORS['surface'])
        self.content_area.pack(side="left", fill="both", expand=True)
        
        # عرض الصفحة الرئيسية افتراضياً
        self.show_dashboard()

        # تحديث الإحصائيات كل 30 ثانية
        self.window.after(30000, self.auto_update_stats)
        
    def create_invoice_type_buttons(self, parent):
        """إنشاء أزرار أنواع الفواتير"""
        buttons_data = [
            ("🏠", "لوحة التحكم", self.show_dashboard, MODERN_COLORS['primary']),
            ("🧾", "فواتير البيع", self.show_sales_invoices, MODERN_COLORS['success']),
            ("🛒", "نقطة البيع POS", self.show_pos_system, MODERN_COLORS['info']),
            ("📦", "فواتير الشراء", self.show_purchase_invoices, MODERN_COLORS['warning']),
            ("↩️", "مرتجع البيع", self.show_sales_returns, MODERN_COLORS['error']),
            ("📤", "مرتجع الشراء", self.show_purchase_returns, MODERN_COLORS['secondary']),
            ("🎫", "فاتورة مختصرة", self.show_quick_invoice, "#9C27B0"),
            ("📊", "التقارير والتحليل", self.show_reports, MODERN_COLORS['primary']),
            ("🗑️", "سلة المحذوفات", self.show_deleted_invoices, "#795548")
        ]
        
        for icon, text, command, color in buttons_data:
            btn = ctk.CTkButton(
                parent,
                text=f"{icon} {text}",
                font=("Cairo", 14),
                fg_color=color,
                hover_color=self.get_hover_color(color),
                height=50,
                anchor="e",
                command=command
            )
            btn.pack(fill="x", padx=15, pady=5)
            
    def get_hover_color(self, color):
        """الحصول على لون التمرير"""
        # تحويل بسيط للون أغمق
        if color == MODERN_COLORS['primary']:
            return MODERN_COLORS['primary_dark']
        elif color == MODERN_COLORS['success']:
            return "#45a049"
        elif color == MODERN_COLORS['info']:
            return "#1976D2"
        elif color == MODERN_COLORS['warning']:
            return "#F57C00"
        elif color == MODERN_COLORS['error']:
            return "#D32F2F"
        else:
            return color
            
    def show_dashboard(self):
        """عرض لوحة التحكم الرئيسية"""
        self.clear_content_area()
        
        # عنوان لوحة التحكم
        title = ctk.CTkLabel(
            self.content_area,
            text="🏠 لوحة التحكم - نظرة عامة على الفواتير",
            font=("Cairo", 20, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=20)
        
        # رسالة ترحيب
        welcome_frame = ctk.CTkFrame(self.content_area, fg_color=MODERN_COLORS['primary'])
        welcome_frame.pack(fill="x", padx=20, pady=10)
        
        welcome_text = ctk.CTkLabel(
            welcome_frame,
            text="مرحباً بك في وحدة الفواتير المتكاملة\nاختر نوع الفاتورة من الشريط الجانبي للبدء",
            font=("Cairo", 16),
            text_color="white",
            justify="center"
        )
        welcome_text.pack(pady=20)
        
    def clear_content_area(self):
        """مسح منطقة المحتوى"""
        for widget in self.content_area.winfo_children():
            if widget and hasattr(widget, "destroy"):
    widget.destroy()
            
    def update_stats(self):
        """تحديث الإحصائيات (يمكن ربطها بقاعدة البيانات لاحقاً)"""
        try:
            # محاكاة بيانات من قاعدة البيانات
            import random
            from datetime import datetime

            # تحديث الإحصائيات بقيم عشوائية للعرض التوضيحي
            self.stats['today_invoices'] = random.randint(15, 50)
            self.stats['today_sales'] = random.uniform(5000, 25000)
            self.stats['today_returns'] = random.uniform(100, 1000)

            # تحديث الكروت إذا كانت موجودة
            if hasattr(self, 'invoices_card'):
                # تحديث كرت الفواتير
                for widget in self.invoices_card.winfo_children():
                    if isinstance(widget, ctk.CTkFrame):
                        for child in widget.winfo_children():
                            if isinstance(child, ctk.CTkLabel) and "bold" in str(child.cget("font")):
                                child.configure(text=str(self.stats['today_invoices']))
                                break

                # تحديث كرت المبيعات
                for widget in self.sales_card.winfo_children():
                    if isinstance(widget, ctk.CTkFrame):
                        for child in widget.winfo_children():
                            if isinstance(child, ctk.CTkLabel) and "bold" in str(child.cget("font")):
                                child.configure(text=f"{self.stats['today_sales']:.2f} ر.س")
                                break

                # تحديث كرت المرتجعات
                for widget in self.returns_card.winfo_children():
                    if isinstance(widget, ctk.CTkFrame):
                        for child in widget.winfo_children():
                            if isinstance(child, ctk.CTkLabel) and "bold" in str(child.cget("font")):
                                child.configure(text=f"{self.stats['today_returns']:.2f} ر.س")
                                break

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def auto_update_stats(self):
        """تحديث تلقائي للإحصائيات"""
        self.update_stats()
        # جدولة التحديث التالي
        self.window.after(30000, self.auto_update_stats)
        
    # دوال عرض أنواع الفواتير المختلفة
    def show_sales_invoices(self):
        """فتح نافذة فواتير البيع"""
        try:
            from ui.sales_invoice_window import SalesInvoiceWindow
            sales_window = SalesInvoiceWindow(self)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في فتح نافذة فواتير البيع: {str(e)}")
        
    def show_pos_system(self):
        """فتح نافذة نقطة البيع POS"""
        try:
            from ui.pos_system_window import POSSystemWindow
            pos_window = POSSystemWindow(self)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في فتح نافذة نقطة البيع: {str(e)}")
        
    def show_purchase_invoices(self):
        messagebox.showinfo("قريباً", "فواتير الشراء قيد التطوير")
        
    def show_sales_returns(self):
        messagebox.showinfo("قريباً", "مرتجع البيع قيد التطوير")
        
    def show_purchase_returns(self):
        messagebox.showinfo("قريباً", "مرتجع الشراء قيد التطوير")
        
    def show_quick_invoice(self):
        messagebox.showinfo("قريباً", "الفاتورة المختصرة قيد التطوير")
        
    def show_reports(self):
        """فتح نافذة التقارير والتحليل"""
        try:
            from ui.invoices_reports_window import InvoicesReportsWindow
            reports_window = InvoicesReportsWindow(self)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في فتح نافذة التقارير: {str(e)}")
        
    def show_deleted_invoices(self):
        messagebox.showinfo("قريباً", "سلة المحذوفات قيد التطوير")


def main():
    """تشغيل النافذة للاختبار"""
    root = ctk.CTk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    app = InvoicesMainWindow()
    root.mainloop()


if __name__ == "__main__":
    main()
