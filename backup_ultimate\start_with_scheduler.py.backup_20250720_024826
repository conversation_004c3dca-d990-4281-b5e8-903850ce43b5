#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل برنامج المحاسبة مع نظام الجدولة التلقائية
"""

import sys
import logging
import signal

# الحصول على مسار مجلد المشروع الحالي
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# متغير عام لمدير الجدولة
scheduler_manager = None

def signal_handler(signum, frame):
    """معالج إشارات النظام لإيقاف المجدول بأمان"""
    global scheduler_manager
    print("\n🛑 تم استلام إشارة إيقاف البرنامج...")
    
    if scheduler_manager:
        print("⏹️ إيقاف نظام الجدولة...")
        scheduler_manager.stop_scheduler()
        print("✅ تم إيقاف نظام الجدولة بأمان")
    
    print("👋 إنهاء البرنامج...")
    sys.exit(0)

def main():
    """
    نقطة البداية الرئيسية لتشغيل برنامج المحاسبة مع نظام الجدولة التلقائية
    """
    global scheduler_manager
    
    try:
        # تسجيل معالجات الإشارات
        signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # إنهاء العملية
        
        print("🚀 بدء تشغيل برنامج المحاسبة مع نظام الجدولة التلقائية")
        print("=" * 60)
        
        # تهيئة وبدء تشغيل نظام الجدولة التلقائية
        print("⚙️ تهيئة نظام الجدولة التلقائية...")
        from core.scheduler_manager import SchedulerManager
        scheduler_manager = SchedulerManager()
        scheduler_manager.start_scheduler()
        
        print("✅ تم تشغيل نظام الجدولة التلقائية بنجاح")
        print("📅 المهام المجدولة:")
        
        # عرض المهام المجدولة
        jobs = scheduler_manager.get_scheduled_jobs()
        for job in jobs:
            print(f"  - {job['name']}")
            print(f"    التشغيل التالي: {job['next_run']}")
            print()
        
        print("🖥️ بدء تشغيل واجهة البرنامج...")
        
        # استيراد وتشغيل التطبيق الرئيسي
        from ui.main_window import MainApplication
    except Exception as e:
        print(f"خطأ: {e}")
from pathlib import Path
import re
        
        # إنشاء مثيل من التطبيق الرئيسي 
        app = MainApplication()
        # تمرير مدير الجدولة للتطبيق
        app.scheduler_manager = scheduler_manager
        
        print("✅ تم تشغيل البرنامج بنجاح!")
        print("📋 الميزات المتاحة:")
        print("  - النسخ الاحتياطي التلقائي يومياً الساعة 11:00 مساءً")
        print("  - تنظيف النسخ الاحتياطية القديمة أسبوعياً")
        print("  - تنظيف ملفات السجلات القديمة شهرياً")
        print("  - جميع ميزات برنامج المحاسبة")
        print()
        
        # تشغيل التطبيق
        app.run()
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف البرنامج بواسطة المستخدم")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        # تسجيل رسالة الخطأ
        logging.error(f"خطأ في تشغيل البرنامج: {e}")
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        
        # إيقاف المجدول في حالة الخطأ
        if scheduler_manager:
            scheduler_manager.stop_scheduler()
        
        sys.exit(1)
    finally:
        # التأكد من إيقاف المجدول
        if scheduler_manager:
            scheduler_manager.stop_scheduler()

if __name__ == "__main__":
    main()
