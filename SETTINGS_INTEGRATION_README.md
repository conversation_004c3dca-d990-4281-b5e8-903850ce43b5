# دليل تكامل نافذة الإعدادات
## Settings Window Integration Guide

### 🎯 الهدف
تم تكامل نافذة الإعدادات الشاملة مع البرنامج الرئيسي `large_font_run.py` بحيث يمكن الوصول إليها من خلال أيقونة "إعداد" في الواجهة الرئيسية.

### 🔧 كيفية الوصول لنافذة الإعدادات

#### 1. من البرنامج الرئيسي
- شغل البرنامج: `python large_font_run.py`
- انقر على أيقونة "إعداد" (الأيقونة الثانية في الصف الأول)
- ستفتح نافذة الإعدادات الشاملة

#### 2. اختبار مستقل
- شغل الاختبار البسيط: `python simple_test.py`
- انقر على زر "فتح الإعدادات"

### 📋 الصفحات المتوفرة حالياً

#### ✅ صفحات مكتملة:
1. **الإعدادات العامة** (`general_settings.py`)
   - اللغة والخط
   - المظهر والثيم
   - السلوك العام

2. **معلومات الشركة** (`company_settings.py`)
   - بيانات الشركة الأساسية
   - تحميل الشعار
   - معلومات الاتصال

3. **الإعدادات المحاسبية** (`accounting_settings.py`)
   - السنة المالية
   - العملة والدقة العشرية
   - طرق الإدخال والحسابات الافتراضية

#### 🔄 صفحات قيد التطوير:
4. **إعدادات المخزون** (inventory_settings.py)
5. **إعدادات الفواتير** (invoicing_settings.py)
6. **إدارة المستخدمين** (users_settings.py)
7. **إعدادات الأمان** (security_settings.py)
8. **إعدادات الشبكة** (network_settings.py)
9. **إعدادات التقارير** (reports_settings.py)

### 🏗️ البنية التقنية

#### الملفات الرئيسية:
```
control_panel/
├── control_panel_window.py      # النافذة الرئيسية
├── styles.qss                   # التنسيقات CSS
├── settings_pages/              # صفحات الإعدادات
│   ├── __init__.py
│   ├── general_settings.py      ✅
│   ├── company_settings.py      ✅
│   ├── accounting_settings.py   ✅
│   └── [6 صفحات أخرى قيد التطوير]
└── icons/                       # أيقونات الإعدادات

core/
└── settings_manager.py          # إدارة الإعدادات المركزية
```

#### التكامل مع البرنامج الرئيسي:
```python
# في large_font_run.py
def menu_click(self, item):
    if item == "إعداد":
        self.open_settings_window()

def open_settings_window(self):
    from control_panel.control_panel_window import ControlPanelWindow
    self.settings_window = ControlPanelWindow(self.main_window)
    self.settings_window.show()
```

### 💾 إدارة الإعدادات

#### حفظ الإعدادات:
- يتم حفظ الإعدادات تلقائياً في ملف JSON
- مسار الملف: `config/settings.json`
- يتم إنشاء نسخة احتياطية عند كل حفظ

#### استرداد الإعدادات:
- يتم تحميل الإعدادات تلقائياً عند بدء التشغيل
- في حالة عدم وجود ملف الإعدادات، يتم استخدام القيم الافتراضية

### 🎨 التصميم والواجهة

#### الميزات:
- دعم كامل للغة العربية (RTL)
- تصميم احترافي مع تدرجات وظلال
- تنقل سهل عبر الشريط الجانبي
- أزرار حفظ وإلغاء في كل صفحة
- رسائل تأكيد للعمليات المهمة

#### الألوان والخطوط:
- الخط الأساسي: Cairo
- الألوان: تدرجات زرقاء وخضراء
- دعم الثيم الفاتح والداكن

### 🔧 التطوير المستقبلي

#### المهام القادمة:
1. إكمال الصفحات الـ 6 المتبقية
2. إضافة المزيد من خيارات التخصيص
3. تحسين الأداء والاستجابة
4. إضافة المزيد من التحقق من صحة البيانات
5. تطوير نظام النسخ الاحتياطي للإعدادات

#### كيفية إضافة صفحة جديدة:
1. إنشاء ملف جديد في `control_panel/settings_pages/`
2. وراثة من الكلاس الأساسي أو اتباع نفس النمط
3. إضافة الاستيراد في `__init__.py`
4. تحديث `control_panel_window.py` لتحميل الصفحة

### 📞 الدعم والمساعدة

في حالة وجود مشاكل:
1. تحقق من وجود جميع الملفات المطلوبة
2. تأكد من تثبيت المكتبات: `customtkinter`, `PIL`, `tkcalendar`
3. راجع رسائل الخطأ في وحدة التحكم
4. استخدم ملف الاختبار `simple_test.py` للتشخيص

---
**تم التطوير بواسطة:** فريق تطوير برنامج ست الكل للمحاسبة  
**التاريخ:** يوليو 2025  
**الإصدار:** 1.0
