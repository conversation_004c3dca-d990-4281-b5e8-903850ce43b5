#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مصلح الاستيرادات الشامل والنهائي
Comprehensive Final Import Fixer
"""

import re
import ast
from pathlib import Path
from typing import Dict, List, Set, Tuple
import json
from PIL import Image
from PIL import ImageDraw
from PIL import ImageTk
from datetime import date
from datetime import datetime
from datetime import timedelta
from enum import Enum
from tkinter import filedialog
from tkinter import messagebox
from tkinter import ttk
from typing import Any
from typing import Callable
from typing import List, Dict, Optional, Tuple, Any, Union, Callable
from typing import Optional
from typing import Union
import logging
import os
import sqlite3
import subprocess
import sys
import threading
import time
import traceback

class ComprehensiveImportFixer:
    """مصلح الاستيرادات الشامل والنهائي"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.fixed_files = []
        self.errors = []
        
        # خريطة شاملة للاستيرادات المطلوبة
        self.import_fixes = {
            # PIL/Pillow imports
            'Image.open': 'from PIL import Image',
            'Image.new': 'from PIL import Image',
            'Image.fromarray': 'from PIL import Image',
            'ImageTk.PhotoImage': 'from PIL import ImageTk',
            'ImageDraw.Draw': 'from PIL import ImageDraw',
            
            # Typing imports
            'List[': 'from typing import List',
            'Dict[': 'from typing import Dict',
            'Optional[': 'from typing import Optional',
            'Tuple[': 'from typing import Tuple',
            'Any': 'from typing import Any',
            'Union[': 'from typing import Union',
            'Callable[': 'from typing import Callable',
            
            # Standard library
            'Path(': 'from pathlib import Path',
            'datetime.now': 'from datetime import datetime',
            'datetime.today': 'from datetime import datetime',
            'date.today': 'from datetime import date',
            'timedelta(': 'from datetime import timedelta',
            
            # Database
            'Enum': 'from enum import Enum',
            
            # GUI
            'messagebox.': 'from tkinter import messagebox',
            'filedialog.': 'from tkinter import filedialog',
            'ttk.': 'from tkinter import ttk',
            
            # Other common imports
            'json.': 'import json',
            'os.path': 'import os',
            'sys.': 'import sys',
            're.': 'import re',
            'time.': 'import time',
            'logging.': 'import logging',
            'traceback.': 'import traceback',
            'subprocess.': 'import subprocess',
            'threading.': 'import threading',
            'sqlite3.': 'import sqlite3',
        }
        
        # أنماط الاستخدام المتقدمة
        self.advanced_patterns = {
            r'\bImage\.(open|new|fromarray|blend)': 'from PIL import Image',
            r'\bImageTk\.(PhotoImage|BitmapImage)': 'from PIL import ImageTk',
            r'\bImageDraw\.(Draw|ImageDraw)': 'from PIL import ImageDraw',
            r':\s*(List|Dict|Optional|Tuple|Any|Union|Callable)\b': 'from typing import List, Dict, Optional, Tuple, Any, Union, Callable',
            r'->\s*(List|Dict|Optional|Tuple|Any|Union|Callable)\b': 'from typing import List, Dict, Optional, Tuple, Any, Union, Callable',
            r'\b(List|Dict|Optional|Tuple|Any|Union|Callable)\[': 'from typing import List, Dict, Optional, Tuple, Any, Union, Callable',
        }
    
    def analyze_file_imports(self, file_path: Path) -> Dict:
        """تحليل استيرادات ملف واحد"""
        analysis = {
            'file': str(file_path),
            'existing_imports': set(),
            'missing_imports': set(),
            'needed_imports': set(),
            'content': ''
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            analysis['content'] = content
            
            # استخراج الاستيرادات الموجودة
            import_lines = re.findall(r'^(from\s+[\w.]+\s+import\s+[^#\n]+|import\s+[^#\n]+)', content, re.MULTILINE)
            
            for import_line in import_lines:
                # تنظيف السطر
                clean_line = import_line.split('#')[0].strip()
                analysis['existing_imports'].add(clean_line)
                
                # استخراج الأسماء المستوردة
                if 'from' in clean_line and 'import' in clean_line:
                    parts = clean_line.split('import', 1)
                    if len(parts) == 2:
                        imported_names = [name.strip() for name in parts[1].split(',')]
                        for name in imported_names:
                            clean_name = name.split(' as ')[0].strip()
                            analysis['existing_imports'].add(clean_name)
            
            # البحث عن الاستيرادات المطلوبة
            for pattern, import_statement in self.import_fixes.items():
                if pattern in content:
                    analysis['needed_imports'].add(import_statement)
            
            # البحث باستخدام الأنماط المتقدمة
            for pattern, import_statement in self.advanced_patterns.items():
                if re.search(pattern, content):
                    analysis['needed_imports'].add(import_statement)
            
            # تحديد الاستيرادات المفقودة
            for needed_import in analysis['needed_imports']:
                if needed_import not in analysis['existing_imports']:
                    # فحص أكثر دقة
                    if 'from' in needed_import:
                        module_part = needed_import.split('import')[1].strip()
                        if module_part not in str(analysis['existing_imports']):
                            analysis['missing_imports'].add(needed_import)
                    else:
                        module_name = needed_import.replace('import', '').strip()
                        if module_name not in str(analysis['existing_imports']):
                            analysis['missing_imports'].add(needed_import)
            
        except Exception as e:
            self.errors.append(f"خطأ في تحليل {file_path}: {e}")
        
        return analysis
    
    def fix_file_imports(self, analysis: Dict) -> bool:
        """إصلاح استيرادات ملف واحد"""
        if not analysis['missing_imports']:
            return False
        
        file_path = Path(analysis['file'])
        content = analysis['content']
        lines = content.split('\n')
        
        # العثور على موقع إدراج الاستيرادات
        insert_position = self.find_import_position(lines)
        
        # تنظيم الاستيرادات المفقودة
        missing_imports = list(analysis['missing_imports'])
        missing_imports.sort()
        
        # إدراج الاستيرادات الجديدة
        for i, import_line in enumerate(missing_imports):
            lines.insert(insert_position + i, import_line)
        
        # كتابة الملف المحدث
        try:
            new_content = '\n'.join(lines)
            
            # التحقق من صحة النحو (إذا أمكن)
            try:
                ast.parse(new_content)
                syntax_ok = True
            except SyntaxError:
                syntax_ok = False
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            status = "✅" if syntax_ok else "⚠️"
            print(f"{status} تم إصلاح: {file_path.name}")
            print(f"   📦 أضيف: {len(missing_imports)} استيراد")
            
            return True
            
        except Exception as e:
            error_msg = f"خطأ في كتابة {file_path}: {e}"
            print(f"❌ {error_msg}")
            self.errors.append(error_msg)
            return False
    
    def find_import_position(self, lines: List[str]) -> int:
        """العثور على الموقع المناسب لإدراج الاستيرادات"""
        # البحث عن آخر استيراد موجود
        last_import_line = -1
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if (stripped.startswith('import ') or 
                stripped.startswith('from ') or
                (stripped.startswith('#') and ('coding' in stripped or 'encoding' in stripped))):
                last_import_line = i
        
        # إذا لم نجد استيرادات، ابحث عن نهاية docstring
        if last_import_line == -1:
            in_docstring = False
            for i, line in enumerate(lines):
                stripped = line.strip()
                
                if '"""' in stripped or "'''" in stripped:
                    if not in_docstring:
                        in_docstring = True
                    else:
                        in_docstring = False
                        last_import_line = i
                        break
                
                if not in_docstring and stripped and not stripped.startswith('#'):
                    return i
        
        return last_import_line + 1
    
    def fix_all_imports(self):
        """إصلاح جميع الاستيرادات في المشروع"""
        print("🔧 بدء الإصلاح الشامل للاستيرادات...")
        
        # فحص جميع ملفات Python
        python_files = []
        for file_path in self.project_root.rglob("*.py"):
            if any(skip in str(file_path) for skip in ["__pycache__", ".git", "venv", "env"]):
                continue
            python_files.append(file_path)
        
        print(f"📊 تم العثور على {len(python_files)} ملف Python")
        
        # تحليل وإصلاح كل ملف
        files_with_issues = 0
        files_fixed = 0
        
        for file_path in python_files:
            analysis = self.analyze_file_imports(file_path)
            
            if analysis['missing_imports']:
                files_with_issues += 1
                print(f"\n🔍 {file_path.name}: {len(analysis['missing_imports'])} استيراد مفقود")
                
                if self.fix_file_imports(analysis):
                    files_fixed += 1
                    self.fixed_files.append(str(file_path))
        
        print(f"\n📊 النتائج النهائية:")
        print(f"   📁 ملفات تم فحصها: {len(python_files)}")
        print(f"   ⚠️ ملفات بها مشاكل: {files_with_issues}")
        print(f"   🔧 ملفات تم إصلاحها: {files_fixed}")
        print(f"   ❌ أخطاء حدثت: {len(self.errors)}")
        
        if self.errors:
            print(f"\n⚠️ الأخطاء:")
            for error in self.errors:
                print(f"   - {error}")
        
        return files_fixed
    
    def validate_fixes(self):
        """التحقق من صحة الإصلاحات"""
        print(f"\n🔍 التحقق من صحة الإصلاحات...")
        
        valid_count = 0
        for file_path_str in self.fixed_files:
            file_path = Path(file_path_str)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # محاولة تحليل الملف نحوياً
                try:
                    ast.parse(content)
                    valid_count += 1
                    print(f"   ✅ {file_path.name}: صحيح نحوياً")
                except SyntaxError as e:
                    print(f"   ⚠️ {file_path.name}: خطأ نحوي - {e}")
                    
            except Exception as e:
                print(f"   ❌ {file_path.name}: خطأ في القراءة - {e}")
        
        print(f"\n📊 نتائج التحقق:")
        print(f"   ✅ ملفات صحيحة: {valid_count}")
        print(f"   ⚠️ ملفات تحتاج مراجعة: {len(self.fixed_files) - valid_count}")
        
        return valid_count

def main():
    """الدالة الرئيسية"""
    fixer = ComprehensiveImportFixer()
    
    # إصلاح جميع الاستيرادات
    files_fixed = fixer.fix_all_imports()
    
    # التحقق من الإصلاحات
    if files_fixed > 0:
        valid_count = fixer.validate_fixes()
        
        print(f"\n🎉 تم الانتهاء من الإصلاح الشامل للاستيرادات!")
        print(f"📊 إجمالي الملفات المصلحة: {files_fixed}")
        print(f"✅ ملفات صحيحة نحوياً: {valid_count}")
        
        success_rate = (valid_count / files_fixed) * 100 if files_fixed > 0 else 0
        print(f"📈 معدل النجاح: {success_rate:.1f}%")
    else:
        print(f"\n✅ جميع الاستيرادات صحيحة!")

if __name__ == "__main__":
    main()
