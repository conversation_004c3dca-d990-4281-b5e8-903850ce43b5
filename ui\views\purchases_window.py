# -*- coding: utf-8 -*-
# cSpell:disable
"""
نافذة المشتريات المحسنة
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, date
from themes.modern_theme import MODERN_COLORS, FONTS
from services.purchases_manager import PurchasesManager
from database.database_manager import DatabaseManager
from ui.window_utils import configure_window_fullscreen

class PurchasesWindow:
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.purchases_manager = PurchasesManager()
        self.db_manager = DatabaseManager()
        self.purchase_items = []  # قائمة الأصناف المشتراة
        self.suppliers_list = []  # قائمة الموردين

        self.create_window()
        self.load_suppliers()

    def create_window(self):
        """إنشاء نافذة المشتريات"""
        self.window = ctk.CTkToplevel(self.parent)

        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "نظام المشتريات - برنامج ست الكل للمحاسبة")
        self.window.configure(fg_color=MODERN_COLORS['background'])

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # إنشاء المحتوى
        self.create_header()
        self.create_main_content()
        self.create_footer()

    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = ctk.CTkFrame(self.window, height=80, fg_color=MODERN_COLORS['primary_dark'])
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            header_frame,
            text="📦 نظام المشتريات",
            font=(FONTS['arabic'], 24, "bold"),
            text_color="white"
        )
        title_label.pack(side="right", padx=20, pady=20)

        # تاريخ اليوم
        date_label = ctk.CTkLabel(
            header_frame,
            text=f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}",
            font=(FONTS['arabic'], 14),
            text_color="white"
        )
        date_label.pack(side="left", padx=20, pady=20)

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        main_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # إطار معلومات فاتورة الشراء
        self.create_purchase_info(main_frame)

        # إطار الأصناف المشتراة
        self.create_items_section(main_frame)

        # إطار الإجماليات
        self.create_totals_section(main_frame)

    def create_purchase_info(self, parent):
        """إنشاء قسم معلومات فاتورة الشراء"""
        info_frame = ctk.CTkFrame(parent, height=120)
        info_frame.pack(fill="x", pady=(0, 10))
        info_frame.pack_propagate(False)

        # العنوان
        title = ctk.CTkLabel(
            info_frame,
            text="معلومات فاتورة الشراء",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=(10, 5))

        # إطار الحقول
        fields_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        fields_frame.pack(fill="x", padx=20, pady=5)

        # الصف الأول
        row1 = ctk.CTkFrame(fields_frame, fg_color="transparent")
        row1.pack(fill="x", pady=5)

        # رقم الفاتورة
        ctk.CTkLabel(row1, text="رقم الفاتورة:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10)
        self.invoice_number = ctk.CTkEntry(row1, width=150, placeholder_text="رقم الفاتورة")
        self.invoice_number.pack(side="right", padx=10)

        # اسم المورد
        ctk.CTkLabel(row1, text="اسم المورد:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10)
        self.supplier_combo = ctk.CTkComboBox(row1, width=200, values=["جاري تحميل الموردين..."])
        self.supplier_combo.pack(side="right", padx=10)

        # زر إضافة مورد جديد
        add_supplier_btn = ctk.CTkButton(
            row1,
            text="➕ مورد جديد",
            width=100,
            command=self.add_new_supplier,
            fg_color=MODERN_COLORS['info']
        )
        add_supplier_btn.pack(side="right", padx=5)

    def create_items_section(self, parent):
        """إنشاء قسم الأصناف المشتراة"""
        items_frame = ctk.CTkFrame(parent)
        items_frame.pack(fill="both", expand=True, pady=(0, 10))

        # العنوان
        title = ctk.CTkLabel(
            items_frame,
            text="الأصناف المشتراة",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=(10, 5))

        # إطار إدخال الصنف
        input_frame = ctk.CTkFrame(items_frame, height=60)
        input_frame.pack(fill="x", padx=20, pady=5)
        input_frame.pack_propagate(False)

        # حقول الإدخال
        ctk.CTkLabel(input_frame, text="اسم الصنف:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10, pady=15)
        self.item_name = ctk.CTkEntry(input_frame, width=150, placeholder_text="اسم الصنف")
        self.item_name.pack(side="right", padx=5, pady=15)

        ctk.CTkLabel(input_frame, text="الكمية:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10, pady=15)
        self.item_quantity = ctk.CTkEntry(input_frame, width=80, placeholder_text="الكمية")
        self.item_quantity.pack(side="right", padx=5, pady=15)

        ctk.CTkLabel(input_frame, text="سعر الشراء:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10, pady=15)
        self.item_price = ctk.CTkEntry(input_frame, width=100, placeholder_text="سعر الشراء")
        self.item_price.pack(side="right", padx=5, pady=15)

        ctk.CTkLabel(input_frame, text="سعر البيع:", font=(FONTS['arabic'], 12)).pack(side="right", padx=10, pady=15)
        self.item_selling_price = ctk.CTkEntry(input_frame, width=100, placeholder_text="سعر البيع")
        self.item_selling_price.pack(side="right", padx=5, pady=15)

        # زر الإضافة
        add_btn = ctk.CTkButton(
            input_frame,
            text="إضافة",
            width=80,
            command=self.add_item,
            fg_color=MODERN_COLORS['success']
        )
        add_btn.pack(side="right", padx=10, pady=15)

        # جدول الأصناف
        self.create_items_table(items_frame)

    def create_items_table(self, parent):
        """إنشاء جدول الأصناف المشتراة"""
        table_frame = ctk.CTkFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview
        columns = ("item", "quantity", "purchase_price", "selling_price", "total")
        self.items_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)

        # تعريف الأعمدة
        self.items_tree.heading("item", text="اسم الصنف")
        self.items_tree.heading("quantity", text="الكمية")
        self.items_tree.heading("purchase_price", text="سعر الشراء")
        self.items_tree.heading("selling_price", text="سعر البيع")
        self.items_tree.heading("total", text="الإجمالي")

        # تحديد عرض الأعمدة
        self.items_tree.column("item", width=180)
        self.items_tree.column("quantity", width=80)
        self.items_tree.column("purchase_price", width=100)
        self.items_tree.column("selling_price", width=100)
        self.items_tree.column("total", width=100)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.items_tree.pack(side="right", fill="both", expand=True)
        scrollbar.pack(side="left", fill="y")

        # ربط النقر المزدوج للحذف
        self.items_tree.bind("<Double-1>", self.delete_item)

    def create_totals_section(self, parent):
        """إنشاء قسم الإجماليات"""
        totals_frame = ctk.CTkFrame(parent, height=100)
        totals_frame.pack(fill="x", pady=(0, 10))
        totals_frame.pack_propagate(False)

        # العنوان
        title = ctk.CTkLabel(
            totals_frame,
            text="إجمالي المشتريات",
            font=(FONTS['arabic'], 16, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=(10, 5))

        # إطار الإجماليات
        totals_content = ctk.CTkFrame(totals_frame, fg_color="transparent")
        totals_content.pack(fill="x", padx=20, pady=5)

        # الإجمالي
        total_frame = ctk.CTkFrame(totals_content, fg_color="transparent")
        total_frame.pack(side="right", padx=20)

        ctk.CTkLabel(total_frame, text="إجمالي التكلفة:", font=(FONTS['arabic'], 14, "bold")).pack(side="right", padx=10)
        self.total_label = ctk.CTkLabel(total_frame, text="0.00 ريال", font=(FONTS['arabic'], 14, "bold"))
        self.total_label.pack(side="right", padx=10)

    def create_footer(self):
        """إنشاء تذييل النافذة"""
        footer_frame = ctk.CTkFrame(self.window, height=60, fg_color="transparent")
        footer_frame.pack(fill="x", padx=10, pady=(0, 10))
        footer_frame.pack_propagate(False)

        # أزرار العمليات
        save_btn = ctk.CTkButton(
            footer_frame,
            text="💾 حفظ فاتورة الشراء",
            width=150,
            command=self.save_purchase,
            fg_color=MODERN_COLORS['success']
        )
        save_btn.pack(side="right", padx=10, pady=15)

        print_btn = ctk.CTkButton(
            footer_frame,
            text="🖨️ طباعة",
            width=100,
            command=self.print_purchase,
            fg_color=MODERN_COLORS['info']
        )
        print_btn.pack(side="right", padx=10, pady=15)

        close_btn = ctk.CTkButton(
            footer_frame,
            text="❌ إغلاق",
            width=100,
            command=self.close_window,
            fg_color=MODERN_COLORS['error']
        )
        close_btn.pack(side="left", padx=10, pady=15)

    def add_item(self):
        """إضافة صنف مشترى جديد"""
        try:
            name = self.item_name.get().strip()
            quantity = float(self.item_quantity.get())
            purchase_price = float(self.item_price.get())
            selling_price = float(self.item_selling_price.get()) if self.item_selling_price.get().strip() else purchase_price * 1.2

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
                return

            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                return

            if purchase_price <= 0:
                messagebox.showerror("خطأ", "سعر الشراء يجب أن يكون أكبر من صفر")
                return

            total = quantity * purchase_price

            # إضافة إلى قائمة الأصناف
            item_data = {
                'name': name,
                'quantity': quantity,
                'unit_price': purchase_price,
                'selling_price': selling_price,
                'total_price': total
            }
            self.purchase_items.append(item_data)

            # إضافة إلى الجدول
            self.items_tree.insert("", "end", values=(
                name,
                quantity,
                f"{purchase_price:.2f}",
                f"{selling_price:.2f}",
                f"{total:.2f}"
            ))

            # مسح الحقول
            self.item_name.delete(0, "end")
            self.item_quantity.delete(0, "end")
            self.item_price.delete(0, "end")
            self.item_selling_price.delete(0, "end")

            # تحديث الإجمالي
            self.update_total()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للكمية والأسعار")

    def delete_item(self, event):
        """حذف صنف من القائمة"""
        try:
            selected_item = self.items_tree.selection()[0]
            index = self.items_tree.index(selected_item)

            # حذف من الجدول
            self.items_tree.delete(selected_item)

            # حذف من القائمة
            if 0 <= index < len(self.purchase_items):
                del self.purchase_items[index]

            # تحديث الإجمالي
            self.update_total()

        except IndexError:
            pass  # لا يوجد عنصر محدد
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حذف الصنف: {e}")

    def update_total(self):
        """تحديث الإجمالي"""
        total = sum(item['total_price'] for item in self.purchase_items)
        self.total_label.configure(text=f"{total:.2f} ريال")

    def save_purchase(self):
        """حفظ فاتورة الشراء"""
        try:
            # التحقق من البيانات
            supplier_text = self.supplier_combo.get().strip()
            if not supplier_text or supplier_text == "جاري تحميل الموردين...":
                messagebox.showerror("خطأ", "يرجى اختيار المورد")
                return

            if not self.purchase_items:
                messagebox.showerror("خطأ", "يرجى إضافة أصناف لفاتورة الشراء")
                return

            invoice_number = self.invoice_number.get().strip()
            if not invoice_number:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الفاتورة")
                return

            # إعداد بيانات المورد
            supplier_data = {
                'name': supplier_text.split(' - ')[0] if ' - ' in supplier_text else supplier_text
            }

            # حساب الإجماليات
            total_amount = sum(item['total_price'] for item in self.purchase_items)

            # إعداد بيانات الفاتورة
            invoice_data = {
                'invoice_number': invoice_number,
                'invoice_date': date.today(),
                'total_amount': total_amount,
                'net_amount': total_amount,
                'payment_status': 'pending',
                'created_by': 1  # يجب تمرير معرف المستخدم الحالي
            }

            # حفظ الفاتورة
            result = self.purchases_manager.save_purchase_invoice(
                supplier_data, self.purchase_items, invoice_data
            )

            if result['success']:
                messagebox.showinfo("نجح", f"تم حفظ فاتورة الشراء بنجاح!\nرقم الفاتورة: {result['invoice_number']}")
                self.clear_form()
            else:
                error_msg = "\n".join(result['errors'])
                messagebox.showerror("خطأ", f"فشل في حفظ فاتورة الشراء:\n{error_msg}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ فاتورة الشراء: {e}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db_manager.fetch_all("SELECT id, name, phone FROM suppliers WHERE is_active = 1 ORDER BY name")
            self.suppliers_list = [dict(supplier) for supplier in suppliers]

            supplier_values = [f"{supplier['name']} - {supplier['phone']}" for supplier in self.suppliers_list]
            if not supplier_values:
                supplier_values = ["لا يوجد موردين"]

            self.supplier_combo.configure(values=supplier_values)
            if supplier_values and supplier_values[0] != "لا يوجد موردين":
                self.supplier_combo.set("اختر المورد")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الموردين: {e}")

    def add_new_supplier(self):
        """إضافة مورد جديد"""
        try:
            # نافذة إدخال بيانات المورد
            supplier_window = ctk.CTkToplevel(self.window)
            supplier_window.title("إضافة مورد جديد")
            supplier_window.geometry("400x300")
            supplier_window.configure(fg_color=MODERN_COLORS['background'])

            # جعل النافذة في المقدمة
            supplier_window.transient(self.window)
            supplier_window.grab_set()

            # حقول الإدخال
            ctk.CTkLabel(supplier_window, text="اسم المورد:", font=(FONTS['arabic'], 12)).pack(pady=10)
            name_entry = ctk.CTkEntry(supplier_window, width=300, placeholder_text="اسم المورد")
            name_entry.pack(pady=5)

            ctk.CTkLabel(supplier_window, text="رقم الهاتف:", font=(FONTS['arabic'], 12)).pack(pady=10)
            phone_entry = ctk.CTkEntry(supplier_window, width=300, placeholder_text="رقم الهاتف")
            phone_entry.pack(pady=5)

            ctk.CTkLabel(supplier_window, text="البريد الإلكتروني:", font=(FONTS['arabic'], 12)).pack(pady=10)
            email_entry = ctk.CTkEntry(supplier_window, width=300, placeholder_text="البريد الإلكتروني")
            email_entry.pack(pady=5)

            def save_supplier():
                name = name_entry.get().strip()
                if not name:
                    messagebox.showerror("خطأ", "اسم المورد مطلوب")
                    return

                try:
                    with self.db_manager.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                            INSERT INTO suppliers (name, phone, email, is_active)
                            VALUES (?, ?, ?, 1)
                        ''', (name, phone_entry.get().strip(), email_entry.get().strip()))
                        conn.commit()

                    messagebox.showinfo("نجح", "تم إضافة المورد بنجاح")
                    if supplier_window and hasattr(supplier_window, "destroy"):

                        supplier_window.destroy()
                    self.load_suppliers()  # إعادة تحميل قائمة الموردين

                except Exception as e:
                    messagebox.showerror("خطأ", f"خطأ في إضافة المورد: {e}")

            # أزرار
            buttons_frame = ctk.CTkFrame(supplier_window, fg_color="transparent")
            buttons_frame.pack(pady=20)

            ctk.CTkButton(buttons_frame, text="حفظ", command=save_supplier,
                         fg_color=MODERN_COLORS['success']).pack(side="right", padx=10)
            ctk.CTkButton(buttons_frame, text="إلغاء", command=supplier_window.destroy,
                         fg_color=MODERN_COLORS['error']).pack(side="left", padx=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة المورد: {e}")

    def clear_form(self):
        """مسح النموذج"""
        self.invoice_number.delete(0, "end")
        self.supplier_combo.set("اختر المورد")
        self.purchase_items.clear()

        # مسح الجدول
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # تحديث الإجمالي
        self.update_total()

    def print_purchase(self):
        """طباعة فاتورة الشراء"""
        if not self.purchase_items:
            messagebox.showerror("خطأ", "لا توجد أصناف للطباعة")
            return

        messagebox.showinfo("طباعة", "سيتم إرسال فاتورة الشراء للطابعة")

    def close_window(self):
        """إغلاق النافذة"""
        if hasattr(self, 'window') and self.window:
            self.window.destroy()
