#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام بعد الإصلاحات
"""

import sys
import os
import tkinter as tk
sys.path.append('.')

def test_window_creation():
    """اختبار إنشاء النافذة"""
    print("🔍 اختبار إنشاء النافذة...")
    try:
        from windows.advanced_item_entry_comprehensive import AdvancedItemEntryComprehensive
        
        # إنشاء النافذة
        app = AdvancedItemEntryComprehensive()
        print("✅ تم إنشاء النافذة بنجاح!")
        
        # اختبار تحميل قائمة الأصناف
        print("🔍 اختبار تحميل قائمة الأصناف...")
        app.load_items_list()
        print("✅ تم تحميل قائمة الأصناف بنجاح!")
        
        # اختبار إضافة بيانات تجريبية
        print("🔍 اختبار إضافة بيانات تجريبية...")
        app.add_sample_data()
        print("✅ تم إضافة البيانات التجريبية بنجاح!")
        
        # اختبار تحديث البيانات
        print("🔍 اختبار تحديث البيانات...")
        app.refresh_data()
        print("✅ تم تحديث البيانات بنجاح!")
        
        # فحص عدد العناصر في القائمة
        items_count = len(app.items_tree.get_children())
        print(f"📊 عدد الأصناف في القائمة: {items_count}")
        
        # إغلاق النافذة
        app.window.destroy()
        return True
        
    except Exception as e:
        print(f"❌ فشل في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("🔍 اختبار عمليات قاعدة البيانات...")
    try:
        from database.advanced_items_database import AdvancedItemsDatabase
        
        db = AdvancedItemsDatabase()
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # اختبار الاستعلام الأساسي
        cursor.execute("""
            SELECT i.code, i.name, c.name as category, i.selling_price, i.current_stock
            FROM items i
            LEFT JOIN categories c ON i.category_id = c.id
            WHERE i.is_active = 1
            ORDER BY i.name
        """)
        
        items = cursor.fetchall()
        print(f"✅ تم تنفيذ الاستعلام - عدد النتائج: {len(items)}")
        
        # عرض أول 3 نتائج
        for i, item in enumerate(items[:3]):
            print(f"  {i+1}. الكود: {item[0]}, الاسم: {item[1]}, الفئة: {item[2] or 'غير محدد'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار قاعدة البيانات: {e}")
        return False

def test_main_application():
    """اختبار التطبيق الرئيسي"""
    print("🔍 اختبار التطبيق الرئيسي...")
    try:
        # محاولة استيراد التطبيق الرئيسي
        import large_font_run
        print("✅ تم استيراد التطبيق الرئيسي بنجاح!")
        
        # اختبار إنشاء النافذة الرئيسية (بدون عرض)
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # محاولة إنشاء نافذة إدخال الأصناف
        from windows.advanced_item_entry_comprehensive import AdvancedItemEntryComprehensive
        item_window = AdvancedItemEntryComprehensive(parent=root)
        print("✅ تم إنشاء نافذة إدخال الأصناف من التطبيق الرئيسي!")
        
        # إغلاق النوافذ
        item_window.window.destroy()
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار التطبيق الرئيسي: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار النظام بعد الإصلاحات")
    print("=" * 60)
    
    tests = [
        ("عمليات قاعدة البيانات", test_database_operations),
        ("إنشاء النافذة", test_window_creation),
        ("التطبيق الرئيسي", test_main_application),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        if test_func():
            passed += 1
        print("-" * 40)
    
    print("=" * 60)
    print("📊 ملخص الاختبارات:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! تم حل مشكلة تحميل قائمة الأصناف.")
        print("\n📝 الإصلاحات المطبقة:")
        print("1. ✅ إصلاح طريقة الوصول لبيانات sqlite3.Row")
        print("2. ✅ تحسين معالجة الأخطاء مع رسائل واضحة")
        print("3. ✅ إضافة وظيفة إضافة بيانات تجريبية")
        print("4. ✅ تحسين وظيفة تحديث البيانات")
        print("5. ✅ إضافة أزرار في شريط الأدوات")
        print("\n🚀 النظام جاهز للاستخدام!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
