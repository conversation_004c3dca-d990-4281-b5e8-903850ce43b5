{"timestamp": "2025-07-29T00:11:50.002208", "total_files_analyzed": 0, "total_size_mb": 0, "files_to_delete": {"backup_files": [{"path": "BACKUP_EXECUTION_PLAN.bat", "size_mb": 0.006836891174316406, "can_delete": true}, {"path": "backups\\backup_20250709_072751.db", "size_mb": 0.12890625, "can_delete": false}, {"path": "backup_critical_files\\backup_restore.py.backup", "size_mb": 0.027726173400878906, "can_delete": true}, {"path": "backup_deep\\backup_restore.py.backup_20250720_195918", "size_mb": 0.02793598175048828, "can_delete": true}, {"path": "backup_fixes\\backup_restore.py.backup_20250720_020022", "size_mb": 0.027846336364746094, "can_delete": true}, {"path": "backup_fixes\\backup_restore.py.backup_20250720_020023", "size_mb": 0.027846336364746094, "can_delete": true}, {"path": "backup_fixes\\backup_restore.py.backup_20250720_021645", "size_mb": 0.027891159057617188, "can_delete": true}, {"path": "backup_ultimate\\backup_restore.py.backup_20250720_024825", "size_mb": 0.02793598175048828, "can_delete": true}, {"path": "ui\\backup_restore.py", "size_mb": 0.02793598175048828, "can_delete": true}, {"path": "ui\\backup_restore.py.except_backup", "size_mb": 0.027726173400878906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_20250709_072751.db", "size_mb": 0.12890625, "can_delete": false}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup", "size_mb": 0.027726173400878906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_020022", "size_mb": 0.027846336364746094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_020023", "size_mb": 0.027846336364746094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_021645", "size_mb": 0.027891159057617188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_024825", "size_mb": 0.02793598175048828, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_195918", "size_mb": 0.02793598175048828, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.except_backup", "size_mb": 0.027726173400878906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\ui_components\\backup_restore.py", "size_mb": 0.02793598175048828, "can_delete": true}, {"path": "main.py.backup", "size_mb": 0.0030736923217773438, "can_delete": true}, {"path": "backup_critical_files\\backup_restore.py.backup", "size_mb": 0.027726173400878906, "can_delete": true}, {"path": "backup_critical_files\\categories_management_window.py.backup", "size_mb": 0.028049468994140625, "can_delete": true}, {"path": "backup_critical_files\\comprehensive_income_window.py.backup", "size_mb": 0.02626323699951172, "can_delete": true}, {"path": "backup_critical_files\\daily_journal_window.py.backup", "size_mb": 0.03763389587402344, "can_delete": true}, {"path": "backup_critical_files\\inventory_window.py.backup", "size_mb": 0.028348922729492188, "can_delete": true}, {"path": "backup_critical_files\\sales_analysis_window.py.backup", "size_mb": 0.05341053009033203, "can_delete": true}, {"path": "backup_critical_files\\stock_management_window.py.backup", "size_mb": 0.06171894073486328, "can_delete": true}, {"path": "backup_critical_files\\treasury_window.py.backup", "size_mb": 0.026228904724121094, "can_delete": true}, {"path": "database\\hybrid_database_manager.py.backup", "size_mb": 0.017877578735351562, "can_delete": true}, {"path": "services\\sales_manager.py.backup", "size_mb": 0.021500587463378906, "can_delete": true}, {"path": "ui\\main_window.py.backup", "size_mb": 0.09733200073242188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup", "size_mb": 0.027726173400878906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\categories_management_window.py.backup", "size_mb": 0.028049468994140625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\comprehensive_income_window.py.backup", "size_mb": 0.02626323699951172, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\daily_journal_window.py.backup", "size_mb": 0.03763389587402344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\inventory_window.py.backup", "size_mb": 0.028348922729492188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\main.py.backup", "size_mb": 0.0030736923217773438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\main_window.py.backup", "size_mb": 0.09733200073242188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\sales_analysis_window.py.backup", "size_mb": 0.05341053009033203, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\sales_manager.py.backup", "size_mb": 0.021500587463378906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\stock_management_window.py.backup", "size_mb": 0.06171894073486328, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\treasury_window.py.backup", "size_mb": 0.026228904724121094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\hybrid_database_manager.py.backup", "size_mb": 0.017877578735351562, "can_delete": true}, {"path": "backups\\scheduled_backup_20250712_191621.db", "size_mb": 0.13671875, "can_delete": false}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\scheduled_backup_20250712_191621.db", "size_mb": 0.13671875, "can_delete": false}], "cache_files": [{"path": "__pycache__\\arabic_text_handler.cpython-313.pyc", "size_mb": 0.008334159851074219, "can_delete": true}, {"path": "__pycache__\\comprehensive_validation_test.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.015737533569335938, "can_delete": true}, {"path": "__pycache__\\comprehensive_validation_test.cpython-311.pyc", "size_mb": 0.015608787536621094, "can_delete": true}, {"path": "__pycache__\\main.cpython-311.pyc", "size_mb": 0.0023736953735351562, "can_delete": true}, {"path": "__pycache__\\main.cpython-313.pyc", "size_mb": 0.0020856857299804688, "can_delete": true}, {"path": "__pycache__\\START_HERE.cpython-311.pyc", "size_mb": 0.0023946762084960938, "can_delete": true}, {"path": "__pycache__\\test_admin_panel_icon.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0043201446533203125, "can_delete": true}, {"path": "__pycache__\\test_advanced_control_panel.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.00832366943359375, "can_delete": true}, {"path": "__pycache__\\test_control_panel.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.004494667053222656, "can_delete": true}, {"path": "__pycache__\\test_control_panel_simple.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.00618743896484375, "can_delete": true}, {"path": "__pycache__\\test_customtkinter.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0018606185913085938, "can_delete": true}, {"path": "__pycache__\\test_database.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0018548965454101562, "can_delete": true}, {"path": "__pycache__\\test_hr_window.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0034809112548828125, "can_delete": true}, {"path": "__pycache__\\test_import.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0017757415771484375, "can_delete": true}, {"path": "__pycache__\\test_settings.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0044765472412109375, "can_delete": true}, {"path": "auth\\__pycache__\\auth_manager.cpython-311.pyc", "size_mb": 0.010835647583007812, "can_delete": true}, {"path": "auth\\__pycache__\\auth_manager.cpython-312.pyc", "size_mb": 0.010107040405273438, "can_delete": true}, {"path": "auth\\__pycache__\\auth_manager.cpython-313.pyc", "size_mb": 0.010283470153808594, "can_delete": true}, {"path": "auth\\__pycache__\\auth_manager.cpython-314.pyc", "size_mb": 0.010611534118652344, "can_delete": true}, {"path": "auth\\__pycache__\\__init__.cpython-311.pyc", "size_mb": 0.0002498626708984375, "can_delete": true}, {"path": "auth\\__pycache__\\__init__.cpython-312.pyc", "size_mb": 0.00023365020751953125, "can_delete": true}, {"path": "auth\\__pycache__\\__init__.cpython-313.pyc", "size_mb": 0.00023365020751953125, "can_delete": true}, {"path": "auth\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00020122528076171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\arabic_fonts.cpython-312.pyc", "size_mb": 0.002471923828125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\argparsing.cpython-311.pyc", "size_mb": 0.02588653564453125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\compat.cpython-311.pyc", "size_mb": 0.003910064697265625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\control_panel_config.cpython-312.pyc", "size_mb": 0.0068798065185546875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\error_reporting.cpython-311.pyc", "size_mb": 0.019292831420898438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\exceptions.cpython-311.pyc", "size_mb": 0.00092315673828125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\expand.cpython-311.pyc", "size_mb": 0.026955604553222656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\extra_validations.cpython-311.pyc", "size_mb": 0.001800537109375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\fastjsonschema_exceptions.cpython-311.pyc", "size_mb": 0.0031118392944335938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\fastjsonschema_validations.cpython-311.pyc", "size_mb": 0.18373584747314453, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\findpaths.cpython-311.pyc", "size_mb": 0.010271072387695312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\formats.cpython-311.pyc", "size_mb": 0.013711929321289062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\pyprojecttoml.cpython-311.pyc", "size_mb": 0.026190757751464844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\scheduler_settings.cpython-311.pyc", "size_mb": 0.0037851333618164062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\scheduler_settings.cpython-312.pyc", "size_mb": 0.0035533905029296875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\settings.cpython-311.pyc", "size_mb": 0.002079010009765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\settings.cpython-312.pyc", "size_mb": 0.0019960403442382812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\settings.cpython-313.pyc", "size_mb": 0.0019893646240234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\setupcfg.cpython-311.pyc", "size_mb": 0.031516075134277344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\sqlserver_config.cpython-311.pyc", "size_mb": 0.0075969696044921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\sqlserver_config.cpython-312.pyc", "size_mb": 0.006987571716308594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\_apply_pyprojecttoml.cpython-311.pyc", "size_mb": 0.021509170532226562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\__init__.cpython-311.pyc", "size_mb": 0.0037012100219726562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\__init__.cpython-311_1.pyc", "size_mb": 0.0022306442260742188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\__init__.cpython-311_2.pyc", "size_mb": 0.0019559860229492188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\__init__.cpython-311_3.pyc", "size_mb": 0.08706855773925781, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\__init__.cpython-311_4.pyc", "size_mb": 0.00017070770263671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\__init__.cpython-312.pyc", "size_mb": 0.00015926361083984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\__init__.cpython-313.pyc", "size_mb": 0.00015926361083984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\accounts_manager.cpython-311.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\accounts_manager.cpython-312.pyc", "size_mb": 0.014154434204101562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\accounts_tree_manager.cpython-312.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\database_manager.cpython-311.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\database_manager.cpython-312.pyc", "size_mb": 0.**************, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\database_manager.cpython-313.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\hybrid_database_manager.cpython-311.pyc", "size_mb": 0.****************, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\hybrid_database_manager.cpython-312.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\hybrid_database_manager.cpython-313.pyc", "size_mb": 0.021854400634765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\invoices_database_manager.cpython-312.pyc", "size_mb": 0.022192001342773438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\invoices_manager.cpython-312.pyc", "size_mb": 0.019351959228515625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\journal_entries_manager.cpython-311.pyc", "size_mb": 0.016023635864257812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\journal_entries_manager.cpython-312.pyc", "size_mb": 0.013880729675292969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\postgresql_manager.cpython-311.pyc", "size_mb": 0.021619796752929688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\postgresql_manager.cpython-312.pyc", "size_mb": 0.01965808868408203, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\products_manager.cpython-311.pyc", "size_mb": 0.02192974090576172, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\products_manager.cpython-312.pyc", "size_mb": 0.018235206604003906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\profit_loss_structure_manager.cpython-312.pyc", "size_mb": 0.03663063049316406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\reports_manager.cpython-311.pyc", "size_mb": 0.014557838439941406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\reports_manager.cpython-312.pyc", "size_mb": 0.012606620788574219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\sqlserver_manager.cpython-311.pyc", "size_mb": 0.021170616*********, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\sqlserver_manager.cpython-312.pyc", "size_mb": 0.01902484893798828, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\warehouse_manager.cpython-312.pyc", "size_mb": 0.026737213134765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\__init__.cpython-311.pyc", "size_mb": 0.00025177001953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\__init__.cpython-312.pyc", "size_mb": 0.00023555755615234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\__init__.cpython-313.pyc", "size_mb": 0.00023555755615234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_abc.cpython-311.pyc", "size_mb": 0.004704475402832031, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_abstract_interface.cpython-311.pyc", "size_mb": 0.0020513534545898438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_admin_panel_icon.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0043201446533203125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_advanced_control_panel.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.00832366943359375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_afm.cpython-311.pyc", "size_mb": 0.0054912567138671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_agg.cpython-311.pyc", "size_mb": 0.022316932678222656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_agg_filter.cpython-311.pyc", "size_mb": 0.0016355514526367188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_angle_helper.cpython-311.pyc", "size_mb": 0.008250236511230469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_animation.cpython-311.pyc", "size_mb": 0.031261444091796875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_api.cpython-311.pyc", "size_mb": 0.012976646423339844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_api.cpython-311_1.pyc", "size_mb": 0.041579246520996094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_argparse.cpython-311.pyc", "size_mb": 0.006534576416015625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arraymethod.cpython-311.pyc", "size_mb": 0.0053997039794921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrayobject.cpython-311.pyc", "size_mb": 0.0024423599243164062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrayobject.cpython-311_1.pyc", "size_mb": 0.0045452117919921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arraypad.cpython-311.pyc", "size_mb": 0.0770883560180664, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrayprint.cpython-311.pyc", "size_mb": 0.0791158676147461, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arraysetops.cpython-311.pyc", "size_mb": 0.05783271789550781, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrayterator.cpython-311.pyc", "size_mb": 0.0031681060791015625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_api_info.cpython-311.pyc", "size_mb": 0.0060787200927734375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_coercion.cpython-311.pyc", "size_mb": 0.05646038055419922, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_from_pyobj.cpython-311.pyc", "size_mb": 0.04140758514404297, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_interface.cpython-311.pyc", "size_mb": 0.008145332336425781, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_utils.cpython-311.pyc", "size_mb": 0.002574920654296875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrow_patches.cpython-311.pyc", "size_mb": 0.009963035583496094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_art3d.cpython-311.pyc", "size_mb": 0.0049419403076171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_artist.cpython-311.pyc", "size_mb": 0.03229522705078125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_assumed_shape.cpython-311.pyc", "size_mb": 0.0034084320068359375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axes.cpython-311.pyc", "size_mb": 0.6232728958129883, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axes3d.cpython-311.pyc", "size_mb": 0.16585063934326172, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axes_grid1.cpython-311.pyc", "size_mb": 0.048956871032714844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axis.cpython-311.pyc", "size_mb": 0.0025892257690429688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axislines.cpython-311.pyc", "size_mb": 0.008936882019042969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axis_artist.cpython-311.pyc", "size_mb": 0.005756378173828125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backends_interactive.cpython-311.pyc", "size_mb": 0.042232513427734375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_bases.cpython-311.pyc", "size_mb": 0.037644386291503906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_cairo.cpython-311.pyc", "size_mb": 0.0026454925537109375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_gtk3.cpython-311.pyc", "size_mb": 0.004530906677246094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_inline.cpython-311.pyc", "size_mb": 0.0029296875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_macosx.cpython-311.pyc", "size_mb": 0.004521369934082031, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_nbagg.cpython-311.pyc", "size_mb": 0.002605438232421875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_pdf.cpython-311.pyc", "size_mb": 0.0294952392578125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_pgf.cpython-311.pyc", "size_mb": 0.02360057830810547, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_ps.cpython-311.pyc", "size_mb": 0.023370742797851562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_qt.cpython-311.pyc", "size_mb": 0.02046966552734375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_registry.cpython-311.pyc", "size_mb": 0.009449005126953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_svg.cpython-311.pyc", "size_mb": 0.04307365417480469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_template.cpython-311.pyc", "size_mb": 0.004010200500488281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_tk.cpython-311.pyc", "size_mb": 0.015591621398925781, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_tools.cpython-311.pyc", "size_mb": 0.0008897781372070312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_webagg.cpython-311.pyc", "size_mb": 0.0017518997192382812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_basic.cpython-311.pyc", "size_mb": 0.0020837783813476562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_bbox_tight.cpython-311.pyc", "size_mb": 0.010833740234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_bezier.cpython-311.pyc", "size_mb": 0.0007448196411132812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_block_docstring.cpython-311.pyc", "size_mb": 0.001476287841796875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_build_ext.cpython-311.pyc", "size_mb": 0.004332542419433594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_callback.cpython-311.pyc", "size_mb": 0.015499114990234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_casting_floatingpoint_errors.cpython-311.pyc", "size_mb": 0.010130882263183594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_casting_unittests.cpython-311.pyc", "size_mb": 0.040480613708496094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_category.cpython-311.pyc", "size_mb": 0.025674819946289062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cbook.cpython-311.pyc", "size_mb": 0.06394481658935547, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ccompiler_opt.cpython-311.pyc", "size_mb": 0.031676292419433594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ccompiler_opt_conf.cpython-311.pyc", "size_mb": 0.009432792663574219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_character.cpython-311.pyc", "size_mb": 0.03790473937988281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_chebyshev.cpython-311.pyc", "size_mb": 0.04116630554199219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_classes.cpython-311.pyc", "size_mb": 0.037461280822753906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_collections.cpython-311.pyc", "size_mb": 0.08804035186767578, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_colorbar.cpython-311.pyc", "size_mb": 0.07757377624511719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_colors.cpython-311.pyc", "size_mb": 0.10198783874511719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_common.cpython-311.pyc", "size_mb": 0.002025604248046875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_compare_images.cpython-311.pyc", "size_mb": 0.0022706985473632812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_configtool.cpython-311.pyc", "size_mb": 0.0027408599853515625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_constrainedlayout.cpython-311.pyc", "size_mb": 0.04449653625488281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_container.cpython-311.pyc", "size_mb": 0.0019073486328125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_contour.cpython-311.pyc", "size_mb": 0.055825233459472656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_control_panel.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.004494667053222656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_control_panel_simple.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.00618743896484375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_conversion_utils.cpython-311.pyc", "size_mb": 0.015545845031738281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_core.cpython-311.pyc", "size_mb": 0.37946319580078125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cpu_dispatcher.cpython-311.pyc", "size_mb": 0.0017747879028320312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cpu_features.cpython-311.pyc", "size_mb": 0.021338462829589844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_crackfortran.cpython-311.pyc", "size_mb": 0.02746295928955078, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ctypeslib.cpython-311.pyc", "size_mb": 0.023281097412109375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_customtkinter.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0018606185913085938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_custom_dtypes.cpython-311.pyc", "size_mb": 0.022825241088867188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cycles.cpython-311.pyc", "size_mb": 0.017167091369628906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cython.cpython-311.pyc", "size_mb": 0.01736927032470703, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_data.cpython-311.pyc", "size_mb": 0.006396293640136719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_database.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0018548965454101562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_dates.cpython-311.pyc", "size_mb": 0.08670997619628906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_datetime.cpython-311.pyc", "size_mb": 0.06493568420410156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_datetime.cpython-311_1.pyc", "size_mb": 0.18419361114501953, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_defchararray.cpython-311.pyc", "size_mb": 0.06619453430175781, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_defmatrix.cpython-311.pyc", "size_mb": 0.03604412078857422, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_deprecations.cpython-311.pyc", "size_mb": 0.0012359619140625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_deprecations.cpython-311_1.pyc", "size_mb": 0.005199432373046875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_deprecations.cpython-311_2.pyc", "size_mb": 0.05982780456542969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_determinism.cpython-311.pyc", "size_mb": 0.011881828308105469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_direct.cpython-311.pyc", "size_mb": 0.037955284118652344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_dlpack.cpython-311.pyc", "size_mb": 0.013940811157226562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_doc.cpython-311.pyc", "size_mb": 0.001354217529296875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_docs.cpython-311.pyc", "size_mb": 0.0036716461181640625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_dtype.cpython-311.pyc", "size_mb": 0.12552833557128906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_dviread.cpython-311.pyc", "size_mb": 0.0052318572998046875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_einsum.cpython-311.pyc", "size_mb": 0.08258247375488281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_errstate.cpython-311.pyc", "size_mb": 0.010382652282714844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_exec_command.cpython-311.pyc", "size_mb": 0.014721870422363281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_extending.cpython-311.pyc", "size_mb": 0.0064754486083984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_extint128.cpython-311.pyc", "size_mb": 0.012825965881347656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_extras.cpython-311.pyc", "size_mb": 0.14228343963623047, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_f2cmap.cpython-311.pyc", "size_mb": 0.001064300537109375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_f2py2e.cpython-311.pyc", "size_mb": 0.049101829528808594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fcompiler.cpython-311.pyc", "size_mb": 0.0021085739135742188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fcompiler_gnu.cpython-311.pyc", "size_mb": 0.0034990310668945312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fcompiler_intel.cpython-311.pyc", "size_mb": 0.00215911865234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fcompiler_nagfor.cpython-311.pyc", "size_mb": 0.001544952392578125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_figure.cpython-311.pyc", "size_mb": 0.1214752197265625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_floating_axes.cpython-311.pyc", "size_mb": 0.0069446563720703125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fontconfig_pattern.cpython-311.pyc", "size_mb": 0.0034170150756835938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_font_manager.cpython-311.pyc", "size_mb": 0.02655506134033203, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_format.cpython-311.pyc", "size_mb": 0.056201934814453125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_from_template.cpython-311.pyc", "size_mb": 0.0016412734985351562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ft2font.cpython-311.pyc", "size_mb": 0.048033714294433594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_function_base.cpython-311.pyc", "size_mb": 0.3206977844238281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_function_base.cpython-311_1.pyc", "size_mb": 0.034740447998046875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_generator_mt19937.cpython-311.pyc", "size_mb": 0.19046783447265625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_generator_mt19937_regressions.cpython-311.pyc", "size_mb": 0.014031410217285156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_getattr.cpython-311.pyc", "size_mb": 0.0022878646850585938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_getlimits.cpython-311.pyc", "size_mb": 0.015166282653808594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_gridspec.cpython-311.pyc", "size_mb": 0.0035772323608398438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_grid_finder.cpython-311.pyc", "size_mb": 0.0025606155395507812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_grid_helper_curvelinear.cpython-311.pyc", "size_mb": 0.011327743530273438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_half.cpython-311.pyc", "size_mb": 0.04080677032470703, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_hashtable.cpython-311.pyc", "size_mb": 0.0021333694458007812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_helper.cpython-311.pyc", "size_mb": 0.0099639892578125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_hermite.cpython-311.pyc", "size_mb": 0.03669929504394531, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_hermite_e.cpython-311.pyc", "size_mb": 0.03685951232910156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_histograms.cpython-311.pyc", "size_mb": 0.05576133728027344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_hr_window.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0034809112548828125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_image.cpython-311.pyc", "size_mb": 0.11077499389648438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_import.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0017757415771484375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_indexerrors.cpython-311.pyc", "size_mb": 0.01360321044921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_indexing.cpython-311.pyc", "size_mb": 0.09110546112060547, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_index_tricks.cpython-311.pyc", "size_mb": 0.039315223693847656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_interaction.cpython-311.pyc", "size_mb": 0.02239513397216797, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_io.cpython-311.pyc", "size_mb": 0.19685935974121094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_isfile.cpython-311.pyc", "size_mb": 0.0016126632690429688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_isoc.cpython-311.pyc", "size_mb": 0.0027866363525390625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_item_selection.cpython-311.pyc", "size_mb": 0.011259078979492188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_kind.cpython-311.pyc", "size_mb": 0.0032835006713867188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_laguerre.cpython-311.pyc", "size_mb": 0.0353851318359375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_lazyloading.cpython-311.pyc", "size_mb": 0.0018186569213867188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_legend.cpython-311.pyc", "size_mb": 0.10864830017089844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_legend3d.cpython-311.pyc", "size_mb": 0.010045051574707031, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_legendre.cpython-311.pyc", "size_mb": 0.03775978088378906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_limited_api.cpython-311.pyc", "size_mb": 0.004813194274902344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_linalg.cpython-311.pyc", "size_mb": 0.15238094329833984, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_lines.cpython-311.pyc", "size_mb": 0.029073715209960938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_loadtxt.cpython-311.pyc", "size_mb": 0.073883056640625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_locations.cpython-311.pyc", "size_mb": 0.0031118392944335938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_log.cpython-311.pyc", "size_mb": 0.0024404525756835938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_longdouble.cpython-311.pyc", "size_mb": 0.030716896057128906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_machar.cpython-311.pyc", "size_mb": 0.002040863037109375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_marker.cpython-311.pyc", "size_mb": 0.017998695373535156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_masked_matrix.cpython-311.pyc", "size_mb": 0.018898963928222656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mathtext.cpython-311.pyc", "size_mb": 0.029685020446777344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_matlib.cpython-311.pyc", "size_mb": 0.0043277740478515625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_matplotlib.cpython-311.pyc", "size_mb": 0.005137443542480469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_matrix_linalg.cpython-311.pyc", "size_mb": 0.004837989807128906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_memmap.cpython-311.pyc", "size_mb": 0.015222549438476562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mem_overlap.cpython-311.pyc", "size_mb": 0.052776336669921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mem_policy.cpython-311.pyc", "size_mb": 0.0198974609375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mingw32ccompiler.cpython-311.pyc", "size_mb": 0.0021915435791015625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_misc_util.cpython-311.pyc", "size_mb": 0.007250785827636719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mixed.cpython-311.pyc", "size_mb": 0.0019903182983398438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mixins.cpython-311.pyc", "size_mb": 0.01425933837890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mlab.cpython-311.pyc", "size_mb": 0.05939483642578125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_modules.cpython-311.pyc", "size_mb": 0.004714012145996094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mrecords.cpython-311.pyc", "size_mb": 0.03213310241699219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_multiarray.cpython-311.pyc", "size_mb": 0.0017070770263671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_multiarray.cpython-311_1.pyc", "size_mb": 0.7147340774536133, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_multithreading.cpython-311.pyc", "size_mb": 0.016613006591796875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_multivariate_colormaps.cpython-311.pyc", "size_mb": 0.03306770324707031, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_nanfunctions.cpython-311.pyc", "size_mb": 0.09559249877929688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_nditer.cpython-311.pyc", "size_mb": 0.20339488983154297, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_nep50_promotions.cpython-311.pyc", "size_mb": 0.018797874450683594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_npy_pkg_config.cpython-311.pyc", "size_mb": 0.005494117736816406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numeric.cpython-311.pyc", "size_mb": 0.0015478134155273438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numeric.cpython-311_1.pyc", "size_mb": 0.2895660400390625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numerictypes.cpython-311.pyc", "size_mb": 0.042591094970703125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numpy_config.cpython-311.pyc", "size_mb": 0.0031871795654296875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numpy_version.cpython-311.pyc", "size_mb": 0.0026769638061523438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_offsetbox.cpython-311.pyc", "size_mb": 0.023766517639160156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_old_ma.cpython-311.pyc", "size_mb": 0.0756378173828125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_overrides.cpython-311.pyc", "size_mb": 0.059950828552246094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_packbits.cpython-311.pyc", "size_mb": 0.023711204528808594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_parameter.cpython-311.pyc", "size_mb": 0.008787155151367188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_patches.cpython-311.pyc", "size_mb": 0.05341625213623047, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_path.cpython-311.pyc", "size_mb": 0.035332679748535156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_patheffects.cpython-311.pyc", "size_mb": 0.014262199401855469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pickle.cpython-311.pyc", "size_mb": 0.020201683044433594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_png.cpython-311.pyc", "size_mb": 0.0036249160766601562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pocketfft.cpython-311.pyc", "size_mb": 0.04994010925292969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_polar.cpython-311.pyc", "size_mb": 0.03366565704345703, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_polynomial.cpython-311.pyc", "size_mb": 0.022635459899902344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_polynomial.cpython-311_1.pyc", "size_mb": 0.04291343688964844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_polyutils.cpython-311.pyc", "size_mb": 0.007115364074707031, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_preprocess_data.cpython-311.pyc", "size_mb": 0.015938758850097656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_print.cpython-311.pyc", "size_mb": 0.012358665466308594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_printing.cpython-311.pyc", "size_mb": 0.03477287292480469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_protocols.cpython-311.pyc", "size_mb": 0.0035314559936523438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_public_api.cpython-311.pyc", "size_mb": 0.026037216186523438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pyf_src.cpython-311.pyc", "size_mb": 0.001621246337890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pyinstaller.cpython-311.pyc", "size_mb": 0.0019893646240234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pyplot.cpython-311.pyc", "size_mb": 0.027587890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pyzbar.cpython-311.pyc", "size_mb": 0.014291763305664062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_quiver.cpython-311.pyc", "size_mb": 0.023298263549804688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_quoted_character.cpython-311.pyc", "size_mb": 0.0013446807861328125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_random.cpython-311.pyc", "size_mb": 0.11728286743164062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_randomstate.cpython-311.pyc", "size_mb": 0.13724136352539062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_randomstate_regression.cpython-311.pyc", "size_mb": 0.015677452087402344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_rcparams.cpython-311.pyc", "size_mb": 0.036022186279296875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_read_zbar.cpython-311.pyc", "size_mb": 0.0031270980834960938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_recfunctions.cpython-311.pyc", "size_mb": 0.06194019317626953, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_records.cpython-311.pyc", "size_mb": 0.04062366485595703, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression.cpython-311.pyc", "size_mb": 0.010897636413574219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression.cpython-311_1.pyc", "size_mb": 0.015891075134277344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression.cpython-311_2.pyc", "size_mb": 0.011364936828613281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression.cpython-311_3.pyc", "size_mb": 0.0077762603759765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression.cpython-311_4.pyc", "size_mb": 0.0030012130737304688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression.cpython-311_5.pyc", "size_mb": 0.01123046875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression.cpython-311_6.pyc", "size_mb": 0.18491649627685547, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_reloading.cpython-311.pyc", "size_mb": 0.0038080215454101562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_character.cpython-311.pyc", "size_mb": 0.00335693359375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_complex.cpython-311.pyc", "size_mb": 0.005578041076660156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_integer.cpython-311.pyc", "size_mb": 0.003926277160644531, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_logical.cpython-311.pyc", "size_mb": 0.005153656005859375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_real.cpython-311.pyc", "size_mb": 0.0066509246826171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_routines.cpython-311.pyc", "size_mb": 0.0020208358764648438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_runtime.cpython-311.pyc", "size_mb": 0.006075859069824219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_sankey.cpython-311.pyc", "size_mb": 0.006915092468261719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalarbuffer.cpython-311.pyc", "size_mb": 0.010170936584472656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalarinherit.cpython-311.pyc", "size_mb": 0.007296562194824219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalarmath.cpython-311.pyc", "size_mb": 0.0788421630859375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalarprint.cpython-311.pyc", "size_mb": 0.02514362335205078, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalar_ctors.cpython-311.pyc", "size_mb": 0.015097618103027344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalar_methods.cpython-311.pyc", "size_mb": 0.018294334411621094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scale.cpython-311.pyc", "size_mb": 0.019062042236328125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scripts.cpython-311.pyc", "size_mb": 0.0029430389404296875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_seed_sequence.cpython-311.pyc", "size_mb": 0.0037240982055664062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_semicolon_split.cpython-311.pyc", "size_mb": 0.002841949462890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_settings.cpython-311-pytest-8.4.1.pyc", "size_mb": 0.0044765472412109375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_shape_base.cpython-311.pyc", "size_mb": 0.06103992462158203, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_shape_base.cpython-311_1.pyc", "size_mb": 0.05524158477783203, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_shell_utils.cpython-311.pyc", "size_mb": 0.0033903121948242188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_simd.cpython-311.pyc", "size_mb": 0.08153533935546875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_simd_module.cpython-311.pyc", "size_mb": 0.007704734802246094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_simplification.cpython-311.pyc", "size_mb": 0.03348827362060547, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_size.cpython-311.pyc", "size_mb": 0.0031061172485351562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_skew.cpython-311.pyc", "size_mb": 0.010069847106933594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_smoke.cpython-311.pyc", "size_mb": 0.058518409729003906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_sphinxext.cpython-311.pyc", "size_mb": 0.012392997741699219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_spines.cpython-311.pyc", "size_mb": 0.01169586181640625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_streamplot.cpython-311.pyc", "size_mb": 0.012251853942871094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_stride_tricks.cpython-311.pyc", "size_mb": 0.03253650665283203, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_string.cpython-311.pyc", "size_mb": 0.006030082702636719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_stringdtype.cpython-311.pyc", "size_mb": 0.10369110107421875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_strings.cpython-311.pyc", "size_mb": 0.07636260986328125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_style.cpython-311.pyc", "size_mb": 0.014375686645507812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_subclassing.cpython-311.pyc", "size_mb": 0.03061962127685547, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_subplots.cpython-311.pyc", "size_mb": 0.017261505126953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_symbol.cpython-311.pyc", "size_mb": 0.013027191162109375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_symbolic.cpython-311.pyc", "size_mb": 0.03621673583984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_system_info.cpython-311.pyc", "size_mb": 0.018918991088867188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_table.cpython-311.pyc", "size_mb": 0.015093803405761719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_testing.cpython-311.pyc", "size_mb": 0.0026578903198242188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_texmanager.cpython-311.pyc", "size_mb": 0.004010200500488281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_text.cpython-311.pyc", "size_mb": 0.06393909454345703, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_textpath.cpython-311.pyc", "size_mb": 0.000835418701171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ticker.cpython-311.pyc", "size_mb": 0.11267852783203125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_tightlayout.cpython-311.pyc", "size_mb": 0.0242462158203125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_transforms.cpython-311.pyc", "size_mb": 0.0827646255493164, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_triangulation.cpython-311.pyc", "size_mb": 0.08228683471679688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_twodim_base.cpython-311.pyc", "size_mb": 0.03322124481201172, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_type1font.cpython-311.pyc", "size_mb": 0.011670112609863281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_type_check.cpython-311.pyc", "size_mb": 0.036670684814453125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_typing.cpython-311.pyc", "size_mb": 0.013052940368652344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ufunc.cpython-311.pyc", "size_mb": 0.23051929473876953, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ufunclike.cpython-311.pyc", "size_mb": 0.006562232971191406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_umath.cpython-311.pyc", "size_mb": 0.36450767517089844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_umath_accuracy.cpython-311.pyc", "size_mb": 0.009546279907226562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_umath_complex.cpython-311.pyc", "size_mb": 0.043343544006347656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_unicode.cpython-311.pyc", "size_mb": 0.019972801208496094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_units.cpython-311.pyc", "size_mb": 0.024687767028808594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_usetex.cpython-311.pyc", "size_mb": 0.011755943298339844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_utils.cpython-311.pyc", "size_mb": 0.004387855529785156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_utils.cpython-311_1.pyc", "size_mb": 0.13422679901123047, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_value_attrspec.cpython-311.pyc", "size_mb": 0.00104522705078125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_warnings.cpython-311.pyc", "size_mb": 0.004662513732910156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_widgets.cpython-311.pyc", "size_mb": 0.08977127075195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_zbar_library.cpython-311.pyc", "size_mb": 0.0074520111083984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__all__.cpython-311.pyc", "size_mb": 0.0008563995361328125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__datasource.cpython-311.pyc", "size_mb": 0.02230548858642578, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__exceptions.cpython-311.pyc", "size_mb": 0.0*****************, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__iotools.cpython-311.pyc", "size_mb": 0.02131938934326172, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__version.cpython-311.pyc", "size_mb": 0.004069328308105469, "can_delete": true}, {"path": "config\\__pycache__\\arabic_fonts.cpython-312.pyc", "size_mb": 0.002471923828125, "can_delete": true}, {"path": "config\\__pycache__\\control_panel_config.cpython-312.pyc", "size_mb": 0.0068798065185546875, "can_delete": true}, {"path": "config\\__pycache__\\scheduler_settings.cpython-311.pyc", "size_mb": 0.0037851333618164062, "can_delete": true}, {"path": "config\\__pycache__\\scheduler_settings.cpython-312.pyc", "size_mb": 0.0035533905029296875, "can_delete": true}, {"path": "config\\__pycache__\\settings.cpython-311.pyc", "size_mb": 0.002079010009765625, "can_delete": true}, {"path": "config\\__pycache__\\settings.cpython-312.pyc", "size_mb": 0.0019960403442382812, "can_delete": true}, {"path": "config\\__pycache__\\settings.cpython-313.pyc", "size_mb": 0.0019893646240234375, "can_delete": true}, {"path": "config\\__pycache__\\settings.cpython-314.pyc", "size_mb": 0.0020666122436523438, "can_delete": true}, {"path": "config\\__pycache__\\sqlserver_config.cpython-311.pyc", "size_mb": 0.0075969696044921875, "can_delete": true}, {"path": "config\\__pycache__\\sqlserver_config.cpython-312.pyc", "size_mb": 0.006987571716308594, "can_delete": true}, {"path": "config\\__pycache__\\__init__.cpython-311.pyc", "size_mb": 0.00017070770263671875, "can_delete": true}, {"path": "config\\__pycache__\\__init__.cpython-312.pyc", "size_mb": 0.00015926361083984375, "can_delete": true}, {"path": "config\\__pycache__\\__init__.cpython-313.pyc", "size_mb": 0.00015926361083984375, "can_delete": true}, {"path": "config\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00012683868408203125, "can_delete": true}, {"path": "core\\__pycache__\\app_core.cpython-312.pyc", "size_mb": 0.015840530395507812, "can_delete": true}, {"path": "core\\__pycache__\\barcode_scanner.cpython-311.pyc", "size_mb": 0.014583587646484375, "can_delete": true}, {"path": "core\\__pycache__\\barcode_scanner.cpython-312.pyc", "size_mb": 0.013654708862304688, "can_delete": true}, {"path": "core\\__pycache__\\barcode_scanner.cpython-314.pyc", "size_mb": 0.014583587646484375, "can_delete": true}, {"path": "core\\__pycache__\\error_handler.cpython-311.pyc", "size_mb": 0.012887001037597656, "can_delete": true}, {"path": "core\\__pycache__\\error_handler.cpython-312.pyc", "size_mb": 0.011513710021972656, "can_delete": true}, {"path": "core\\__pycache__\\error_handler.cpython-314.pyc", "size_mb": 0.01386260986328125, "can_delete": true}, {"path": "core\\__pycache__\\scheduler_manager.cpython-311.pyc", "size_mb": 0.01577472686767578, "can_delete": true}, {"path": "core\\__pycache__\\scheduler_manager.cpython-312.pyc", "size_mb": 0.014657974243164062, "can_delete": true}, {"path": "core\\__pycache__\\scheduler_manager.cpython-313.pyc", "size_mb": 0.014847755432128906, "can_delete": true}, {"path": "core\\__pycache__\\scheduler_manager.cpython-314.pyc", "size_mb": 0.015311241*********, "can_delete": true}, {"path": "core\\__pycache__\\__init__.cpython-311.pyc", "size_mb": 0.0002460479736328125, "can_delete": true}, {"path": "core\\__pycache__\\__init__.cpython-312.pyc", "size_mb": 0.00022983551025390625, "can_delete": true}, {"path": "core\\__pycache__\\__init__.cpython-313.pyc", "size_mb": 0.00022983551025390625, "can_delete": true}, {"path": "core\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00023174285888671875, "can_delete": true}, {"path": "database\\__pycache__\\accounts_manager.cpython-311.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "database\\__pycache__\\accounts_manager.cpython-312.pyc", "size_mb": 0.014154434204101562, "can_delete": true}, {"path": "database\\__pycache__\\accounts_manager.cpython-314.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "database\\__pycache__\\accounts_tree_manager.cpython-312.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "database\\__pycache__\\database_manager.cpython-311.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "database\\__pycache__\\database_manager.cpython-312.pyc", "size_mb": 0.034628868103027344, "can_delete": true}, {"path": "database\\__pycache__\\database_manager.cpython-313.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "database\\__pycache__\\database_manager.cpython-314.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "database\\__pycache__\\hybrid_database_manager.cpython-311.pyc", "size_mb": 0.****************, "can_delete": true}, {"path": "database\\__pycache__\\hybrid_database_manager.cpython-312.pyc", "size_mb": 0.021727561950683594, "can_delete": true}, {"path": "database\\__pycache__\\hybrid_database_manager.cpython-313.pyc", "size_mb": 0.021854400634765625, "can_delete": true}, {"path": "database\\__pycache__\\hybrid_database_manager.cpython-314.pyc", "size_mb": 0.02480316162109375, "can_delete": true}, {"path": "database\\__pycache__\\invoices_database_manager.cpython-312.pyc", "size_mb": 0.022192001342773438, "can_delete": true}, {"path": "database\\__pycache__\\invoices_manager.cpython-312.pyc", "size_mb": 0.019351959228515625, "can_delete": true}, {"path": "database\\__pycache__\\journal_entries_manager.cpython-311.pyc", "size_mb": 0.016023635864257812, "can_delete": true}, {"path": "database\\__pycache__\\journal_entries_manager.cpython-312.pyc", "size_mb": 0.013880729675292969, "can_delete": true}, {"path": "database\\__pycache__\\journal_entries_manager.cpython-314.pyc", "size_mb": 0.01591014862060547, "can_delete": true}, {"path": "database\\__pycache__\\postgresql_manager.cpython-311.pyc", "size_mb": 0.021619796752929688, "can_delete": true}, {"path": "database\\__pycache__\\postgresql_manager.cpython-312.pyc", "size_mb": 0.019695281982421875, "can_delete": true}, {"path": "database\\__pycache__\\postgresql_manager.cpython-314.pyc", "size_mb": 0.020723342895507812, "can_delete": true}, {"path": "database\\__pycache__\\products_manager.cpython-311.pyc", "size_mb": 0.02192974090576172, "can_delete": true}, {"path": "database\\__pycache__\\products_manager.cpython-312.pyc", "size_mb": 0.018235206604003906, "can_delete": true}, {"path": "database\\__pycache__\\products_manager.cpython-314.pyc", "size_mb": 0.02123737335205078, "can_delete": true}, {"path": "database\\__pycache__\\profit_loss_structure_manager.cpython-312.pyc", "size_mb": 0.03663063049316406, "can_delete": true}, {"path": "database\\__pycache__\\reports_manager.cpython-311.pyc", "size_mb": 0.014557838439941406, "can_delete": true}, {"path": "database\\__pycache__\\reports_manager.cpython-312.pyc", "size_mb": 0.012606620788574219, "can_delete": true}, {"path": "database\\__pycache__\\reports_manager.cpython-314.pyc", "size_mb": 0.014691352844238281, "can_delete": true}, {"path": "database\\__pycache__\\sqlserver_manager.cpython-311.pyc", "size_mb": 0.021170616*********, "can_delete": true}, {"path": "database\\__pycache__\\sqlserver_manager.cpython-312.pyc", "size_mb": 0.01902484893798828, "can_delete": true}, {"path": "database\\__pycache__\\warehouse_manager.cpython-312.pyc", "size_mb": 0.026737213134765625, "can_delete": true}, {"path": "database\\__pycache__\\__init__.cpython-311.pyc", "size_mb": 0.00025177001953125, "can_delete": true}, {"path": "database\\__pycache__\\__init__.cpython-312.pyc", "size_mb": 0.00023555755615234375, "can_delete": true}, {"path": "database\\__pycache__\\__init__.cpython-313.pyc", "size_mb": 0.00023555755615234375, "can_delete": true}, {"path": "database\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00020313262939453125, "can_delete": true}, {"path": "models\\__pycache__\\customer.cpython-312.pyc", "size_mb": 0.008401870727539062, "can_delete": true}, {"path": "models\\__pycache__\\invoice.cpython-312.pyc", "size_mb": 0.012442588806152344, "can_delete": true}, {"path": "models\\__pycache__\\product.cpython-312.pyc", "size_mb": 0.011078834533691406, "can_delete": true}, {"path": "models\\__pycache__\\__init__.cpython-311.pyc", "size_mb": 0.00024509429931640625, "can_delete": true}, {"path": "models\\__pycache__\\__init__.cpython-312.pyc", "size_mb": 0.0001811981201171875, "can_delete": true}, {"path": "reports\\__pycache__\\report_generator.cpython-312.pyc", "size_mb": 0.018747329711914062, "can_delete": true}, {"path": "reports\\__pycache__\\__init__.cpython-311.pyc", "size_mb": 0.000247955322265625, "can_delete": true}, {"path": "reports\\__pycache__\\__init__.cpython-312.pyc", "size_mb": 0.00018405914306640625, "can_delete": true}, {"path": "services\\__pycache__\\employees_manager.cpython-312.pyc", "size_mb": 0.013243675231933594, "can_delete": true}, {"path": "services\\__pycache__\\invoice_printer.cpython-312.pyc", "size_mb": 0.019262313842773438, "can_delete": true}, {"path": "services\\__pycache__\\postgresql_sales_manager.cpython-311.pyc", "size_mb": 0.023179054260253906, "can_delete": true}, {"path": "services\\__pycache__\\postgresql_sales_manager.cpython-312.pyc", "size_mb": 0.01953601837158203, "can_delete": true}, {"path": "services\\__pycache__\\purchases_manager.cpython-311.pyc", "size_mb": 0.01801586151123047, "can_delete": true}, {"path": "services\\__pycache__\\purchases_manager.cpython-312.pyc", "size_mb": 0.015697479248046875, "can_delete": true}, {"path": "services\\__pycache__\\purchases_manager.cpython-314.pyc", "size_mb": 0.018175125122070312, "can_delete": true}, {"path": "services\\__pycache__\\sales_manager.cpython-311.pyc", "size_mb": 0.022736549377441406, "can_delete": true}, {"path": "services\\__pycache__\\sales_manager.cpython-312.pyc", "size_mb": 0.019530296325683594, "can_delete": true}, {"path": "services\\__pycache__\\treasury_manager.cpython-312.pyc", "size_mb": 0.010840415954589844, "can_delete": true}, {"path": "services\\__pycache__\\__init__.cpython-311.pyc", "size_mb": 0.0002288818359375, "can_delete": true}, {"path": "services\\__pycache__\\__init__.cpython-312.pyc", "size_mb": 0.00021266937255859375, "can_delete": true}, {"path": "services\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00018024444580078125, "can_delete": true}, {"path": "themes\\__pycache__\\font_manager.cpython-311.pyc", "size_mb": 0.01247406005859375, "can_delete": true}, {"path": "themes\\__pycache__\\font_manager.cpython-312.pyc", "size_mb": 0.011362075805664062, "can_delete": true}, {"path": "themes\\__pycache__\\font_manager.cpython-313.pyc", "size_mb": 0.011567115783691406, "can_delete": true}, {"path": "themes\\__pycache__\\font_manager.cpython-314.pyc", "size_mb": 0.013745307922363281, "can_delete": true}, {"path": "themes\\__pycache__\\modern_theme.cpython-311.pyc", "size_mb": 0.00440216064453125, "can_delete": true}, {"path": "themes\\__pycache__\\modern_theme.cpython-312.pyc", "size_mb": 0.004185676574707031, "can_delete": true}, {"path": "themes\\__pycache__\\modern_theme.cpython-313.pyc", "size_mb": 0.004181861877441406, "can_delete": true}, {"path": "themes\\__pycache__\\modern_theme.cpython-314.pyc", "size_mb": 0.004300117492675781, "can_delete": true}, {"path": "themes\\__pycache__\\theme_manager.cpython-311.pyc", "size_mb": 0.011178016662597656, "can_delete": true}, {"path": "themes\\__pycache__\\theme_manager.cpython-312.pyc", "size_mb": 0.009917259216308594, "can_delete": true}, {"path": "themes\\__pycache__\\theme_manager.cpython-313.pyc", "size_mb": 0.009974479675292969, "can_delete": true}, {"path": "themes\\__pycache__\\theme_manager.cpython-314.pyc", "size_mb": 0.0108318***********, "can_delete": true}, {"path": "themes\\__pycache__\\__init__.cpython-311.pyc", "size_mb": 0.00023937225341796875, "can_delete": true}, {"path": "themes\\__pycache__\\__init__.cpython-312.pyc", "size_mb": 0.0002231597900390625, "can_delete": true}, {"path": "themes\\__pycache__\\__init__.cpython-313.pyc", "size_mb": 0.0002231597900390625, "can_delete": true}, {"path": "themes\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "ui\\__pycache__\\accounts_window.cpython-311.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "ui\\__pycache__\\accounts_window.cpython-314.pyc", "size_mb": 0.020836830139160156, "can_delete": true}, {"path": "ui\\__pycache__\\add_items_window.cpython-311.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "ui\\__pycache__\\add_items_window.cpython-314.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "ui\\__pycache__\\advanced_financial_reports_window.cpython-311.pyc", "size_mb": 0.053328514099121094, "can_delete": true}, {"path": "ui\\__pycache__\\advanced_financial_reports_window.cpython-314.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "ui\\__pycache__\\advanced_sections.cpython-311.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "ui\\__pycache__\\advanced_sections.cpython-313.pyc", "size_mb": 0.*****************, "can_delete": true}, {"path": "ui\\__pycache__\\advanced_sections.cpython-314.pyc", "size_mb": 0.053122520446777344, "can_delete": true}, {"path": "ui\\__pycache__\\advanced_sections_part2.cpython-311.pyc", "size_mb": 0.029102325439453125, "can_delete": true}, {"path": "ui\\__pycache__\\advanced_sections_part2.cpython-313.pyc", "size_mb": 0.025625228881835938, "can_delete": true}, {"path": "ui\\__pycache__\\advanced_sections_part2.cpython-314.pyc", "size_mb": 0.027428627014160156, "can_delete": true}, {"path": "ui\\__pycache__\\categories_management_window.cpython-311.pyc", "size_mb": 0.034758567810058594, "can_delete": true}, {"path": "ui\\__pycache__\\categories_management_window.cpython-314.pyc", "size_mb": 0.033286094665527344, "can_delete": true}, {"path": "ui\\__pycache__\\central_control_panel.cpython-311.pyc", "size_mb": 0.14520740509033203, "can_delete": true}, {"path": "ui\\__pycache__\\central_control_panel.cpython-313.pyc", "size_mb": 0.12894248962402344, "can_delete": true}, {"path": "ui\\__pycache__\\central_control_panel.cpython-314.pyc", "size_mb": 0.1337423324584961, "can_delete": true}, {"path": "ui\\__pycache__\\control_panel_integration.cpython-311.pyc", "size_mb": 0.02389812469482422, "can_delete": true}, {"path": "ui\\__pycache__\\control_panel_integration.cpython-313.pyc", "size_mb": 0.02152729034423828, "can_delete": true}, {"path": "ui\\__pycache__\\control_panel_integration.cpython-314.pyc", "size_mb": 0.022513389587402344, "can_delete": true}, {"path": "ui\\__pycache__\\employees_window.cpython-311.pyc", "size_mb": 0.04389762878417969, "can_delete": true}, {"path": "ui\\__pycache__\\hr_management_window.cpython-311.pyc", "size_mb": 0.095916748046875, "can_delete": true}, {"path": "ui\\__pycache__\\hr_management_window.cpython-314.pyc", "size_mb": 0.09140872955322266, "can_delete": true}, {"path": "ui\\__pycache__\\invoices_main_window.cpython-311.pyc", "size_mb": 0.019158363342285156, "can_delete": true}, {"path": "ui\\__pycache__\\invoices_main_window.cpython-314.pyc", "size_mb": 0.018036842346191406, "can_delete": true}, {"path": "ui\\__pycache__\\journal_entries_window.cpython-311.pyc", "size_mb": 0.026060104370117188, "can_delete": true}, {"path": "ui\\__pycache__\\journal_entries_window.cpython-314.pyc", "size_mb": 0.024547576904296875, "can_delete": true}, {"path": "ui\\__pycache__\\login_window.cpython-311.pyc", "size_mb": 0.01111602783203125, "can_delete": true}, {"path": "ui\\__pycache__\\login_window.cpython-313.pyc", "size_mb": 0.010389328002929688, "can_delete": true}, {"path": "ui\\__pycache__\\login_window.cpython-314.pyc", "size_mb": 0.010667800903320312, "can_delete": true}, {"path": "ui\\__pycache__\\main_window.cpython-311.pyc", "size_mb": 0.10721302032470703, "can_delete": true}, {"path": "ui\\__pycache__\\main_window.cpython-313.pyc", "size_mb": 0.11851215362548828, "can_delete": true}, {"path": "ui\\__pycache__\\main_window.cpython-314.pyc", "size_mb": 0.12742042541503906, "can_delete": true}, {"path": "ui\\__pycache__\\purchases_window.cpython-311.pyc", "size_mb": 0.02803516387939453, "can_delete": true}, {"path": "ui\\__pycache__\\purchases_window.cpython-314.pyc", "size_mb": 0.02608966827392578, "can_delete": true}, {"path": "ui\\__pycache__\\reports_window.cpython-311.pyc", "size_mb": 0.0272064208984375, "can_delete": true}, {"path": "ui\\__pycache__\\reports_window.cpython-314.pyc", "size_mb": 0.025724411010742188, "can_delete": true}, {"path": "ui\\__pycache__\\sales_window.cpython-311.pyc", "size_mb": 0.026801109313964844, "can_delete": true}, {"path": "ui\\__pycache__\\sales_window.cpython-314.pyc", "size_mb": 0.025053024291992188, "can_delete": true}, {"path": "ui\\__pycache__\\simple_welcome_window.cpython-311.pyc", "size_mb": 0.013544082641601562, "can_delete": true}, {"path": "ui\\__pycache__\\simple_welcome_window.cpython-314.pyc", "size_mb": 0.012700080871582031, "can_delete": true}, {"path": "ui\\__pycache__\\stock_management_window.cpython-311.pyc", "size_mb": 0.07214641571044922, "can_delete": true}, {"path": "ui\\__pycache__\\stock_management_window.cpython-314.pyc", "size_mb": 0.06811809539794922, "can_delete": true}, {"path": "ui\\__pycache__\\units_management_window.cpython-311.pyc", "size_mb": 0.027052879333496094, "can_delete": true}, {"path": "ui\\__pycache__\\units_management_window.cpython-314.pyc", "size_mb": 0.025763511657714844, "can_delete": true}, {"path": "ui\\__pycache__\\user_management.cpython-311.pyc", "size_mb": 0.024837493896484375, "can_delete": true}, {"path": "ui\\__pycache__\\user_management.cpython-314.pyc", "size_mb": 0.024399757385253906, "can_delete": true}, {"path": "ui\\__pycache__\\warehouses_management_window.cpython-311.pyc", "size_mb": 0.09922981262207031, "can_delete": true}, {"path": "ui\\__pycache__\\warehouses_management_window.cpython-314.pyc", "size_mb": 0.092437744140625, "can_delete": true}, {"path": "ui\\__pycache__\\window_utils.cpython-311.pyc", "size_mb": 0.006500244140625, "can_delete": true}, {"path": "ui\\__pycache__\\window_utils.cpython-313.pyc", "size_mb": 0.005679130554199219, "can_delete": true}, {"path": "ui\\__pycache__\\window_utils.cpython-314.pyc", "size_mb": 0.0057353973388671875, "can_delete": true}, {"path": "ui\\__pycache__\\__init__.cpython-311.pyc", "size_mb": 0.00023555755615234375, "can_delete": true}, {"path": "ui\\__pycache__\\__init__.cpython-313.pyc", "size_mb": 0.0002193450927734375, "can_delete": true}, {"path": "ui\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.000186920166015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0008440017700195312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\__pycache__\\__main__.cpython-314.pyc", "size_mb": 0.0008373260498046875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\__pycache__\\__pip-runner__.cpython-314.pyc", "size_mb": 0.0022563934326171875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\__pycache__\\build_env.cpython-314.pyc", "size_mb": 0.017124176025390625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\__pycache__\\cache.cpython-314.pyc", "size_mb": 0.015115737915039062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\__pycache__\\configuration.cpython-314.pyc", "size_mb": 0.02106475830078125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\__pycache__\\exceptions.cpython-314.pyc", "size_mb": 0.04606342315673828, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\__pycache__\\main.cpython-314.pyc", "size_mb": 0.000823974609375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\__pycache__\\pyproject.cpython-314.pyc", "size_mb": 0.005680084228515625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\__pycache__\\self_outdated_check.cpython-314.pyc", "size_mb": 0.012203216552734375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\__pycache__\\wheel_builder.cpython-314.pyc", "size_mb": 0.015059471130371094, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0009336471557617188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\autocompletion.cpython-314.pyc", "size_mb": 0.009479522705078125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\base_command.cpython-314.pyc", "size_mb": 0.011794090270996094, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\cmdoptions.cpython-314.pyc", "size_mb": 0.03936290740966797, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\command_context.cpython-314.pyc", "size_mb": 0.002346038818359375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\index_command.cpython-314.pyc", "size_mb": 0.008381843566894531, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\main.cpython-314.pyc", "size_mb": 0.0024089813232421875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\main_parser.cpython-314.pyc", "size_mb": 0.005331993103027344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\parser.cpython-314.pyc", "size_mb": 0.017510414123535156, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\progress_bars.cpython-314.pyc", "size_mb": 0.006923675537109375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\req_command.cpython-314.pyc", "size_mb": 0.014190673828125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\spinners.cpython-314.pyc", "size_mb": 0.01023101806640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\status_codes.cpython-314.pyc", "size_mb": 0.00034999847412109375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.000278472900390625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\cache.cpython-314.pyc", "size_mb": 0.011687278747558594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\check.cpython-314.pyc", "size_mb": 0.0028362274169921875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\completion.cpython-314.pyc", "size_mb": 0.005679130554199219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\configuration.cpython-314.pyc", "size_mb": 0.015209197998046875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\debug.cpython-314.pyc", "size_mb": 0.011582374572753906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\download.cpython-314.pyc", "size_mb": 0.0076084136962890625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\freeze.cpython-314.pyc", "size_mb": 0.004878997802734375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\hash.cpython-314.pyc", "size_mb": 0.00341796875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\help.cpython-314.pyc", "size_mb": 0.0019083023071289062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\index.cpython-314.pyc", "size_mb": 0.007540702819824219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\inspect.cpython-314.pyc", "size_mb": 0.0044269561767578125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\install.cpython-314.pyc", "size_mb": 0.03076457977294922, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\list.cpython-314.pyc", "size_mb": 0.018935203552246094, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\lock.cpython-314.pyc", "size_mb": 0.008082389831542969, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\search.cpython-314.pyc", "size_mb": 0.009329795837402344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\show.cpython-314.pyc", "size_mb": 0.012494087219238281, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\uninstall.cpython-314.pyc", "size_mb": 0.004973411560058594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\wheel.cpython-314.pyc", "size_mb": 0.008910179138183594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.004513740539550781, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\distributions\\__pycache__\\base.cpython-314.pyc", "size_mb": 0.0034036636352539062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\distributions\\__pycache__\\installed.cpython-314.pyc", "size_mb": 0.002178192138671875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\distributions\\__pycache__\\sdist.cpython-314.pyc", "size_mb": 0.009821891784667969, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\distributions\\__pycache__\\wheel.cpython-314.pyc", "size_mb": 0.0027475357055664062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\distributions\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0010900497436523438, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\index\\__pycache__\\collector.cpython-314.pyc", "size_mb": 0.025710105895996094, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\index\\__pycache__\\package_finder.cpython-314.pyc", "size_mb": 0.04635143280029297, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\index\\__pycache__\\sources.cpython-314.pyc", "size_mb": 0.016080856323242188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\index\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0002346038818359375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\locations\\__pycache__\\base.cpython-314.pyc", "size_mb": 0.004416465759277344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\locations\\__pycache__\\_distutils.cpython-314.pyc", "size_mb": 0.007587432861328125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\locations\\__pycache__\\_sysconfig.cpython-314.pyc", "size_mb": 0.008959770202636719, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\locations\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.01888275146484375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\metadata\\__pycache__\\base.cpython-314.pyc", "size_mb": 0.041896820068359375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\metadata\\__pycache__\\pkg_resources.cpython-314.pyc", "size_mb": 0.02051544189453125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\metadata\\__pycache__\\_json.cpython-314.pyc", "size_mb": 0.003360748291015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\metadata\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.007731437683105469, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\__pycache__\\_compat.cpython-314.pyc", "size_mb": 0.005504608154296875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\__pycache__\\_dists.cpython-314.pyc", "size_mb": 0.01596546173095703, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\__pycache__\\_envs.cpython-314.pyc", "size_mb": 0.00943756103515625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00035762786865234375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\candidate.cpython-314.pyc", "size_mb": 0.00214385986328125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\direct_url.cpython-314.pyc", "size_mb": 0.014475822448730469, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\format_control.cpython-314.pyc", "size_mb": 0.0051937103271484375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\index.cpython-314.pyc", "size_mb": 0.0020580291748046875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\installation_report.cpython-314.pyc", "size_mb": 0.0028524398803710938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\link.cpython-314.pyc", "size_mb": 0.033469200134277344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\pylock.cpython-314.pyc", "size_mb": 0.010232925415039062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\scheme.cpython-314.pyc", "size_mb": 0.0012712478637695312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\search_scope.cpython-314.pyc", "size_mb": 0.005810737609863281, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\selection_prefs.cpython-314.pyc", "size_mb": 0.0019826889038085938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\target_python.cpython-314.pyc", "size_mb": 0.005343437194824219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\wheel.cpython-314.pyc", "size_mb": 0.008144378662109375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00026702880859375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__\\auth.cpython-314.pyc", "size_mb": 0.02645587921142578, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__\\cache.cpython-314.pyc", "size_mb": 0.008666038513183594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__\\download.cpython-314.pyc", "size_mb": 0.015008926391601562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__\\lazy_wheel.cpython-314.pyc", "size_mb": 0.014039039611816406, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__\\session.cpython-314.pyc", "size_mb": 0.02167797088623047, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__\\utils.cpython-314.pyc", "size_mb": 0.0027570724487304688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__\\xmlrpc.cpython-314.pyc", "size_mb": 0.0032787322998046875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.000255584716796875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\__pycache__\\check.cpython-314.pyc", "size_mb": 0.008368492126464844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\__pycache__\\freeze.cpython-314.pyc", "size_mb": 0.011626243591308594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\__pycache__\\prepare.cpython-314.pyc", "size_mb": 0.02978229522705078, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0002040863037109375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__\\build_tracker.cpython-314.pyc", "size_mb": 0.009077072143554688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__\\metadata.cpython-314.pyc", "size_mb": 0.0020008087158203125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__\\metadata_editable.cpython-314.pyc", "size_mb": 0.00205230712890625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__\\metadata_legacy.cpython-314.pyc", "size_mb": 0.00324249267578125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__\\wheel.cpython-314.pyc", "size_mb": 0.0018396377563476562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__\\wheel_editable.cpython-314.pyc", "size_mb": 0.0021696090698242188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__\\wheel_legacy.cpython-314.pyc", "size_mb": 0.004929542541503906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.000209808349609375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\__pycache__\\editable_legacy.cpython-314.pyc", "size_mb": 0.0020322799682617188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\__pycache__\\wheel.cpython-314.pyc", "size_mb": 0.039910316467285156, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00026702880859375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\req\\__pycache__\\constructors.cpython-314.pyc", "size_mb": 0.025031089782714844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\req\\__pycache__\\req_dependency_group.cpython-314.pyc", "size_mb": 0.004810333251953125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\req\\__pycache__\\req_file.cpython-314.pyc", "size_mb": 0.028466224670410156, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\req\\__pycache__\\req_install.cpython-314.pyc", "size_mb": 0.04446220397949219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\req\\__pycache__\\req_set.cpython-314.pyc", "size_mb": 0.006751060485839844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\req\\__pycache__\\req_uninstall.cpython-314.pyc", "size_mb": 0.03678703308105469, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\req\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00479888916015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\__pycache__\\base.cpython-314.pyc", "size_mb": 0.001598358154296875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0002040863037109375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy\\__pycache__\\resolver.cpython-314.pyc", "size_mb": 0.02462291717529297, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00021076202392578125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__\\base.cpython-314.pyc", "size_mb": 0.011175155639648438, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__\\candidates.cpython-314.pyc", "size_mb": 0.036952972412109375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__\\factory.cpython-314.pyc", "size_mb": 0.03745460510253906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__\\found_candidates.cpython-314.pyc", "size_mb": 0.007933616638183594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__\\provider.cpython-314.pyc", "size_mb": 0.012647628784179688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__\\reporter.cpython-314.pyc", "size_mb": 0.006484031677246094, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__\\requirements.cpython-314.pyc", "size_mb": 0.02101421356201172, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__\\resolver.cpython-314.pyc", "size_mb": 0.013318061828613281, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00021457672119140625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\appdirs.cpython-314.pyc", "size_mb": 0.0029993057250976562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\compat.cpython-314.pyc", "size_mb": 0.0032720565795898438, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\compatibility_tags.cpython-314.pyc", "size_mb": 0.008390426635742188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\datetime.cpython-314.pyc", "size_mb": 0.0008039474487304688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\deprecation.cpython-314.pyc", "size_mb": 0.0049190521240234375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\direct_url_helpers.cpython-314.pyc", "size_mb": 0.0040721893310546875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\egg_link.cpython-314.pyc", "size_mb": 0.00354766845703125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\entrypoints.cpython-314.pyc", "size_mb": 0.004445075988769531, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\filesystem.cpython-314.pyc", "size_mb": 0.008401870727539062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\filetypes.cpython-314.pyc", "size_mb": 0.001708984375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\glibc.cpython-314.pyc", "size_mb": 0.0029458999633789062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\hashes.cpython-314.pyc", "size_mb": 0.009577751159667969, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\logging.cpython-314.pyc", "size_mb": 0.01708984375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\misc.cpython-314.pyc", "size_mb": 0.04189491271972656, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\packaging.cpython-314.pyc", "size_mb": 0.00212860107421875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\retry.cpython-314.pyc", "size_mb": 0.0025568008422851562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\setuptools_build.cpython-314.pyc", "size_mb": 0.005625724792480469, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\subprocess.cpython-314.pyc", "size_mb": 0.009903907775878906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\temp_dir.cpython-314.pyc", "size_mb": 0.014434814453125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\unpacking.cpython-314.pyc", "size_mb": 0.015150070190429688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\urls.cpython-314.pyc", "size_mb": 0.0023641586303710938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\virtualenv.cpython-314.pyc", "size_mb": 0.0052165985107421875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\wheel.cpython-314.pyc", "size_mb": 0.006638526916503906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\_jaraco_text.cpython-314.pyc", "size_mb": 0.0042018890380859375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\_log.cpython-314.pyc", "size_mb": 0.0022907257080078125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00019931793212890625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__pycache__\\bazaar.cpython-314.pyc", "size_mb": 0.006237983703613281, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__pycache__\\git.cpython-314.pyc", "size_mb": 0.022075653076171875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__pycache__\\mercurial.cpython-314.pyc", "size_mb": 0.008860588073730469, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__pycache__\\subversion.cpython-314.pyc", "size_mb": 0.01474761962890625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__pycache__\\versioncontrol.cpython-314.pyc", "size_mb": 0.034423828125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.000522613525390625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\__pycache__\\typing_extensions.cpython-314.pyc", "size_mb": 0.18163299560546875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0044384002685546875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__\\adapter.cpython-314.pyc", "size_mb": 0.007216453552246094, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__\\cache.cpython-314.pyc", "size_mb": 0.005153656005859375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__\\controller.cpython-314.pyc", "size_mb": 0.017932891845703125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__\\filewrapper.cpython-314.pyc", "size_mb": 0.005069732666015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__\\heuristics.cpython-314.pyc", "size_mb": 0.008146286010742188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__\\serialize.cpython-314.pyc", "size_mb": 0.005866050720214844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__\\wrapper.cpython-314.pyc", "size_mb": 0.001735687255859375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__\\_cmd.cpython-314.pyc", "size_mb": 0.0030889511108398438, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0008678436279296875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\__pycache__\\file_cache.cpython-314.pyc", "size_mb": 0.008612632751464844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\__pycache__\\redis_cache.cpython-314.pyc", "size_mb": 0.0035104751586914062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00043201446533203125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\__pycache__\\core.cpython-314.pyc", "size_mb": 0.004155158996582031, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0003204345703125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\__pycache__\\__main__.cpython-314.pyc", "size_mb": 0.0006456375122070312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__pycache__\\_implementation.cpython-314.pyc", "size_mb": 0.010549545288085938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__pycache__\\_lint_dependency_groups.cpython-314.pyc", "size_mb": 0.0029592514038085938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__pycache__\\_pip_wrapper.cpython-314.pyc", "size_mb": 0.0036497116088867188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__pycache__\\_toml_compat.cpython-314.pyc", "size_mb": 0.00046253204345703125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00037670135498046875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__pycache__\\__main__.cpython-314.pyc", "size_mb": 0.0028219223022460938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\compat.cpython-314.pyc", "size_mb": 0.04492759704589844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\database.cpython-314.pyc", "size_mb": 0.06366157531738281, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\index.cpython-314.pyc", "size_mb": 0.02286529541015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\locators.cpython-314.pyc", "size_mb": 0.05814647674560547, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\manifest.cpython-314.pyc", "size_mb": 0.014674186706542969, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\markers.cpython-314.pyc", "size_mb": 0.007628440856933594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\metadata.cpython-314.pyc", "size_mb": 0.04195690155029297, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\resources.cpython-314.pyc", "size_mb": 0.01718616485595703, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\scripts.cpython-314.pyc", "size_mb": 0.01986408233642578, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\util.cpython-314.pyc", "size_mb": 0.08844470977783203, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\version.cpython-314.pyc", "size_mb": 0.030549049377441406, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\wheel.cpython-314.pyc", "size_mb": 0.053374290466308594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0013589859008789062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__pycache__\\distro.cpython-314.pyc", "size_mb": 0.0584869384765625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0009326934814453125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__pycache__\\__main__.cpython-314.pyc", "size_mb": 0.000301361083984375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__\\codec.cpython-314.pyc", "size_mb": 0.006069183349609375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__\\compat.cpython-314.pyc", "size_mb": 0.0012922286987304688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__\\core.cpython-314.pyc", "size_mb": 0.01942920684814453, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__\\idnadata.cpython-314.pyc", "size_mb": 0.09488868713378906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__\\intranges.cpython-314.pyc", "size_mb": 0.0032720565795898438, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__\\package_data.cpython-314.pyc", "size_mb": 0.00022411346435546875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__\\uts46data.cpython-314.pyc", "size_mb": 0.2901782989501953, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00086212158203125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\__pycache__\\exceptions.cpython-314.pyc", "size_mb": 0.0021142959594726562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\__pycache__\\ext.cpython-314.pyc", "size_mb": 0.007920265197753906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\__pycache__\\fallback.cpython-314.pyc", "size_mb": 0.040866851806640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0016536712646484375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\markers.cpython-314.pyc", "size_mb": 0.014636039733886719, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\metadata.cpython-314.pyc", "size_mb": 0.029755592346191406, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\requirements.cpython-314.pyc", "size_mb": 0.005313873291015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\specifiers.cpython-314.pyc", "size_mb": 0.04275989532470703, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\tags.cpython-314.pyc", "size_mb": 0.028284072875976562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\utils.cpython-314.pyc", "size_mb": 0.0073184967041015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\version.cpython-314.pyc", "size_mb": 0.023149490356445312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\_elffile.cpython-314.pyc", "size_mb": 0.005452156066894531, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\_manylinux.cpython-314.pyc", "size_mb": 0.011460304260253906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\_musllinux.cpython-314.pyc", "size_mb": 0.004920005798339844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\_parser.cpython-314.pyc", "size_mb": 0.01687335968017578, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\_structures.cpython-314.pyc", "size_mb": 0.005196571350097656, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\_tokenizer.cpython-314.pyc", "size_mb": 0.009066581726074219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00054931640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses\\__pycache__\\_spdx.cpython-314.pyc", "size_mb": 0.049559593200683594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0043582916259765625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pkg_resources\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.18083763122558594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__\\android.cpython-314.pyc", "size_mb": 0.012982368469238281, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__\\api.cpython-314.pyc", "size_mb": 0.018027305603027344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__\\macos.cpython-314.pyc", "size_mb": 0.010629653930664062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__\\unix.cpython-314.pyc", "size_mb": 0.01738452911376953, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__\\version.cpython-314.pyc", "size_mb": 0.0010366439819335938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__\\windows.cpython-314.pyc", "size_mb": 0.01610851287841797, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.02193737030029297, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__\\__main__.cpython-314.pyc", "size_mb": 0.0020046234130859375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\console.cpython-314.pyc", "size_mb": 0.002696990966796875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\filter.cpython-314.pyc", "size_mb": 0.0031719207763671875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\formatter.cpython-314.pyc", "size_mb": 0.004427909851074219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\lexer.cpython-314.pyc", "size_mb": 0.037845611572265625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\modeline.cpython-314.pyc", "size_mb": 0.0015459060668945312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\plugin.cpython-314.pyc", "size_mb": 0.0024566650390625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\regexopt.cpython-314.pyc", "size_mb": 0.004183769226074219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\scanner.cpython-314.pyc", "size_mb": 0.004559516906738281, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\sphinxext.cpython-314.pyc", "size_mb": 0.012159347534179688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\style.cpython-314.pyc", "size_mb": 0.006969451904296875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\token.cpython-314.pyc", "size_mb": 0.007970809936523438, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\unistring.cpython-314.pyc", "size_mb": 0.031699180603027344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\util.cpython-314.pyc", "size_mb": 0.013876914978027344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0033245086669921875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__\\__main__.cpython-314.pyc", "size_mb": 0.0006914138793945312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\filters\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.036728858947753906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\__pycache__\\_mapping.cpython-314.pyc", "size_mb": 0.0040378570556640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00681304931640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__pycache__\\python.cpython-314.pyc", "size_mb": 0.047692298889160156, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__pycache__\\_mapping.cpython-314.pyc", "size_mb": 0.06663322448730469, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.014545440673828125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles\\__pycache__\\_mapping.cpython-314.pyc", "size_mb": 0.0035648345947265625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0026464462280273438, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\__pycache__\\_impl.cpython-314.pyc", "size_mb": 0.020720481872558594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0007419586181640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\__pycache__\\_in_process.cpython-314.pyc", "size_mb": 0.015206336975097656, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0010499954223632812, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\adapters.cpython-314.pyc", "size_mb": 0.026717185974121094, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\api.cpython-314.pyc", "size_mb": 0.0066070556640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\auth.cpython-314.pyc", "size_mb": 0.013866424560546875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\certs.cpython-314.pyc", "size_mb": 0.0006656646728515625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\compat.cpython-314.pyc", "size_mb": 0.0016183853149414062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\cookies.cpython-314.pyc", "size_mb": 0.024301528930664062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\exceptions.cpython-314.pyc", "size_mb": 0.0076694488525390625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\help.cpython-314.pyc", "size_mb": 0.004168510437011719, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\hooks.cpython-314.pyc", "size_mb": 0.0010690689086914062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\models.cpython-314.pyc", "size_mb": 0.034781455993652344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\packages.cpython-314.pyc", "size_mb": 0.001316070556640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\sessions.cpython-314.pyc", "size_mb": 0.02648162841796875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\status_codes.cpython-314.pyc", "size_mb": 0.005753517150878906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\structures.cpython-314.pyc", "size_mb": 0.0054874420166015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\utils.cpython-314.pyc", "size_mb": 0.03558921813964844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\_internal_utils.cpython-314.pyc", "size_mb": 0.0019426345825195312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0051021575927734375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__\\__version__.cpython-314.pyc", "size_mb": 0.0005750656127929688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\__pycache__\\providers.cpython-314.pyc", "size_mb": 0.00986480712890625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\__pycache__\\reporters.cpython-314.pyc", "size_mb": 0.0042572021484375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\__pycache__\\structs.cpython-314.pyc", "size_mb": 0.01598358154296875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0006227493286132812, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\__pycache__\\abstract.cpython-314.pyc", "size_mb": 0.0027036666870117188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\__pycache__\\criterion.cpython-314.pyc", "size_mb": 0.0036706924438476562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\__pycache__\\exceptions.cpython-314.pyc", "size_mb": 0.0049953460693359375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\__pycache__\\resolution.cpython-314.pyc", "size_mb": 0.02408123016357422, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0007181167602539062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\abc.cpython-314.pyc", "size_mb": 0.0018377304077148438, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\align.cpython-314.pyc", "size_mb": 0.014425277709960938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\ansi.cpython-314.pyc", "size_mb": 0.009767532348632812, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\bar.cpython-314.pyc", "size_mb": 0.004984855651855469, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\box.cpython-314.pyc", "size_mb": 0.013859748840332031, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\cells.cpython-314.pyc", "size_mb": 0.0061626434326171875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\color.cpython-314.pyc", "size_mb": 0.027365684509277344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\color_triplet.cpython-314.pyc", "size_mb": 0.0022840499877929688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\columns.cpython-314.pyc", "size_mb": 0.009380340576171875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\console.cpython-314.pyc", "size_mb": 0.13135051727294922, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\constrain.cpython-314.pyc", "size_mb": 0.0027017593383789062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\containers.cpython-314.pyc", "size_mb": 0.01163482666015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\control.cpython-314.pyc", "size_mb": 0.013088226318359375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\default_styles.cpython-314.pyc", "size_mb": 0.010413169860839844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\diagnose.cpython-314.pyc", "size_mb": 0.0016002655029296875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\emoji.cpython-314.pyc", "size_mb": 0.004881858825683594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\errors.cpython-314.pyc", "size_mb": 0.0019445419311523438, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\filesize.cpython-314.pyc", "size_mb": 0.0035581588745117188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\file_proxy.cpython-314.pyc", "size_mb": 0.0044803619384765625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\highlighter.cpython-314.pyc", "size_mb": 0.010690689086914062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\json.cpython-314.pyc", "size_mb": 0.0062694549560546875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\jupyter.cpython-314.pyc", "size_mb": 0.00634765625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\layout.cpython-314.pyc", "size_mb": 0.02415943145751953, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\live.cpython-314.pyc", "size_mb": 0.02206134796142578, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\live_render.cpython-314.pyc", "size_mb": 0.005450248718261719, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\logging.cpython-314.pyc", "size_mb": 0.014929771423339844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\markup.cpython-314.pyc", "size_mb": 0.010809898376464844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\measure.cpython-314.pyc", "size_mb": 0.0071239471435546875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\padding.cpython-314.pyc", "size_mb": 0.0077953338623046875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\pager.cpython-314.pyc", "size_mb": 0.00234222412109375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\palette.cpython-314.pyc", "size_mb": 0.0063037872314453125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\panel.cpython-314.pyc", "size_mb": 0.013888359069824219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\pretty.cpython-314.pyc", "size_mb": 0.047313690185546875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\progress.cpython-314.pyc", "size_mb": 0.09074592590332031, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\progress_bar.cpython-314.pyc", "size_mb": 0.011660575866699219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\prompt.cpython-314.pyc", "size_mb": 0.018891334533691406, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\protocol.cpython-314.pyc", "size_mb": 0.0019922256469726562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\region.cpython-314.pyc", "size_mb": 0.0008420944213867188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\repr.cpython-314.pyc", "size_mb": 0.008475303649902344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\rule.cpython-314.pyc", "size_mb": 0.007358551025390625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\scope.cpython-314.pyc", "size_mb": 0.00423431396484375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\screen.cpython-314.pyc", "size_mb": 0.0029878616333007812, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\segment.cpython-314.pyc", "size_mb": 0.032143592834472656, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\spinner.cpython-314.pyc", "size_mb": 0.006890296936035156, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\status.cpython-314.pyc", "size_mb": 0.0073032379150390625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\style.cpython-314.pyc", "size_mb": 0.04016685485839844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\styled.cpython-314.pyc", "size_mb": 0.0025968551635742188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\syntax.cpython-314.pyc", "size_mb": 0.044686317443847656, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\table.cpython-314.pyc", "size_mb": 0.0492706298828125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\terminal_theme.cpython-314.pyc", "size_mb": 0.0035228729248046875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\text.cpython-314.pyc", "size_mb": 0.06972885131835938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\theme.cpython-314.pyc", "size_mb": 0.0074481964111328125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\themes.cpython-314.pyc", "size_mb": 0.00031757354736328125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\traceback.cpython-314.pyc", "size_mb": 0.040389060974121094, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\tree.cpython-314.pyc", "size_mb": 0.0126495361328125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_cell_widths.cpython-314.pyc", "size_mb": 0.015130996704101562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_emoji_codes.cpython-314.pyc", "size_mb": 0.19646930694580078, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_emoji_replace.cpython-314.pyc", "size_mb": 0.0020494461059570312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_export_format.cpython-314.pyc", "size_mb": 0.00225830078125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_extension.cpython-314.pyc", "size_mb": 0.0006666183471679688, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_fileno.cpython-314.pyc", "size_mb": 0.0009708404541015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_inspect.cpython-314.pyc", "size_mb": 0.013968467712402344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_log_render.cpython-314.pyc", "size_mb": 0.00467681884765625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_loop.cpython-314.pyc", "size_mb": 0.0024242401123046875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_null_file.cpython-314.pyc", "size_mb": 0.006329536437988281, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_palettes.cpython-314.pyc", "size_mb": 0.008975028991699219, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_pick.cpython-314.pyc", "size_mb": 0.0008640289306640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_ratio.cpython-314.pyc", "size_mb": 0.007468223571777344, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_spinners.cpython-314.pyc", "size_mb": 0.015005111694335938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_stack.cpython-314.pyc", "size_mb": 0.0013456344604492188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_timer.cpython-314.pyc", "size_mb": 0.001007080078125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_win32_console.cpython-314.pyc", "size_mb": 0.03199291229248047, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_windows.cpython-314.pyc", "size_mb": 0.0029649734497070312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_windows_renderer.cpython-314.pyc", "size_mb": 0.0037174224853515625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\_wrap.cpython-314.pyc", "size_mb": 0.0035085678100585938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.008021354675292969, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__\\__main__.cpython-314.pyc", "size_mb": 0.01061248779296875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\__pycache__\\_parser.cpython-314.pyc", "size_mb": 0.034012794494628906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\__pycache__\\_re.cpython-314.pyc", "size_mb": 0.0044918060302734375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\__pycache__\\_types.cpython-314.pyc", "size_mb": 0.00038242340087890625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00034046173095703125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\tomli_w\\__pycache__\\_writer.cpython-314.pyc", "size_mb": 0.012546539306640625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\tomli_w\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0003299713134765625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__pycache__\\_api.cpython-314.pyc", "size_mb": 0.023092269897460938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__pycache__\\_macos.cpython-314.pyc", "size_mb": 0.020105361938476562, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__pycache__\\_openssl.cpython-314.pyc", "size_mb": 0.00269317626953125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__pycache__\\_ssl_constants.cpython-314.pyc", "size_mb": 0.0014810562133789062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__pycache__\\_windows.cpython-314.pyc", "size_mb": 0.016618728637695312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0014095306396484375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\connection.cpython-314.pyc", "size_mb": 0.02016448974609375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\connectionpool.cpython-314.pyc", "size_mb": 0.035198211669921875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\exceptions.cpython-314.pyc", "size_mb": 0.013737678527832031, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\fields.cpython-314.pyc", "size_mb": 0.009932518005371094, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\filepost.cpython-314.pyc", "size_mb": 0.0038633346557617188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\poolmanager.cpython-314.pyc", "size_mb": 0.019426345825195312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\request.cpython-314.pyc", "size_mb": 0.006770133972167969, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\response.cpython-314.pyc", "size_mb": 0.03337669372558594, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\_collections.cpython-314.pyc", "size_mb": 0.0161590576171875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\_version.cpython-314.pyc", "size_mb": 0.00022602081298828125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0032339096069335938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__pycache__\\appengine.cpython-314.pyc", "size_mb": 0.011515617370605469, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__pycache__\\ntlmpool.cpython-314.pyc", "size_mb": 0.005579948425292969, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__pycache__\\pyopenssl.cpython-314.pyc", "size_mb": 0.024019241333007812, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__pycache__\\securetransport.cpython-314.pyc", "size_mb": 0.034882545471191406, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__pycache__\\socks.cpython-314.pyc", "size_mb": 0.007599830627441406, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__pycache__\\_appengine_environ.cpython-314.pyc", "size_mb": 0.0018329620361328125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00020694732666015625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\__pycache__\\bindings.cpython-314.pyc", "size_mb": 0.01665019989013672, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\__pycache__\\low_level.cpython-314.pyc", "size_mb": 0.014283180236816406, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0002231597900390625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\__pycache__\\six.cpython-314.pyc", "size_mb": 0.04091930389404297, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.0002079010009765625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\__pycache__\\makefile.cpython-314.pyc", "size_mb": 0.0018930435180664062, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\__pycache__\\weakref_finalize.cpython-314.pyc", "size_mb": 0.007370948791503906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.000217437744140625, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\connection.cpython-314.pyc", "size_mb": 0.004624366760253906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\proxy.cpython-314.pyc", "size_mb": 0.0015382766723632812, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\queue.cpython-314.pyc", "size_mb": 0.0014238357543945312, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\request.cpython-314.pyc", "size_mb": 0.0039882659912109375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\response.cpython-314.pyc", "size_mb": 0.002941131591796875, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\retry.cpython-314.pyc", "size_mb": 0.020880699157714844, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\ssltransport.cpython-314.pyc", "size_mb": 0.010607719421386719, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\ssl_.cpython-314.pyc", "size_mb": 0.015043258666992188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\ssl_match_hostname.cpython-314.pyc", "size_mb": 0.0050868988037109375, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\timeout.cpython-314.pyc", "size_mb": 0.010300636291503906, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\url.cpython-314.pyc", "size_mb": 0.015920639038085938, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\wait.cpython-314.pyc", "size_mb": 0.0044727325439453125, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__\\__init__.cpython-314.pyc", "size_mb": 0.00110626220703125, "can_delete": true}], "log_files": [{"path": "logs\\app.log", "size_mb": 0.5467844009399414, "can_delete": true}, {"path": "logs\\app_errors_20250716.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250717.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250718.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250719.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250720.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250722.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250723.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250725.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250728.log", "size_mb": 0.0010280609130859375, "can_delete": true}, {"path": "logs\\app_fixed.log", "size_mb": 0.07669544219970703, "can_delete": true}, {"path": "logs\\critical_fixes.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\deep_audit_20250722_205809.log", "size_mb": 0.0013189315795898438, "can_delete": true}, {"path": "logs\\deep_audit_20250722_210514.log", "size_mb": 0.0013189315795898438, "can_delete": true}, {"path": "logs\\scheduler_test.log", "size_mb": 0.0011167526245117188, "can_delete": true}, {"path": "logs\\sqlserver_test.log", "size_mb": 0.0013589859008789062, "can_delete": true}, {"path": "logs\\system_check.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\validation_test.log", "size_mb": 0.0021200180053710938, "can_delete": true}, {"path": "logs\\app.log", "size_mb": 0.5467844009399414, "can_delete": true}, {"path": "logs\\app_errors_20250716.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250717.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250718.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250719.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250720.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250722.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250723.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250725.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\app_errors_20250728.log", "size_mb": 0.0010280609130859375, "can_delete": true}, {"path": "logs\\app_fixed.log", "size_mb": 0.07669544219970703, "can_delete": true}, {"path": "logs\\critical_fixes.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\deep_audit_20250722_205809.log", "size_mb": 0.0013189315795898438, "can_delete": true}, {"path": "logs\\deep_audit_20250722_210514.log", "size_mb": 0.0013189315795898438, "can_delete": true}, {"path": "logs\\scheduler_test.log", "size_mb": 0.0011167526245117188, "can_delete": true}, {"path": "logs\\sqlserver_test.log", "size_mb": 0.0013589859008789062, "can_delete": true}, {"path": "logs\\system_check.log", "size_mb": 0.0, "can_delete": true}, {"path": "logs\\validation_test.log", "size_mb": 0.0021200180053710938, "can_delete": true}, {"path": "cleanup_report_20250718_235201.json", "size_mb": 0.0073947906494140625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_AUDIT_REPORT_20250725_185838.json", "size_mb": 1.2622852325439453, "can_delete": true}, {"path": "COMPLETE_SYSTEM_PRESERVATION_REPORT_20250725_193755.json", "size_mb": 0.008877754211425781, "can_delete": true}, {"path": "COMPREHENSIVE_AUDIT_REPORT_20250725_190045.json", "size_mb": 0.4972095489501953, "can_delete": true}, {"path": "comprehensive_functional_test_report_20250722_084903.json", "size_mb": 0.0030755996704101562, "can_delete": true}, {"path": "comprehensive_systematic_audit_report_20250722_084138.json", "size_mb": 0.017122268676757812, "can_delete": true}, {"path": "comprehensive_system_report_20250720_015846.json", "size_mb": 0.007917404174804688, "can_delete": true}, {"path": "comprehensive_system_report_20250720_021713.json", "size_mb": 0.013388633728027344, "can_delete": true}, {"path": "comprehensive_system_report_20250720_024630.json", "size_mb": 0.013345718383789062, "can_delete": true}, {"path": "comprehensive_system_report_20250720_194025.json", "size_mb": 0.012454986572265625, "can_delete": true}, {"path": "comprehensive_system_report_20250720_195721.json", "size_mb": 0.012454986572265625, "can_delete": true}, {"path": "comprehensive_validation_report_20250722_204949.json", "size_mb": 0.0032291412353515625, "can_delete": true}, {"path": "comprehensive_validation_report_20250722_205127.json", "size_mb": 0.003147125244140625, "can_delete": true}, {"path": "comprehensive_validation_report_20250722_210820.json", "size_mb": 0.003147125244140625, "can_delete": true}, {"path": "database_analysis_report_20250718_235408.json", "size_mb": 0.029114723205566406, "can_delete": true}, {"path": "deep_comprehensive_audit_report_20250722_205812.json", "size_mb": 0.06027030944824219, "can_delete": true}, {"path": "deep_comprehensive_audit_report_20250722_210518.json", "size_mb": 0.05966949462890625, "can_delete": true}, {"path": "deep_comprehensive_fix_report_20250720_195920.json", "size_mb": 0.0012912750244140625, "can_delete": true}, {"path": "deep_error_analysis_report_20250719_010043.json", "size_mb": 0.009621620178222656, "can_delete": true}, {"path": "error_fix_report_20250720_020023.json", "size_mb": 0.0016536712646484375, "can_delete": true}, {"path": "error_fix_report_20250720_021646.json", "size_mb": 0.0016536712646484375, "can_delete": true}, {"path": "final_cleanup_report_20250720_025539.json", "size_mb": 0.0036563873291015625, "can_delete": true}, {"path": "performance_analysis_report_20250718_235910.json", "size_mb": 0.053124427795410156, "can_delete": true}, {"path": "systematic_repair_report_20250722_084405.json", "size_mb": 0.0027942657470703125, "can_delete": true}, {"path": "ultimate_advanced_fix_report_20250722_081809.json", "size_mb": 0.0009698867797851562, "can_delete": true}, {"path": "ultimate_diagnostic_report_20250722_081611.json", "size_mb": 0.006758689880371094, "can_delete": true}, {"path": "ultimate_fix_report_20250720_024827.json", "size_mb": 0.001739501953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\cleanup_report_20250718_235201.json", "size_mb": 0.0073947906494140625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_functional_test_report_20250722_084903.json", "size_mb": 0.0030755996704101562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_systematic_audit_report_20250722_084138.json", "size_mb": 0.017122268676757812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_system_report_20250720_015846.json", "size_mb": 0.007917404174804688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_system_report_20250720_021713.json", "size_mb": 0.013388633728027344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_system_report_20250720_024630.json", "size_mb": 0.013345718383789062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_system_report_20250720_194025.json", "size_mb": 0.012454986572265625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_system_report_20250720_195721.json", "size_mb": 0.012454986572265625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_validation_report_20250722_204949.json", "size_mb": 0.0032291412353515625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_validation_report_20250722_205127.json", "size_mb": 0.003147125244140625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\comprehensive_validation_report_20250722_210820.json", "size_mb": 0.003147125244140625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\database_analysis_report_20250718_235408.json", "size_mb": 0.029114723205566406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\deep_comprehensive_audit_report_20250722_205812.json", "size_mb": 0.06027030944824219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\deep_comprehensive_audit_report_20250722_210518.json", "size_mb": 0.05966949462890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\deep_comprehensive_fix_report_20250720_195920.json", "size_mb": 0.0012912750244140625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\deep_error_analysis_report_20250719_010043.json", "size_mb": 0.009621620178222656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\error_fix_report_20250720_020023.json", "size_mb": 0.0016536712646484375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\error_fix_report_20250720_021646.json", "size_mb": 0.0016536712646484375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\final_cleanup_report_20250720_025539.json", "size_mb": 0.0036563873291015625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\performance_analysis_report_20250718_235910.json", "size_mb": 0.053124427795410156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\systematic_repair_report_20250722_084405.json", "size_mb": 0.0027942657470703125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\ultimate_advanced_fix_report_20250722_081809.json", "size_mb": 0.0009698867797851562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\ultimate_diagnostic_report_20250722_081611.json", "size_mb": 0.006758689880371094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\configuration\\ultimate_fix_report_20250720_024827.json", "size_mb": 0.001739501953125, "can_delete": true}, {"path": "database_check_report_20250716_043252.md", "size_mb": 0.0008697509765625, "can_delete": true}, {"path": "data_integrity_report_20250716_045527.md", "size_mb": 0.0015811920166015625, "can_delete": true}, {"path": "data_quality_report_20250716_045319.md", "size_mb": 0.0022039413452148438, "can_delete": true}, {"path": "FINAL_DEEP_AUDIT_REPORT_2025.md", "size_mb": 0.007870674133300781, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\database_check_report_20250716_043252.md", "size_mb": 0.0008697509765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\data_integrity_report_20250716_045527.md", "size_mb": 0.0015811920166015625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\data_quality_report_20250716_045319.md", "size_mb": 0.0022039413452148438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\FINAL_DEEP_AUDIT_REPORT_2025.md", "size_mb": 0.007870674133300781, "can_delete": true}], "test_files": [{"path": "test_admin_panel_icon.py", "size_mb": 0.003192901611328125, "can_delete": true}, {"path": "test_advanced_control_panel.py", "size_mb": 0.006541252136230469, "can_delete": true}, {"path": "test_control_panel.py", "size_mb": 0.0033197402954101562, "can_delete": true}, {"path": "test_control_panel_simple.py", "size_mb": 0.004000663757324219, "can_delete": true}, {"path": "test_customtkinter.py", "size_mb": 0.0009822845458984375, "can_delete": true}, {"path": "test_database.py", "size_mb": 0.000957489013671875, "can_delete": true}, {"path": "test_hr_window.py", "size_mb": 0.0020914077758789062, "can_delete": true}, {"path": "test_import.py", "size_mb": 0.000873565673828125, "can_delete": true}, {"path": "test_main_window.py", "size_mb": 0.0034418106079101562, "can_delete": true}, {"path": "test_settings.py", "size_mb": 0.0032405853271484375, "can_delete": true}, {"path": "test_settings_removal.py", "size_mb": 0.005097389221191406, "can_delete": true}, {"path": "test_simple.py", "size_mb": 0.0007228851318359375, "can_delete": true}, {"path": "test_title_bar.py", "size_mb": 0.004558563232421875, "can_delete": true}, {"path": "test_ui.py", "size_mb": 0.0012521743774414062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_abc.py", "size_mb": 0.0021696090698242188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_abstract_interface.py", "size_mb": 0.0007944107055664062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_admin_panel_icon.py", "size_mb": 0.003192901611328125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_advanced_control_panel.py", "size_mb": 0.006541252136230469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_afm.py", "size_mb": 0.0035295486450195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_agg.py", "size_mb": 0.010379791259765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_agg_filter.py", "size_mb": 0.0010175704956054688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_angle_helper.py", "size_mb": 0.0054073333740234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_animation.py", "size_mb": 0.01750659942626953, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_api.py", "size_mb": 0.00547027587890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_api_1.py", "size_mb": 0.022455215454101562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_argparse.py", "size_mb": 0.0027790069580078125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arraymethod.py", "size_mb": 0.0031843185424804688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrayobject.py", "size_mb": 0.0010862350463867188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrayobject_1.py", "size_mb": 0.0025472640991210938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arraypad.py", "size_mb": 0.05481719970703125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrayprint.py", "size_mb": 0.04801654815673828, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arraysetops.py", "size_mb": 0.037215232849121094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrayterator.py", "size_mb": 0.0012750625610351562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_api_info.py", "size_mb": 0.0030269622802734375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_coercion.py", "size_mb": 0.034088134765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_from_pyobj.py", "size_mb": 0.02324962615966797, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_interface.py", "size_mb": 0.0076160430908203125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_array_utils.py", "size_mb": 0.0010986328125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_arrow_patches.py", "size_mb": 0.006272315979003906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_art3d.py", "size_mb": 0.0031633377075195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_artist.py", "size_mb": 0.017737388610839844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_assumed_shape.py", "size_mb": 0.0014448165893554688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axes.py", "size_mb": 0.3133869171142578, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axes3d.py", "size_mb": 0.08806991577148438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axes_grid1.py", "size_mb": 0.02774524688720703, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axis.py", "size_mb": 0.0013790130615234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axislines.py", "size_mb": 0.004151344299316406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_axis_artist.py", "size_mb": 0.002841949462890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backends_interactive.py", "size_mb": 0.027675628662109375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_bases.py", "size_mb": 0.021719932556152344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_cairo.py", "size_mb": 0.0017366409301757812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_gtk3.py", "size_mb": 0.0027170181274414062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_inline.py", "size_mb": 0.00153350830078125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_macosx.py", "size_mb": 0.0021295547485351562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_nbagg.py", "size_mb": 0.0013914108276367188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_pdf.py", "size_mb": 0.013925552368164062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_pgf.py", "size_mb": 0.012424468994140625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_ps.py", "size_mb": 0.01201629638671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_qt.py", "size_mb": 0.01239776611328125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_registry.py", "size_mb": 0.0056743621826171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_svg.py", "size_mb": 0.021867752075195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_template.py", "size_mb": 0.00208282470703125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_tk.py", "size_mb": 0.008440017700195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_tools.py", "size_mb": 0.00047779083251953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_backend_webagg.py", "size_mb": 0.0008945465087890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_basic.py", "size_mb": 0.0010881423950195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_bbox_tight.py", "size_mb": 0.0060214996337890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_bezier.py", "size_mb": 0.000659942626953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_block_docstring.py", "size_mb": 0.00057220458984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_build_ext.py", "size_mb": 0.0027208328247070312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_callback.py", "size_mb": 0.007033348083496094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_casting_floatingpoint_errors.py", "size_mb": 0.004985809326171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_casting_unittests.py", "size_mb": 0.03349876403808594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_category.py", "size_mb": 0.011485099792480469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cbook.py", "size_mb": 0.03197669982910156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ccompiler_opt.py", "size_mb": 0.028215408325195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ccompiler_opt_conf.py", "size_mb": 0.006220817565917969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_character.py", "size_mb": 0.0214996337890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_chebyshev.py", "size_mb": 0.02016162872314453, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_classes.py", "size_mb": 0.018174171447753906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_collections.py", "size_mb": 0.04616355895996094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_colorbar.py", "size_mb": 0.044547080993652344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_colors.py", "size_mb": 0.058379173278808594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_common.py", "size_mb": 0.0006303787231445312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_compare_images.py", "size_mb": 0.003108978271484375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_configtool.py", "size_mb": 0.0015239715576171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_constrainedlayout.py", "size_mb": 0.022508621215820312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_container.py", "size_mb": 0.0006618499755859375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_contour.py", "size_mb": 0.028885841369628906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_control_panel.py", "size_mb": 0.0033197402954101562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_control_panel_simple.py", "size_mb": 0.004000663757324219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_conversion_utils.py", "size_mb": 0.0064983367919921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_core.py", "size_mb": 0.2146291732788086, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cpu_dispatcher.py", "size_mb": 0.0015230178833007812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cpu_features.py", "size_mb": 0.015104293823242188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_crackfortran.py", "size_mb": 0.016022682189941406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ctypeslib.py", "size_mb": 0.01213836669921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_customtkinter.py", "size_mb": 0.0009822845458984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_custom_dtypes.py", "size_mb": 0.011381149291992188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cycles.py", "size_mb": 0.005718231201171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_cython.py", "size_mb": 0.008509635925292969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_data.py", "size_mb": 0.0028209686279296875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_database.py", "size_mb": 0.000957489013671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_dates.py", "size_mb": 0.05391407012939453, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_datetime.py", "size_mb": 0.03112316131591797, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_datetime_1.py", "size_mb": 0.11861419677734375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_defchararray.py", "size_mb": 0.029967308044433594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_defmatrix.py", "size_mb": 0.0147705078125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_deprecations.py", "size_mb": 0.000629425048828125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_deprecations_1.py", "size_mb": 0.0025272369384765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_deprecations_2.py", "size_mb": 0.027899742126464844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_determinism.py", "size_mb": 0.0075893402099609375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_direct.py", "size_mb": 0.01958465576171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_dlpack.py", "size_mb": 0.0057125091552734375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_doc.py", "size_mb": 0.0009679794311523438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_docs.py", "size_mb": 0.0018205642700195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_dtype.py", "size_mb": 0.07568836212158203, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_dviread.py", "size_mb": 0.002635955810546875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_einsum.py", "size_mb": 0.051611900329589844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_errstate.py", "size_mb": 0.004542350769042969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_exec_command.py", "size_mb": 0.007259368896484375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_extending.py", "size_mb": 0.004349708557128906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_extint128.py", "size_mb": 0.005591392517089844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_extras.py", "size_mb": 0.07655525207519531, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_f2cmap.py", "size_mb": 0.0003814697265625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_f2py2e.py", "size_mb": 0.027496337890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fcompiler.py", "size_mb": 0.00125885009765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fcompiler_gnu.py", "size_mb": 0.0020895004272460938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fcompiler_intel.py", "size_mb": 0.00103759765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fcompiler_nagfor.py", "size_mb": 0.001071929931640625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_figure.py", "size_mb": 0.057496070861816406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_floating_axes.py", "size_mb": 0.0038938522338867188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_fontconfig_pattern.py", "size_mb": 0.00206756591796875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_font_manager.py", "size_mb": 0.01348114013671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_format.py", "size_mb": 0.039994239807128906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_from_template.py", "size_mb": 0.0010938644409179688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ft2font.py", "size_mb": 0.03894615173339844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_function_base.py", "size_mb": 0.16528892517089844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_function_base_1.py", "size_mb": 0.017118453979492188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_generator_mt19937.py", "size_mb": 0.11452198028564453, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_generator_mt19937_regressions.py", "size_mb": 0.007915496826171875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_getattr.py", "size_mb": 0.0010395050048828125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_getlimits.py", "size_mb": 0.006847381591796875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_gridspec.py", "size_mb": 0.001506805419921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_grid_finder.py", "size_mb": 0.001102447509765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_grid_helper_curvelinear.py", "size_mb": 0.0068817138671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_half.py", "size_mb": 0.0237274169921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_hashtable.py", "size_mb": 0.0011272430419921875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_helper.py", "size_mb": 0.006023406982421875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_hermite.py", "size_mb": 0.01824474334716797, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_hermite_e.py", "size_mb": 0.01856517791748047, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_histograms.py", "size_mb": 0.032935142517089844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_hr_window.py", "size_mb": 0.0020914077758789062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_image.py", "size_mb": 0.05680274963378906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_import.py", "size_mb": 0.000873565673828125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_indexerrors.py", "size_mb": 0.0046329498291015625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_indexing.py", "size_mb": 0.05411243438720703, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_index_tricks.py", "size_mb": 0.019951820373535156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_interaction.py", "size_mb": 0.011681556701660156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_io.py", "size_mb": 0.10763931274414062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_isfile.py", "size_mb": 0.0008554458618164062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_isoc.py", "size_mb": 0.0014123916625976562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_item_selection.py", "size_mb": 0.006316184997558594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_kind.py", "size_mb": 0.00174713134765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_laguerre.py", "size_mb": 0.017210960388183594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_lazyloading.py", "size_mb": 0.001140594482421875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_legend.py", "size_mb": 0.05228900909423828, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_legend3d.py", "size_mb": 0.004141807556152344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_legendre.py", "size_mb": 0.01834869384765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_limited_api.py", "size_mb": 0.003246307373046875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_linalg.py", "size_mb": 0.08173084259033203, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_lines.py", "size_mb": 0.014338493347167969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_loadtxt.py", "size_mb": 0.03969383239746094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_locations.py", "size_mb": 0.0016222000122070312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_log.py", "size_mb": 0.0008602142333984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_longdouble.py", "size_mb": 0.013623237609863281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_machar.py", "size_mb": 0.0010480880737304688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_main_window.py", "size_mb": 0.0034418106079101562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_marker.py", "size_mb": 0.010881423950195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_masked_matrix.py", "size_mb": 0.008722305297851562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mathtext.py", "size_mb": 0.02341175079345703, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_matlib.py", "size_mb": 0.0018215179443359375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_matplotlib.py", "size_mb": 0.0027532577514648438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_matrix_linalg.py", "size_mb": 0.00205230712890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_memmap.py", "size_mb": 0.007984161376953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mem_overlap.py", "size_mb": 0.028677940368652344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mem_policy.py", "size_mb": 0.01631641387939453, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mingw32ccompiler.py", "size_mb": 0.0015745162963867188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_misc_util.py", "size_mb": 0.003292083740234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mixed.py", "size_mb": 0.0008516311645507812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mixins.py", "size_mb": 0.0069103240966796875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mlab.py", "size_mb": 0.04031085968017578, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_modules.py", "size_mb": 0.002269744873046875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_mrecords.py", "size_mb": 0.019405364990234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_multiarray.py", "size_mb": 0.0005435943603515625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_multiarray_1.py", "size_mb": 0.38399696350097656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_multithreading.py", "size_mb": 0.008486747741699219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_multivariate_colormaps.py", "size_mb": 0.019822120666503906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_nanfunctions.py", "size_mb": 0.05222511291503906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_nditer.py", "size_mb": 0.12831687927246094, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_nep50_promotions.py", "size_mb": 0.009874343872070312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_npy_pkg_config.py", "size_mb": 0.0025186538696289062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numeric.py", "size_mb": 0.0004367828369140625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numerictypes.py", "size_mb": 0.02280426025390625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numeric_1.py", "size_mb": 0.15516281127929688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numpy_config.py", "size_mb": 0.0012178421020507812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_numpy_version.py", "size_mb": 0.0017242431640625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_offsetbox.py", "size_mb": 0.015389442443847656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_old_ma.py", "size_mb": 0.03207111358642578, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_overrides.py", "size_mb": 0.02740192413330078, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_packbits.py", "size_mb": 0.01708984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_parameter.py", "size_mb": 0.004532814025878906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_patches.py", "size_mb": 0.03191566467285156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_path.py", "size_mb": 0.021131515502929688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_patheffects.py", "size_mb": 0.007733345031738281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pickle.py", "size_mb": 0.009550094604492188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_png.py", "size_mb": 0.0013418197631835938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pocketfft.py", "size_mb": 0.023840904235839844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_polar.py", "size_mb": 0.016791343688964844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_polynomial.py", "size_mb": 0.011187553405761719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_polynomial_1.py", "size_mb": 0.021610260009765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_polyutils.py", "size_mb": 0.0037164688110351562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_preprocess_data.py", "size_mb": 0.010836601257324219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_print.py", "size_mb": 0.0067119598388671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_printing.py", "size_mb": 0.020841598510742188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_protocols.py", "size_mb": 0.0011768341064453125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_public_api.py", "size_mb": 0.027154922485351562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pyf_src.py", "size_mb": 0.0011243820190429688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pyinstaller.py", "size_mb": 0.0011157989501953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pyplot.py", "size_mb": 0.013265609741210938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_pyzbar.py", "size_mb": 0.008413314819335938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_quiver.py", "size_mb": 0.011399269104003906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_quoted_character.py", "size_mb": 0.00047016143798828125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_random.py", "size_mb": 0.06857013702392578, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_randomstate.py", "size_mb": 0.08335018157958984, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_randomstate_regression.py", "size_mb": 0.007834434509277344, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_rcparams.py", "size_mb": 0.025278091430664062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_read_zbar.py", "size_mb": 0.001247406005859375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_recfunctions.py", "size_mb": 0.04294300079345703, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_records.py", "size_mb": 0.020097732543945312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression.py", "size_mb": 0.005743980407714844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression_1.py", "size_mb": 0.007554054260253906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression_2.py", "size_mb": 0.0065479278564453125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression_3.py", "size_mb": 0.00322723388671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression_4.py", "size_mb": 0.0009183883666992188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression_5.py", "size_mb": 0.005351066589355469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_regression_6.py", "size_mb": 0.09351539611816406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_reloading.py", "size_mb": 0.0023279190063476562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_character.py", "size_mb": 0.0014848709106445312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_complex.py", "size_mb": 0.0023660659790039062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_integer.py", "size_mb": 0.0017452239990234375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_logical.py", "size_mb": 0.0019845962524414062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_return_real.py", "size_mb": 0.0031986236572265625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_routines.py", "size_mb": 0.0007839202880859375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_runtime.py", "size_mb": 0.00322723388671875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_sankey.py", "size_mb": 0.003719329833984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalarbuffer.py", "size_mb": 0.005469322204589844, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalarinherit.py", "size_mb": 0.0025682449340820312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalarmath.py", "size_mb": 0.045581817626953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalarprint.py", "size_mb": 0.020082473754882812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalar_ctors.py", "size_mb": 0.006602287292480469, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scalar_methods.py", "size_mb": 0.008953094482421875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scale.py", "size_mb": 0.008038520812988281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_scripts.py", "size_mb": 0.001613616943359375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_seed_sequence.py", "size_mb": 0.0032339096069335938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_semicolon_split.py", "size_mb": 0.001621246337890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_settings.py", "size_mb": 0.0032405853271484375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_settings_removal.py", "size_mb": 0.005097389221191406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_shape_base.py", "size_mb": 0.02698516845703125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_shape_base_1.py", "size_mb": 0.030366897583007812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_shell_utils.py", "size_mb": 0.0020914077758789062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_simd.py", "size_mb": 0.047669410705566406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_simd_module.py", "size_mb": 0.003818511962890625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_simple.py", "size_mb": 0.0007228851318359375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_simplification.py", "size_mb": 0.020570755004882812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_size.py", "size_mb": 0.0011425018310546875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_skew.py", "size_mb": 0.006054878234863281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_smoke.py", "size_mb": 0.02764892578125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_sphinxext.py", "size_mb": 0.009476661682128906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_spines.py", "size_mb": 0.004681587219238281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_streamplot.py", "size_mb": 0.005465507507324219, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_stride_tricks.py", "size_mb": 0.022548675537109375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_string.py", "size_mb": 0.0028934478759765625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_stringdtype.py", "size_mb": 0.05673503875732422, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_strings.py", "size_mb": 0.050617218017578125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_style.py", "size_mb": 0.006207466125488281, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_subclassing.py", "size_mb": 0.01665973663330078, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_subplots.py", "size_mb": 0.010272026062011719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_symbol.py", "size_mb": 0.005329132080078125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_symbolic.py", "size_mb": 0.01796245574951172, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_system_info.py", "size_mb": 0.011195182800292969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_table.py", "size_mb": 0.008257865905761719, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_testing.py", "size_mb": 0.0010080337524414062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_texmanager.py", "size_mb": 0.0025243759155273438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_text.py", "size_mb": 0.036690711975097656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_textpath.py", "size_mb": 0.00025844573974609375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ticker.py", "size_mb": 0.07129573822021484, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_tightlayout.py", "size_mb": 0.013330459594726562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_title_bar.py", "size_mb": 0.004558563232421875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_transforms.py", "size_mb": 0.046416282653808594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_triangulation.py", "size_mb": 0.052727699279785156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_twodim_base.py", "size_mb": 0.018484115600585938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_type1font.py", "size_mb": 0.006073951721191406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_type_check.py", "size_mb": 0.014443397521972656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_typing.py", "size_mb": 0.008195877075195312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ufunc.py", "size_mb": 0.12932395935058594, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_ufunclike.py", "size_mb": 0.0029783248901367188, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_umath.py", "size_mb": 0.18890857696533203, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_umath_accuracy.py", "size_mb": 0.005333900451660156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_umath_complex.py", "size_mb": 0.02280426025390625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_unicode.py", "size_mb": 0.012608528137207031, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_units.py", "size_mb": 0.011134147644042969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_usetex.py", "size_mb": 0.006108283996582031, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_utils.py", "size_mb": 0.0023403167724609375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_utils_1.py", "size_mb": 0.0690317153930664, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_value_attrspec.py", "size_mb": 0.00032711029052734375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_warnings.py", "size_mb": 0.0023097991943359375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_widgets.py", "size_mb": 0.06335735321044922, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test_zbar_library.py", "size_mb": 0.004147529602050781, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__all__.py", "size_mb": 0.0002193450927734375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__datasource.py", "size_mb": 0.010415077209472656, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__exceptions.py", "size_mb": 0.0028324127197265625, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__iotools.py", "size_mb": 0.013443946838378906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\test__version.py", "size_mb": 0.0019674301147460938, "can_delete": true}, {"path": "comprehensive_validation_test.py", "size_mb": 0.012385368347167969, "can_delete": true}, {"path": "comprehensive_validation_test.py", "size_mb": 0.012385368347167969, "can_delete": true}], "temp_files": [{"path": "temp_clean_end.py", "size_mb": 0.0006589889526367188, "can_delete": true}, {"path": "ui\\.venv\\Lib\\site-packages\\pip\\_internal\\utils\\temp_dir.py", "size_mb": 0.008878707885742188, "can_delete": true}, {"path": "search_temp.py", "size_mb": 0.0007581710815429688, "can_delete": true}, {"path": "search_temp.py", "size_mb": 0.0007581710815429688, "can_delete": true}, {"path": "temp_clean_end.py", "size_mb": 0.0006589889526367188, "can_delete": true}], "fixer_files": [{"path": "advanced_error_fixer.py", "size_mb": 0.011957168579101562, "can_delete": true}, {"path": "advanced_syntax_fixer.py", "size_mb": 0.010446548461914062, "can_delete": true}, {"path": "comma_fixer.py", "size_mb": 0.0016584396362304688, "can_delete": true}, {"path": "comprehensive_import_fixer.py", "size_mb": 0.012722015380859375, "can_delete": true}, {"path": "comprehensive_syntax_fixer.py", "size_mb": 0.013601303100585938, "can_delete": true}, {"path": "critical_file_fixer.py", "size_mb": 0.01038360595703125, "can_delete": true}, {"path": "critical_issues_fixer.py", "size_mb": 0.011714935302734375, "can_delete": true}, {"path": "deep_comprehensive_fixer.py", "size_mb": 0.017498016357421875, "can_delete": true}, {"path": "deep_import_fixer.py", "size_mb": 0.011707305908203125, "can_delete": true}, {"path": "error_checker_and_fixer.py", "size_mb": 0.008790016174316406, "can_delete": true}, {"path": "escape_character_fixer.py", "size_mb": 0.0014705657958984375, "can_delete": true}, {"path": "except_block_fixer.py", "size_mb": 0.006028175354003906, "can_delete": true}, {"path": "precise_syntax_fixer.py", "size_mb": 0.009267807006835938, "can_delete": true}, {"path": "quick_pattern_fixer.py", "size_mb": 0.0035572052001953125, "can_delete": true}, {"path": "syntax_error_fixer.py", "size_mb": 0.009771347045898438, "can_delete": true}, {"path": "ultimate_advanced_fixer.py", "size_mb": 0.015225410461425781, "can_delete": true}, {"path": "ultimate_system_fixer.py", "size_mb": 0.017274856567382812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\advanced_error_fixer.py", "size_mb": 0.011957168579101562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\advanced_syntax_fixer.py", "size_mb": 0.010446548461914062, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\comma_fixer.py", "size_mb": 0.0016584396362304688, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\comprehensive_import_fixer.py", "size_mb": 0.012722015380859375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\comprehensive_syntax_fixer.py", "size_mb": 0.013601303100585938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\critical_file_fixer.py", "size_mb": 0.01038360595703125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\critical_issues_fixer.py", "size_mb": 0.011714935302734375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\deep_comprehensive_fixer.py", "size_mb": 0.017498016357421875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\deep_import_fixer.py", "size_mb": 0.011707305908203125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\error_checker_and_fixer.py", "size_mb": 0.008790016174316406, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\escape_character_fixer.py", "size_mb": 0.0014705657958984375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\except_block_fixer.py", "size_mb": 0.006028175354003906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\precise_syntax_fixer.py", "size_mb": 0.009267807006835938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\quick_pattern_fixer.py", "size_mb": 0.0035572052001953125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\syntax_error_fixer.py", "size_mb": 0.009771347045898438, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\ultimate_advanced_fixer.py", "size_mb": 0.015225410461425781, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\ultimate_system_fixer.py", "size_mb": 0.017274856567382812, "can_delete": true}, {"path": "advanced_error_analyzer.py", "size_mb": 0.015498161315917969, "can_delete": true}, {"path": "comprehensive_file_cleanup_analyzer.py", "size_mb": 0.01649188995361328, "can_delete": true}, {"path": "database_analyzer.py", "size_mb": 0.011368751525878906, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\advanced_error_analyzer.py", "size_mb": 0.015498161315917969, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\database_analyzer.py", "size_mb": 0.011368751525878906, "can_delete": true}, {"path": "comprehensive_dependency_checker.py", "size_mb": 0.021783828735351562, "can_delete": true}, {"path": "comprehensive_system_checker.py", "size_mb": 0.015703201293945312, "can_delete": true}, {"path": "quick_missing_files_checker.py", "size_mb": 0.0048122406005859375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\comprehensive_dependency_checker.py", "size_mb": 0.021783828735351562, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\comprehensive_system_checker.py", "size_mb": 0.015703201293945312, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\quick_missing_files_checker.py", "size_mb": 0.0048122406005859375, "can_delete": true}, {"path": "quick_diagnostic.py", "size_mb": 0.006873130798339844, "can_delete": true}, {"path": "ultimate_comprehensive_diagnostic.py", "size_mb": 0.015069007873535156, "can_delete": true}, {"path": "comprehensive_import_fixer.py", "size_mb": 0.012722015380859375, "can_delete": true}, {"path": "comprehensive_syntax_fixer.py", "size_mb": 0.013601303100585938, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\comprehensive_import_fixer.py", "size_mb": 0.012722015380859375, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\comprehensive_syntax_fixer.py", "size_mb": 0.013601303100585938, "can_delete": true}, {"path": "ultimate_advanced_fixer.py", "size_mb": 0.015225410461425781, "can_delete": true}, {"path": "ultimate_system_fixer.py", "size_mb": 0.017274856567382812, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\ultimate_advanced_fixer.py", "size_mb": 0.015225410461425781, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\ultimate_system_fixer.py", "size_mb": 0.017274856567382812, "can_delete": true}, {"path": "deep_comprehensive_fixer.py", "size_mb": 0.017498016357421875, "can_delete": true}, {"path": "deep_import_fixer.py", "size_mb": 0.011707305908203125, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\deep_comprehensive_fixer.py", "size_mb": 0.017498016357421875, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\utility_scripts\\deep_import_fixer.py", "size_mb": 0.011707305908203125, "can_delete": true}], "duplicate_files": [{"path": "ui\\employees_window_fixed.py", "size_mb": 0.023736000061035156, "can_delete": true}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\ui_components\\employees_window_fixed.py", "size_mb": 0.023736000061035156, "can_delete": true}]}, "files_to_keep": {}, "backup_analysis": {"backup_directories": [{"name": "backup", "size_mb": 0.0, "file_count": 0}, {"name": "backups", "size_mb": 0.265625, "file_count": 2}, {"name": "backup_critical_files", "size_mb": 0.2893800735473633, "file_count": 8}, {"name": "backup_deep", "size_mb": 0.954289436340332, "file_count": 34}, {"name": "backup_final", "size_mb": 2.1288156509399414, "file_count": 106}, {"name": "backup_fixes", "size_mb": 2.9152002334594727, "file_count": 91}, {"name": "backup_precise", "size_mb": 0.20002365112304688, "file_count": 4}, {"name": "backup_systematic", "size_mb": 0.5212230682373047, "file_count": 35}, {"name": "backup_ultimate", "size_mb": 1.4144172668457031, "file_count": 42}, {"name": "backup_ultimate_advanced", "size_mb": 0.44135570526123047, "file_count": 24}, {"name": "COMPLETE_SYSTEM_BACKUP_20250725_185838", "size_mb": 252.42686939239502, "file_count": 1466}], "backup_files": [{"path": "BACKUP_EXECUTION_PLAN.bat", "size_mb": 0.006836891174316406, "modified": "2025-07-25T19:39:10.125624"}, {"path": "backups\\backup_20250709_072751.db", "size_mb": 0.12890625, "modified": "2025-07-09T07:27:51.553499"}, {"path": "backup_critical_files\\backup_restore.py.backup", "size_mb": 0.027726173400878906, "modified": "2025-07-19T01:03:54.986119"}, {"path": "backup_deep\\backup_restore.py.backup_20250720_195918", "size_mb": 0.02793598175048828, "modified": "2025-07-20T02:16:45.874228"}, {"path": "backup_fixes\\backup_restore.py.backup_20250720_020022", "size_mb": 0.027846336364746094, "modified": "2025-07-19T06:55:45.096668"}, {"path": "backup_fixes\\backup_restore.py.backup_20250720_020023", "size_mb": 0.027846336364746094, "modified": "2025-07-19T06:55:45.096668"}, {"path": "backup_fixes\\backup_restore.py.backup_20250720_021645", "size_mb": 0.027891159057617188, "modified": "2025-07-20T02:00:23.061105"}, {"path": "backup_ultimate\\backup_restore.py.backup_20250720_024825", "size_mb": 0.02793598175048828, "modified": "2025-07-20T02:16:45.874228"}, {"path": "ui\\backup_restore.py", "size_mb": 0.02793598175048828, "modified": "2025-07-20T02:16:45.874228"}, {"path": "ui\\backup_restore.py.except_backup", "size_mb": 0.027726173400878906, "modified": "2025-07-19T01:24:27.058095"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_20250709_072751.db", "size_mb": 0.12890625, "modified": "2025-07-09T07:27:51.553499"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup", "size_mb": 0.027726173400878906, "modified": "2025-07-19T01:03:54.986119"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_020022", "size_mb": 0.027846336364746094, "modified": "2025-07-19T06:55:45.096668"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_020023", "size_mb": 0.027846336364746094, "modified": "2025-07-19T06:55:45.096668"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_021645", "size_mb": 0.027891159057617188, "modified": "2025-07-20T02:00:23.061105"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_024825", "size_mb": 0.02793598175048828, "modified": "2025-07-20T02:16:45.874228"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup_20250720_195918", "size_mb": 0.02793598175048828, "modified": "2025-07-20T02:16:45.874228"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.except_backup", "size_mb": 0.027726173400878906, "modified": "2025-07-19T01:24:27.058095"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\ui_components\\backup_restore.py", "size_mb": 0.02793598175048828, "modified": "2025-07-20T02:16:45.874228"}, {"path": "main.py.backup", "size_mb": 0.0030736923217773438, "modified": "2025-07-25T12:13:22.982556"}, {"path": "backup_critical_files\\backup_restore.py.backup", "size_mb": 0.027726173400878906, "modified": "2025-07-19T01:03:54.986119"}, {"path": "backup_critical_files\\categories_management_window.py.backup", "size_mb": 0.028049468994140625, "modified": "2025-07-19T01:03:54.978081"}, {"path": "backup_critical_files\\comprehensive_income_window.py.backup", "size_mb": 0.02626323699951172, "modified": "2025-07-19T01:03:54.967091"}, {"path": "backup_critical_files\\daily_journal_window.py.backup", "size_mb": 0.03763389587402344, "modified": "2025-07-19T01:03:54.969126"}, {"path": "backup_critical_files\\inventory_window.py.backup", "size_mb": 0.028348922729492188, "modified": "2025-07-19T01:03:54.975087"}, {"path": "backup_critical_files\\sales_analysis_window.py.backup", "size_mb": 0.05341053009033203, "modified": "2025-07-19T01:03:54.983091"}, {"path": "backup_critical_files\\stock_management_window.py.backup", "size_mb": 0.06171894073486328, "modified": "2025-07-19T01:03:54.973085"}, {"path": "backup_critical_files\\treasury_window.py.backup", "size_mb": 0.026228904724121094, "modified": "2025-07-19T01:03:54.980119"}, {"path": "database\\hybrid_database_manager.py.backup", "size_mb": 0.017877578735351562, "modified": "2025-07-25T12:13:22.998180"}, {"path": "services\\sales_manager.py.backup", "size_mb": 0.021500587463378906, "modified": "2025-07-19T01:21:21.073929"}, {"path": "ui\\main_window.py.backup", "size_mb": 0.09733200073242188, "modified": "2025-07-25T12:13:22.982556"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\backup_restore.py.backup", "size_mb": 0.027726173400878906, "modified": "2025-07-19T01:03:54.986119"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\categories_management_window.py.backup", "size_mb": 0.028049468994140625, "modified": "2025-07-19T01:03:54.978081"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\comprehensive_income_window.py.backup", "size_mb": 0.02626323699951172, "modified": "2025-07-19T01:03:54.967091"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\daily_journal_window.py.backup", "size_mb": 0.03763389587402344, "modified": "2025-07-19T01:03:54.969126"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\inventory_window.py.backup", "size_mb": 0.028348922729492188, "modified": "2025-07-19T01:03:54.975087"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\main.py.backup", "size_mb": 0.0030736923217773438, "modified": "2025-07-25T12:13:22.982556"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\main_window.py.backup", "size_mb": 0.09733200073242188, "modified": "2025-07-25T12:13:22.982556"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\sales_analysis_window.py.backup", "size_mb": 0.05341053009033203, "modified": "2025-07-19T01:03:54.983091"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\sales_manager.py.backup", "size_mb": 0.021500587463378906, "modified": "2025-07-19T01:21:21.073929"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\stock_management_window.py.backup", "size_mb": 0.06171894073486328, "modified": "2025-07-19T01:03:54.973085"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\treasury_window.py.backup", "size_mb": 0.026228904724121094, "modified": "2025-07-19T01:03:54.980119"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\hybrid_database_manager.py.backup", "size_mb": 0.017877578735351562, "modified": "2025-07-25T12:13:22.998180"}, {"path": "backups\\scheduled_backup_20250712_191621.db", "size_mb": 0.13671875, "modified": "2025-07-12T18:59:55.850935"}, {"path": "COMPLETE_SYSTEM_BACKUP_20250725_185838\\backup_files\\scheduled_backup_20250712_191621.db", "size_mb": 0.13671875, "modified": "2025-07-12T18:59:55.850935"}], "total_backup_size_mb": 0, "oldest_backup": null, "newest_backup": null}, "dependency_analysis": {}, "recommendations": [{"priority": "HIGH", "category": "backup_cleanup", "description": "حذف الن<PERSON>خ الاحتياطية القديمة - توفير 261.6 ميجابايت", "action": "delete_old_backups", "savings_mb": 261.5571994781494}, {"priority": "MEDIUM", "category": "cache_cleanup", "description": "حذف ملفا<PERSON> الكاش (950 ملف)", "action": "delete_cache_files", "savings_mb": 5}, {"priority": "LOW", "category": "test_cleanup", "description": "حذف ملفا<PERSON> الاختبار القديمة (326 ملف)", "action": "delete_old_tests", "savings_mb": 2}], "duplicate_analysis": {"ui\\employees_window.py": ["ui\\employees_window_fixed.py"], "COMPLETE_SYSTEM_BACKUP_20250725_185838\\ui_components\\employees_window.py": ["COMPLETE_SYSTEM_BACKUP_20250725_185838\\ui_components\\employees_window_fixed.py"]}, "documentation_analysis": {"essential_docs": ["README.md", "USER_GUIDE.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\README.md"], "redundant_docs": ["ADVANCED_LIBRARIES_SUMMARY.md", "ARABIC_FONTS_SUMMARY.md", "COMPREHENSIVE_SYSTEM_AUDIT_FINAL_REPORT.md", "COMPREHENSIVE_SYSTEM_AUDIT_REPORT.md", "CONTROL_PANEL_TEST_REPORT.md", "database_check_report_20250716_043252.md", "data_integrity_report_20250716_045527.md", "data_quality_report_20250716_045319.md", "dependency_status_report.md", "EXECUTIVE_SUMMARY_COMPREHENSIVE_AUDIT.md", "FINAL_COMPREHENSIVE_AUDIT_REPORT.md", "FINAL_COMPREHENSIVE_SYSTEMATIC_AUDIT_REPORT.md", "FINAL_DEEP_AUDIT_REPORT_2025.md", "FINAL_DEEP_COMPREHENSIVE_AUDIT_REPORT.md", "FINAL_DEPENDENCY_AUDIT_REPORT.md", "FINAL_REPORT.md", "FULLSCREEN_UPDATE.md", "GREEN_BAR_HEIGHT_UPDATE.md", "ICONS_POSITION_UPDATE.md", "ICONS_SWAP_UPDATE.md", "ICONS_UPDATE.md", "INVOICE_ICON_UPDATE.md", "LOGO_UPDATE.md", "RESPONSIVE_WINDOW_UPDATE.md", "SETTINGS_ICON_REMOVAL_REPORT.md", "SQLSERVER_INTEGRATION_SUMMARY.md", "UI_UPDATE_README.md", "database\\database_compatibility_report.md", "database\\database_status_report.md", "services\\sales_manager_report.md", "ui\\sales_window_enhancement_report.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\database_compatibility_report.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\database_files\\database_status_report.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\ADVANCED_LIBRARIES_SUMMARY.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\ARABIC_FONTS_SUMMARY.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\COMPREHENSIVE_SYSTEM_AUDIT_REPORT.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\CONTROL_PANEL_TEST_REPORT.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\database_check_report_20250716_043252.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\data_integrity_report_20250716_045527.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\data_quality_report_20250716_045319.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\dependency_status_report.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\FINAL_COMPREHENSIVE_AUDIT_REPORT.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\FINAL_COMPREHENSIVE_SYSTEMATIC_AUDIT_REPORT.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\FINAL_DEEP_AUDIT_REPORT_2025.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\FINAL_DEEP_COMPREHENSIVE_AUDIT_REPORT.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\FINAL_DEPENDENCY_AUDIT_REPORT.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\FINAL_REPORT.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\sales_manager_report.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\sales_window_enhancement_report.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\SETTINGS_ICON_REMOVAL_REPORT.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\SQLSERVER_INTEGRATION_SUMMARY.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\UI_UPDATE_README.md"], "outdated_docs": ["ADMIN_PANEL_ICON_INTEGRATION_GUIDE.md", "ADVANCED_CONTROL_PANEL_README.md", "CENTRAL_CONTROL_PANEL_README.md", "COMPARISON.md", "COMPLETE_GUIDE.md", "FINAL_INSTRUCTIONS.md", "FONT_SIZES_COMPARISON.md", "HOW_TO_ACCESS_SETTINGS.md", "HOW_TO_RUN.md", "HOW_TO_RUN_CONTROL_PANEL.md", "HR_Management_README.md", "HR_UI_Improvements_README.md", "INVOICES_MODULE_README.md", "README_ADMIN_PANEL.md", "README_INTERFACE.md", "REVERT_GREEN_BAR_HEIGHT.md", "SCHEDULER_GUIDE.md", "SETTINGS_ICON_ADDED_CONFIRMATION.md", "SETTINGS_WINDOW_README.md", "Warehouse_Management_README.md", "المعادلة_المحاسبية_الشاملة.md", "الهيكل_التنظيمي_للأرباح_والخسائر.md", "تقرير_الإصلاح_العميق_النهائي_2025.md", "تقرير_الفحص_الشامل_والإصلاح_النهائي_2025.md", "تقرير_الفحص_العميق_النهائي_الشامل_2025.md", "دليل_التشغيل.md", "ملخص_أدوات_التشغيل.md", "📋_دليل_التشغيل_النهائي_الشامل.md", "🚀_تشغيل_لوحة_التحكم.md", "assets\\README_IMAGES.md", "docs\\admin_panel_guide.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\assets\\README_IMAGES.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\admin_panel_guide.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\ADMIN_PANEL_ICON_INTEGRATION_GUIDE.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\ADVANCED_CONTROL_PANEL_README.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\CENTRAL_CONTROL_PANEL_README.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\HOW_TO_ACCESS_SETTINGS.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\HOW_TO_RUN.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\HOW_TO_RUN_CONTROL_PANEL.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\HR_Management_README.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\HR_UI_Improvements_README.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\INVOICES_MODULE_README.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\LICENSE.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\README_1.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\README_ADMIN_PANEL.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\SCHEDULER_GUIDE.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\SETTINGS_ICON_ADDED_CONFIRMATION.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\SETTINGS_WINDOW_README.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\Warehouse_Management_README.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\المعادلة_المحاسبية_الشاملة.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\الهيكل_التنظيمي_للأرباح_والخسائر.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\تقرير_الإصلاح_العميق_النهائي_2025.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\تقرير_الفحص_الشامل_والإصلاح_النهائي_2025.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\تقرير_الفحص_العميق_النهائي_الشامل_2025.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\دليل_التشغيل.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\ملخص_أدوات_التشغيل.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\📋_دليل_التشغيل_النهائي_الشامل.md", "COMPLETE_SYSTEM_BACKUP_20250725_185838\\documentation\\🚀_تشغيل_لوحة_التحكم.md"]}, "assets_analysis": {"used_assets": ["assets\\ceo_image.jpg", "assets\\fonts\\Amiri-Regular.ttf", "assets\\fonts\\Cairo-Regular.ttf", "assets\\fonts\\NotoNaskhArabic-Regular.ttf", "assets\\icons\\1.png", "assets\\icons\\10.png", "assets\\icons\\11.png", "assets\\icons\\12.png", "assets\\icons\\13.png", "assets\\icons\\14.png", "assets\\icons\\15.png", "assets\\icons\\16.png", "assets\\icons\\17.png", "assets\\icons\\18.png", "assets\\icons\\2.png", "assets\\icons\\21.png", "assets\\icons\\22.png", "assets\\icons\\23.png", "assets\\icons\\24.png", "assets\\icons\\25.png", "assets\\icons\\26.png", "assets\\icons\\27.png", "assets\\icons\\28.png", "assets\\icons\\3.png", "assets\\icons\\31.png", "assets\\icons\\32.png", "assets\\icons\\38.ico", "assets\\icons\\4.png", "assets\\icons\\40.png", "assets\\icons\\43.png", "assets\\icons\\44.jpg", "assets\\icons\\46.jpg", "assets\\icons\\47.jpg", "assets\\icons\\48.jpg", "assets\\icons\\5.png", "assets\\icons\\50.jpg", "assets\\icons\\53.ico", "assets\\icons\\54.ico", "assets\\icons\\6.png", "assets\\icons\\7.png", "assets\\icons\\8.png", "assets\\icons\\9.png", "assets\\icons\\employees.png", "assets\\logo\\222555.png"], "unused_assets": [{"path": "assets\\README_IMAGES.md", "size_mb": 0.0006513595581054688}, {"path": "assets\\employee_photos\\emp_20250719_142625.jpg", "size_mb": 0.22138023376464844}, {"path": "assets\\fonts\\Amiri.zip", "size_mb": 0.2078723907470703}, {"path": "assets\\fonts\\Cairo.zip", "size_mb": 0.2078714370727539}, {"path": "assets\\fonts\\NotoNaskhArabic.zip", "size_mb": 0.2078714370727539}, {"path": "assets\\icons\\19.png", "size_mb": 9.469651222229004}, {"path": "assets\\icons\\20.png", "size_mb": 9.469651222229004}, {"path": "assets\\icons\\29.png", "size_mb": 9.469651222229004}, {"path": "assets\\icons\\30.png", "size_mb": 3.4983835220336914}, {"path": "assets\\icons\\33.ico", "size_mb": 0.36232566833496094}, {"path": "assets\\icons\\34.ico", "size_mb": 0.015895843505859375}, {"path": "assets\\icons\\35.ico", "size_mb": 0.015783309936523438}, {"path": "assets\\icons\\36.ico", "size_mb": 0.02196216583251953}, {"path": "assets\\icons\\37.ico", "size_mb": 0.46143150329589844}, {"path": "assets\\icons\\39.ico", "size_mb": 0.05815315246582031}, {"path": "assets\\icons\\41.ico", "size_mb": 0.04708385467529297}, {"path": "assets\\icons\\42.ico", "size_mb": 0.36232566833496094}, {"path": "assets\\icons\\45.jpg", "size_mb": 0.38396453857421875}, {"path": "assets\\icons\\49.jpg", "size_mb": 0.1927499771118164}, {"path": "assets\\icons\\51.jpg", "size_mb": 0.25510406494140625}, {"path": "assets\\icons\\52.png", "size_mb": 0.05896949768066406}, {"path": "assets\\icons\\55.ico", "size_mb": 0.009415626525878906}, {"path": "assets\\icons\\56.ico", "size_mb": 0.025159835815429688}, {"path": "assets\\icons\\employees_alt.png", "size_mb": 0.0009393692016601562}, {"path": "assets\\icons\\employees_small.png", "size_mb": 0.005329132080078125}, {"path": "assets\\images\\56263.jpg", "size_mb": 0.08546924591064453}, {"path": "assets\\styles\\main_style.css", "size_mb": 0.0052433013916015625}], "total_assets_size_mb": 211.99018955230713}, "total_potential_savings_mb": 268.5571994781494}